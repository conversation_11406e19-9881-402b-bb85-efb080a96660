// ===== THÊM TÍNH NĂNG GỘP LỜI NÓI CHO BIÊN BẢN PHIÊN TÒA =====

// 1. <PERSON><PERSON><PERSON> tiên, thêm biến toàn cục để lưu trữ trạng thái gộp lời nói
let mergeSameSpeakerTranscript = false;

// 2. Thêm hàm này vào court-scripts.js sau hàm setupEditableFields()
function setupMergeSpeakerOption() {
    // Thêm tùy chọn gộp lời nói vào modal cài đặt
    const settingsModalBody = document.querySelector('#settingsModal .modal-body');
    if (settingsModalBody) {
        const mergeSpeakerOption = document.createElement('div');
        mergeSpeakerOption.className = 'form-group';
        mergeSpeakerOption.innerHTML = `
            <label class="form-label">
                <input type="checkbox" id="mergeSameSpeakerTranscript"> Gộp lời nói của cùng một người
            </label>
        `;
        settingsModalBody.appendChild(mergeSpeakerOption);
        
        // Thiết lập trạng thái ban đầu từ localStorage
        const savedValue = localStorage.getItem('mergeSameSpeakerTranscript');
        if (savedValue !== null) {
            mergeSameSpeakerTranscript = savedValue === 'true';
            document.getElementById('mergeSameSpeakerTranscript').checked = mergeSameSpeakerTranscript;
        }
        
        // Thêm sự kiện khi thay đổi checkbox
        document.getElementById('mergeSameSpeakerTranscript').addEventListener('change', function() {
            mergeSameSpeakerTranscript = this.checked;
            localStorage.setItem('mergeSameSpeakerTranscript', mergeSameSpeakerTranscript);
        });
    }
}

// 3. Sửa đổi hàm applySettings để lưu cài đặt gộp lời nói
function applySettings() {
    const fontFamily = document.getElementById('fontFamily');
    const fontSize = document.getElementById('fontSize');
    const lineHeight = document.getElementById('lineHeight');
    const autoSave = document.getElementById('autoSave');
    const mergeSpeakerOption = document.getElementById('mergeSameSpeakerTranscript');
    
    if (fontFamily && fontSize && lineHeight) {
        document.body.style.fontFamily = fontFamily.value;
        document.body.style.fontSize = fontSize.value;
        document.body.style.lineHeight = lineHeight.value;
        
        // Lưu cài đặt
        localStorage.setItem('fontFamily', fontFamily.value);
        localStorage.setItem('fontSize', fontSize.value);
        localStorage.setItem('lineHeight', lineHeight.value);
    }
    
    if (autoSave) {
        localStorage.setItem('autoSave', autoSave.checked ? 'true' : 'false');
    }
    
    // Lưu cài đặt gộp lời nói
    if (mergeSpeakerOption) {
        mergeSameSpeakerTranscript = mergeSpeakerOption.checked;
        localStorage.setItem('mergeSameSpeakerTranscript', mergeSameSpeakerTranscript);
    }
    
    closeModal('settingsModal');
    
    // Tạo lịch sử cho thay đổi cài đặt
    saveState('Thay đổi cài đặt');
    
    showAlert('success', 'Đã áp dụng cài đặt mới');
}

// 4. Sửa đổi hàm formatAllResultsHTML để hỗ trợ gộp lời nói
function formatAllResultsHTML() {
    let formattedHTML = '';
    
    // Nếu không có kết quả nào
    if (!speechResults || speechResults.length === 0) {
        return '<div style="text-align: center; padding: 20px; color: #666;">Chưa có kết quả nhận dạng giọng nói</div>';
    }
    
    // Xử lý chế độ gộp lời nói nếu được bật
    if (mergeSameSpeakerTranscript) {
        let currentSpeaker = null;
        let mergedText = '';
        let lastTimestamp = '';
        
        speechResults.forEach((result, index) => {
            // Thời gian hiện tại
            const timestamp = new Date().toLocaleTimeString();
            
            // Xử lý kết quả có thông tin người nói
            let speakerName = "Không xác định";
            let resultText = result;
            
            if (result.includes('[') && result.includes(']')) {
                const speakerEndPos = result.indexOf(']') + 1;
                const speakerInfo = result.substring(0, speakerEndPos);
                resultText = result.substring(speakerEndPos).trim();
                
                // Trích xuất tên người nói
                const speakerMatch = speakerInfo.match(/\[(.*?)\]/);
                if (speakerMatch) {
                    speakerName = speakerMatch[1];
                }
            }
            
            // Nếu là người nói đầu tiên hoặc khác người nói trước
            if (currentSpeaker === null || currentSpeaker !== speakerName) {
                // Nếu đã có nội dung trước đó, thêm vào kết quả
                if (currentSpeaker !== null) {
                    const speakerHTML = `
                        <div class="speaker-content">
                            <span class="speaker-name">${currentSpeaker}</span>
                            <span class="speaker-time">(${lastTimestamp})</span>:
                            <div class="speaker-text">${mergedText}</div>
                        </div>`;
                    formattedHTML += speakerHTML;
                }
                
                // Cập nhật người nói mới và bắt đầu văn bản mới
                currentSpeaker = speakerName;
                mergedText = resultText;
                lastTimestamp = timestamp;
            } else {
                // Cùng người nói, gộp văn bản
                mergedText += " " + resultText;
            }
            
            // Nếu là kết quả cuối cùng, thêm vào kết quả
            if (index === speechResults.length - 1) {
                const speakerHTML = `
                    <div class="speaker-content">
                        <span class="speaker-name">${currentSpeaker}</span>
                        <span class="speaker-time">(${lastTimestamp})</span>:
                        <div class="speaker-text">${mergedText}</div>
                    </div>`;
                formattedHTML += speakerHTML;
            }
        });
    } else {
        // Chế độ thông thường - không gộp
        let lastSpeaker = null;
        
        speechResults.forEach((result) => {
            // Thời gian hiện tại
            const timestamp = new Date().toLocaleTimeString();
            
            // Xử lý kết quả có thông tin người nói
            if (result.includes('[') && result.includes(']')) {
                const speakerEndPos = result.indexOf(']') + 1;
                const speakerInfo = result.substring(0, speakerEndPos);
                const resultText = result.substring(speakerEndPos).trim();
                
                // Trích xuất tên người nói
                const speakerMatch = speakerInfo.match(/\[(.*?)\]/);
                if (speakerMatch) {
                    const speakerName = speakerMatch[1];
                    
                    // Tạo HTML cho từng người nói
                    const speakerHTML = `
                        <div class="speaker-content">
                            <span class="speaker-name">${speakerName}</span>
                            <span class="speaker-time">(${timestamp})</span>:
                            <div class="speaker-text">${resultText}</div>
                        </div>`;
                    
                    formattedHTML += speakerHTML;
                    lastSpeaker = speakerName;
                } else {
                    // Không có tên người nói nhưng vẫn có dấu []
                    const speakerHTML = `
                        <div class="speaker-content">
                            <span class="speaker-name">Không xác định</span>
                            <span class="speaker-time">(${timestamp})</span>:
                            <div class="speaker-text">${resultText}</div>
                        </div>`;
                    
                    formattedHTML += speakerHTML;
                    lastSpeaker = null;
                }
            } else {
                // Không có thông tin người nói
                const speakerHTML = `
                    <div class="speaker-content">
                        <span class="speaker-name">Không xác định</span>
                        <span class="speaker-time">(${timestamp})</span>:
                        <div class="speaker-text">${result}</div>
                    </div>`;
                
                formattedHTML += speakerHTML;
                lastSpeaker = null;
            }
        });
    }
    
    return formattedHTML;
}

// 5. Sửa hàm insertAllText để đảm bảo sử dụng chức năng gộp lời nói
function insertAllText() {
    if (!activeElement) {
        showAlert('error', 'Vui lòng chọn một mục để chèn văn bản');
        return;
    }
    
    // Xóa placeholder nếu có
    if (activeElement.classList.contains('editable-placeholder')) {
        activeElement.classList.remove('editable-placeholder');
        activeElement.textContent = '';
    }
    
    // Lưu trạng thái trước khi thay đổi
    saveState('Trước khi chèn văn bản');
    
    // Lấy tất cả văn bản từ kết quả nhận dạng với định dạng HTML
    let formattedContent = formatAllResultsHTML();
    
    activeElement.innerHTML = formattedContent;
    activeElement.focus();
    
    // Lưu trạng thái sau khi thay đổi
    saveState('Chèn văn bản');
    
    showAlert('success', 'Đã chèn văn bản vào phần được chọn');
}

// 6. Cũng cần cập nhật hàm insertSelectedText để đảm bảo tính nhất quán
function insertSelectedText(sectionId) {
    const targetElement = document.getElementById(sectionId);
    if (!targetElement) return;
    
    // Xóa placeholder nếu có
    if (targetElement.classList.contains('editable-placeholder')) {
        targetElement.classList.remove('editable-placeholder');
        targetElement.textContent = '';
    }
    
    // Lưu trạng thái trước khi thay đổi
    saveState('Trước khi chèn văn bản vào ' + sectionId);
    
    // Lấy tất cả kết quả với định dạng HTML, áp dụng cài đặt gộp lời nói
    let formattedContent = formatAllResultsHTML();
    
    // Đưa vào mục đích
    targetElement.innerHTML = formattedContent;
    targetElement.focus();
    
    // Lưu trạng thái sau khi thay đổi
    saveState('Chèn văn bản vào ' + sectionId);
    
    // Hiển thị thông báo
    showAlert('success', 'Đã thêm tất cả kết quả nhận dạng vào phần được chọn');
}

// 7. Thêm vào hàm document.addEventListener('DOMContentLoaded',...) sau setupEditableFields();
document.addEventListener('DOMContentLoaded', function() {
    // Các hàm khởi tạo khác...
    setupEditableFields();
    setupMergeSpeakerOption(); // THÊM DÒNG NÀY
    // Các hàm khởi tạo khác...
});
