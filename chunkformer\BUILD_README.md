# Hướng dẫn đóng gói ChunkFormer thành file .exe

## Tổng quan
Hướng dẫn này sẽ giúp bạn đóng gói ứng dụng ChunkFormer thành file .exe sử dụng PyInstaller, với tùy chọn ẩn cửa sổ cmd.

## <PERSON><PERSON><PERSON> c<PERSON>u hệ thống
- Windows 10/11
- Python 3.8 trở lên
- Ít nhất 8GB RAM
- Ít nhất 10GB dung lượng trống

## Chuẩn bị

### 1. Cài đặt dependencies
```bash
# Cách 1: Sử dụng script tự động
python check_dependencies.py

# Cách 2: Cài đặt thủ công
pip install -r requirements.txt
```

### 2. Kiểm tra ứng dụng hoạt động
```bash
python decode.py
```
Đảm bảo ứng dụng chạy bình thường trước khi build.

## Cách build

### Phương pháp 1: Sử dụng script tự động (Khuyến nghị)
```bash
# Chạy script build nâng cao với menu
build_advanced.bat

# Hoặc chạy script build đơn giản
build_exe.bat
```

### Phương pháp 2: Sử dụng PyInstaller trực tiếp
```bash
# Build với ẩn console
pyinstaller chunkformer.spec --clean --noconfirm

# Build với hiện console (để debug)
pyinstaller chunkformer.spec --clean --noconfirm --console

# Build onefile (tất cả trong 1 file)
pyinstaller chunkformer.spec --clean --noconfirm --onefile
```

## Các tùy chọn build

### 1. Build thông thường (Khuyến nghị)
- **Ưu điểm**: Nhanh, dễ debug, kích thước nhỏ hơn
- **Nhược điểm**: Nhiều file
- **Kết quả**: Thư mục `dist/ChunkFormer/` chứa file exe và dependencies

### 2. Build onefile
- **Ưu điểm**: Chỉ 1 file exe duy nhất
- **Nhược điểm**: Rất chậm khi khởi động, kích thước lớn (có thể >1GB)
- **Kết quả**: File `dist/ChunkFormer.exe` duy nhất

### 3. Build với console
- **Ưu điểm**: Hiện thông báo lỗi, dễ debug
- **Nhược điểm**: Hiện cửa sổ cmd
- **Sử dụng**: Chỉ dùng khi cần debug

## Cấu trúc file sau khi build

```
dist/
└── ChunkFormer/
    ├── ChunkFormer.exe          # File chính
    ├── models/                  # Thư mục model AI
    ├── pretrained_models/       # Model pretrained
    ├── templates/               # Template HTML
    ├── static/                  # File CSS, JS
    ├── profile/                 # Profile người nói
    ├── locales/                 # File ngôn ngữ
    ├── resources/               # Tài nguyên khác
    ├── settings.json            # Cài đặt
    └── [các file dll và dependencies khác]
```

## Khắc phục sự cố

### Lỗi thiếu dependencies
```bash
# Chạy script kiểm tra
python check_dependencies.py

# Cài đặt package thiếu
pip install [tên_package]
```

### Lỗi "Module not found"
- Thêm module vào `hiddenimports` trong file `chunkformer.spec`
- Chạy lại build

### File exe quá lớn
- Sử dụng build thông thường thay vì onefile
- Loại bỏ các dependencies không cần thiết

### Lỗi khi chạy exe
- Kiểm tra antivirus có chặn không
- Chạy với quyền administrator
- Kiểm tra log trong thư mục temp

### Ứng dụng không khởi động
- Đảm bảo tất cả file model có trong thư mục dist
- Kiểm tra microphone permissions
- Chạy build với console để xem lỗi

## Tối ưu hóa

### Giảm kích thước file
1. Loại bỏ các module không cần thiết trong spec file
2. Sử dụng `--exclude-module` cho các package lớn không dùng
3. Nén bằng UPX (đã bật trong spec file)

### Tăng tốc độ khởi động
1. Sử dụng build thông thường thay vì onefile
2. Đặt ứng dụng trên SSD
3. Thêm exception cho antivirus

## Lưu ý quan trọng

1. **Kích thước**: File exe có thể rất lớn (500MB - 2GB) do chứa toàn bộ Python runtime và dependencies
2. **Thời gian build**: Quá trình build có thể mất 10-30 phút tùy vào cấu hình máy
3. **Antivirus**: Một số antivirus có thể báo false positive, thêm exception nếu cần
4. **Model files**: Đảm bảo tất cả file model được copy vào thư mục dist
5. **Permissions**: Ứng dụng cần quyền truy cập microphone

## Phân phối

Khi phân phối ứng dụng:
1. Nén toàn bộ thư mục `dist/ChunkFormer/`
2. Kèm theo hướng dẫn sử dụng
3. Yêu cầu người dùng:
   - Cài đặt Visual C++ Redistributable
   - Cho phép truy cập microphone
   - Thêm exception antivirus nếu cần

## Hỗ trợ

Nếu gặp vấn đề:
1. Chạy `python check_dependencies.py` để kiểm tra dependencies
2. Sử dụng build với console để xem lỗi chi tiết
3. Kiểm tra log file trong thư mục temp
4. Đảm bảo Python và pip được cập nhật mới nhất
