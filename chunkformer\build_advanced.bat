@echo off
chcp 65001 >nul
echo ========================================
echo    ChunkFormer Advanced Build Script
echo ========================================
echo.

:menu
echo Chọn loại build:
echo 1. Build thông thường (ẩn console)
echo 2. Build với console (để debug)
echo 3. Build onefile (tất cả trong 1 file exe)
echo 4. Kiểm tra dependencies
echo 5. Thoát
echo.
set /p choice="Nhập lựa chọn (1-5): "

if "%choice%"=="1" goto build_noconsole
if "%choice%"=="2" goto build_console
if "%choice%"=="3" goto build_onefile
if "%choice%"=="4" goto check_deps
if "%choice%"=="5" goto exit
echo Lựa chọn không hợp lệ!
goto menu

:check_deps
echo.
echo Đang kiểm tra dependencies...
python check_dependencies.py
if errorlevel 1 (
    echo.
    echo Vui lòng cài đặt các dependencies thiếu trước khi build.
    pause
    goto menu
)
echo.
pause
goto menu

:build_noconsole
echo.
echo Đang build ứng dụng (ẩn console)...
call :cleanup
pyinstaller chunkformer.spec --clean --noconfirm
goto check_result

:build_console
echo.
echo Đang build ứng dụng (hiện console)...
call :cleanup
REM Tạo spec file tạm thời với console=True
powershell -Command "(Get-Content chunkformer.spec) -replace 'console=False', 'console=True' | Set-Content chunkformer_console.spec"
pyinstaller chunkformer_console.spec --clean --noconfirm
del chunkformer_console.spec
goto check_result

:build_onefile
echo.
echo Đang build ứng dụng (onefile)...
call :cleanup
echo Cảnh báo: Build onefile có thể mất rất nhiều thời gian và tạo file rất lớn!
set /p confirm="Bạn có chắc chắn muốn tiếp tục? (y/n): "
if /i not "%confirm%"=="y" goto menu

REM Tạo spec file onefile
powershell -Command "(Get-Content chunkformer.spec) -replace 'exclude_binaries=True', 'exclude_binaries=False' -replace 'COLLECT\(', '#COLLECT(' | Set-Content chunkformer_onefile.spec"
pyinstaller chunkformer_onefile.spec --clean --noconfirm --onefile
del chunkformer_onefile.spec
goto check_result

:cleanup
echo Đang xóa các file build cũ...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "*.pyc" del /q "*.pyc"
exit /b

:check_result
if errorlevel 1 (
    echo.
    echo ❌ BUILD THẤT BẠI!
    echo.
    echo Các nguyên nhân có thể:
    echo - Thiếu dependencies
    echo - Lỗi trong code
    echo - Không đủ dung lượng ổ cứng
    echo - Antivirus chặn PyInstaller
    echo.
    echo Hãy chạy "python check_dependencies.py" để kiểm tra dependencies
    pause
    goto menu
)

echo.
echo ✅ BUILD THÀNH CÔNG!
echo.

REM Kiểm tra file exe
if exist "dist\ChunkFormer\ChunkFormer.exe" (
    echo 📁 File exe: dist\ChunkFormer\ChunkFormer.exe
    
    REM Hiển thị kích thước
    for %%I in ("dist\ChunkFormer\ChunkFormer.exe") do echo 📊 Kích thước: %%~zI bytes
    
    echo.
    echo 📋 Hướng dẫn sử dụng:
    echo - Chạy ChunkFormer.exe để khởi động ứng dụng
    echo - Ứng dụng web sẽ chạy tại http://localhost:5000
    echo - Đảm bảo microphone được kết nối
    echo.
    
    set /p open="Mở thư mục chứa file exe? (y/n): "
    if /i "%open%"=="y" explorer "dist\ChunkFormer"
    
    set /p test="Chạy thử ứng dụng? (y/n): "
    if /i "%test%"=="y" (
        echo Đang khởi động ứng dụng...
        start "" "dist\ChunkFormer\ChunkFormer.exe"
        echo Ứng dụng đã được khởi động. Kiểm tra http://localhost:5000
    )
) else (
    echo ❌ Không tìm thấy file exe!
)

echo.
pause
goto menu

:exit
echo Tạm biệt!
exit /b 0
