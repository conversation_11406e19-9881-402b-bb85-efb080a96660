# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Thêm thư mục hiện tại vào path
current_dir = os.path.dirname(os.path.abspath(SPEC))
sys.path.insert(0, current_dir)

# Thu thập tất cả các module con
hidden_imports = []
hidden_imports.extend(collect_submodules('torch'))
hidden_imports.extend(collect_submodules('torchaudio'))
hidden_imports.extend(collect_submodules('transformers'))
hidden_imports.extend(collect_submodules('numpy'))
hidden_imports.extend(collect_submodules('scipy'))
hidden_imports.extend(collect_submodules('sklearn'))
hidden_imports.extend(collect_submodules('librosa'))
hidden_imports.extend(collect_submodules('soundfile'))
hidden_imports.extend(collect_submodules('pyaudio'))
hidden_imports.extend(collect_submodules('webrtcvad'))
hidden_imports.extend(collect_submodules('RealtimeSTT'))
hidden_imports.extend(collect_submodules('flask'))
hidden_imports.extend(collect_submodules('openpyxl'))
hidden_imports.extend(collect_submodules('pandas'))

# Thêm các module cần thiết khác
additional_imports = [
    'model',
    'model.asr_model',
    'model.attention',
    'model.cmvn',
    'model.convolution',
    'model.ctc',
    'model.embedding',
    'model.encoder',
    'model.encoder_layer',
    'model.positionwise_feed_forward',
    'model.subsampling',
    'model.swish',
    'vietnamese_corrector',
    'export_routes',
    'realtime_chunkformer',
    'speechbrain.pretrained',
    'speechbrain.utils.data_utils',
    'speechbrain.utils.parameter_transfer',
    'speechbrain.dataio.dataio',
    'speechbrain.dataio.dataset',
    'speechbrain.dataio.batch',
    'speechbrain.dataio.sampler',
    'speechbrain.dataio.dataloader',
    'speechbrain.dataio.legacy',
    'speechbrain.lobes',
    'speechbrain.lobes.models',
    'speechbrain.lobes.models.ECAPA_TDNN',
    'speechbrain.nnet',
    'speechbrain.nnet.CNN',
    'speechbrain.nnet.linear',
    'speechbrain.nnet.normalization',
    'speechbrain.nnet.pooling',
    'speechbrain.nnet.activations',
    'speechbrain.nnet.containers',
    'speechbrain.processing',
    'speechbrain.processing.features',
    'speechbrain.processing.speech_augmentation',
    'huggingface_hub',
    'huggingface_hub.utils',
    'huggingface_hub.constants',
    'huggingface_hub.file_download',
    'huggingface_hub.hf_api',
    'huggingface_hub.repository',
    'huggingface_hub.snapshot_download',
    'requests',
    'urllib3',
    'certifi',
    'charset_normalizer',
    'idna',
    'tqdm',
    'packaging',
    'filelock',
    'typing_extensions',
    'pyyaml',
    'regex',
    'tokenizers',
    'safetensors',
    'hyperpyyaml',
    'joblib',
    'sentencepiece',
    'protobuf'
]

hidden_imports.extend(additional_imports)

# Thu thập dữ liệu từ các package
datas = []
datas.extend(collect_data_files('torch'))
datas.extend(collect_data_files('torchaudio'))
datas.extend(collect_data_files('transformers'))
datas.extend(collect_data_files('speechbrain'))
datas.extend(collect_data_files('huggingface_hub'))
datas.extend(collect_data_files('tokenizers'))
datas.extend(collect_data_files('sentencepiece'))

# Thêm các thư mục dữ liệu cần thiết
data_dirs = [
    ('models', 'models'),
    ('pretrained_models', 'pretrained_models'),
    ('templates', 'templates'),
    ('static', 'static'),
    ('profile', 'profile'),
    ('locales', 'locales'),
    ('resources', 'resources'),
    ('settings.json', '.'),
    ('audio_devices_info.json', '.'),
]

for src, dst in data_dirs:
    if os.path.exists(os.path.join(current_dir, src)):
        datas.append((os.path.join(current_dir, src), dst))

block_cipher = None

a = Analysis(
    ['decode.py'],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ChunkFormer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # Ẩn cửa sổ console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Có thể thêm icon nếu có
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='ChunkFormer',
)
