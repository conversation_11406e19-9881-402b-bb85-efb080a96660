<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nhận D<PERSON>ng <PERSON>ọ<PERSON></title>
    <!-- Font Google -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- CSS Tách riêng -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}?v=1.0.2">
</head>
<body>    <div class="app-header">
        <div class="app-logo">
            <img src="{{ url_for('static', filename='favicon.ico') }}" alt="Logo" style="width: 48px; height: 48px;">
            <div>
                <h1 class="app-title">AI Chuyển Giọng Nói Thành Văn Bản</h1>
                <p class="app-subtitle">Tự Động nhận dạng người nói TÒA ÁN NHÂN DÂN THÀNH PHỐ HỒ CHÍ MINH</p>
            </div>
        </div>

        <!-- Speaker Selection Indicator - Centered in header -->
        <div id="speakerSelectionIndicator" class="speaker-selection-indicator">
            <div class="indicator-header">
                <i class="fas fa-users"></i>
                <span class="indicator-title">Người đang được nói</span>
                <span id="selectedCount" class="selected-count">0</span>
            </div>
            <div id="selectedSpeakersList" class="selected-speakers-list">
                <span class="no-selection">Chưa người nói nào</span>
            </div>
        </div>

        <!-- Status indicator hidden as requested -->
        <!-- <div class="status-indicator listening">
            <div class="pulse"></div>
            Đang lắng nghe...
        </div> -->
    </div>

    <div id="alertsContainer"></div>

    <!-- New Speaker Mode Indicator -->
    <div id="newSpeakerIndicator" class="new-speaker-indicator">
        <i class="fas fa-user-plus"></i> Chế độ người mới đang bật
    </div>
    
    <div class="container">
        <!-- Cột bên trái: Người nói -->
        <div id="speakersTab" class="tab-content">            <div class="content-header">
                <h2 class="content-title">Hồ Sơ Người Nói</h2>
                <div class="content-actions">
                    <button class="button button-danger" onclick="deleteAllSpeakers()">
                        <i class="fas fa-trash-alt"></i> Xóa tất cả
                    </button>
                    <button class="button" onclick="fetchSpeakers()">
                        <i class="fas fa-sync-alt"></i> Làm mới
                    </button>
                </div>
            </div>
            
            <table id="speakersTable" class="speakers-table">
                <thead>
                    <tr>
                        <th>Tên người nói</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody id="speakersTableBody">
                    <!-- Dữ liệu sẽ được điền bởi JavaScript -->
                </tbody>
            </table>
            
            <div id="speakersEmptyState" class="empty-state" style="display: none;">
                <i class="fas fa-user-slash"></i>
                <div class="empty-state-text">Chưa có hồ sơ người nói nào</div>
                <div>Hệ thống sẽ tự động tạo hồ sơ khi nhận dạng giọng nói mới</div>
            </div>
        </div>
        
        <!-- Cột giữa: Kết quả -->
        <div class="tab-container">
            <!-- Tab navigation (để ẩn bằng CSS) -->
            <div class="tabs">
                <button class="tab-button active" data-tab="resultsTab">
                    <i class="fas fa-comment-alt"></i> Kết Quả
                </button>
                <button class="tab-button" data-tab="speakersTab">
                    <i class="fas fa-users"></i> Người Nói
                </button>
            </div>
            
            <!-- Tab Kết Quả -->
            <div id="resultsTab" class="tab-content active">
                <div class="content-header">
                    <h2 class="content-title">Kết Quả Nhận Dạng</h2>
                    <div class="content-actions">
                        <div class="dropdown">
                            <button id="exportBtn" class="button button-export dropdown-toggle">
                                <i class="fas fa-file-export"></i> Xuất file
                            </button>
                            <div class="dropdown-menu">
                                <a href="#" onclick="exportToTxt(); return false;" class="dropdown-item">
                                    <i class="fas fa-file-alt"></i> Xuất TXT
                                </a>
                                <a href="#" onclick="exportToExcel(); return false;" class="dropdown-item">
                                    <i class="fas fa-file-excel"></i> Xuất Excel
                                </a>
                            </div>
                        </div>
                        <button id="deleteAllBtn" class="button button-danger" onclick="deleteAllResults()">
                            <i class="fas fa-trash-alt"></i> Xóa tất cả
                        </button>                        <button id="toggleUpdateBtn" class="button" onclick="toggleAutoUpdate()">
                            <i class="fas fa-pause"></i> Tạm dừng nghe</button>
                        <a href="/court_transcript" class="button" style="background-color: #e63946;">
                            <i class="fas fa-gavel"></i> Tạo biên bản tòa án
                        </a>
                    </div>
                </div>
                <div id="resultsContainer" class="results-container">
                    <!-- Hướng dẫn sử dụng -->
                    <div class="usage-tips" style="background: #f8f9fa; border-left: 4px solid #007bff; padding: 10px; margin-bottom: 15px; font-size: 13px; color: #6c757d;">
                        <i class="fas fa-info-circle"></i> 
                        <strong>Hướng dẫn:</strong> Nhấp vào văn bản để chỉnh sửa. 
                        <strong>Enter:</strong> Kết thúc chỉnh sửa. 
                        <strong>Shift+Enter:</strong> Xóa phần văn bản sau con trỏ.
                    </div>
                    <div class="results-inner">
                        <!-- Kết quả sẽ được điền bởi JavaScript -->
                        <div class="empty-state">
                            <i class="fas fa-microphone-slash"></i>
                            <div class="empty-state-text">Chưa có kết quả nhận dạng nào</div>
                            <div>Hãy nói gì đó để bắt đầu nhận dạng giọng nói</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Cột phải: Settings -->
        <div class="settings-panel">
            <div class="settings-panel-header">
                <h2 class="settings-panel-title">Cài Đặt</h2>
            </div>
            
            <div class="settings-container">
                <!-- Audio Device Selection -->
                <div class="setting-group">
                    <h3><i class="fas fa-microphone"></i> Thiết bị âm thanh</h3>

                    <div class="setting-row">
                        <label class="setting-label" for="audioDeviceSelect">Chọn microphone:</label>
                        <div class="setting-input">
                            <select id="audioDeviceSelect" class="form-select" onchange="onAudioDeviceChange()">
                                <option value="">Đang tải thiết bị...</option>
                            </select>
                            <button type="button" onclick="refreshAudioDevices()" class="btn btn-secondary" title="Làm mới danh sách thiết bị" style="margin-left: 8px; padding: 6px 10px;">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Audio Level Monitor -->
                <div class="setting-group">
                    <h3><i class="fas fa-volume-up"></i> Mức độ âm thanh</h3>

                    <div class="audio-monitor">
                        <div class="audio-monitor-header">
                            <span class="audio-level-label">Mức hiện tại:</span>
                            <span id="audioLevelValue" class="audio-level-value">0</span>
                        </div>

                        <div class="audio-level-container">
                            <div class="audio-level-bar" onclick="handleAudioBarClick()" title="Click để bắt đầu audio monitoring">
                                <div id="audioLevelIndicator" class="audio-level-indicator"></div>
                                <div id="silenceThresholdLine" class="silence-threshold-line"></div>
                            </div>
                            <div class="audio-level-labels">
                                <span>0</span>
                                <span>25</span>
                                <span>50</span>
                                <span>75</span>
                                <span>100</span>
                            </div>
                        </div>

                        <div class="silence-threshold-control">
                            <label for="silenceThreshold" class="setting-label">
                                <i class="fas fa-volume-down"></i> Ngưỡng im lặng:
                                <span id="silenceThresholdValue">20</span>%
                            </label>
                            <input type="range" id="silenceThreshold" min="0" max="100" value="20"
                                   class="audio-threshold-slider" oninput="updateSilenceThreshold(this.value)">
                            <div class="threshold-help">
                                <small><i class="fas fa-info-circle"></i> Âm thanh dưới mức này sẽ được coi là im lặng</small>
                            </div>
                        </div>

                        <div class="audio-level-status">
                            <div class="audio-status-indicator" id="audioStatusIndicator"></div>
                            <span id="audioStatusText">Đang khởi tạo...</span>
                        </div>
                    </div>
                </div>

                <form id="settingsForm" method="POST" action="{{ url_for('settings') }}">                    <!-- Cài đặt Silero VAD -->
                    <div class="setting-group">
                        <h3><i class="fas fa-microphone-alt"></i> Cài đặt phát hiện giọng nói</h3>
                        
                        <div class="setting-row" style="display: none;">
                            <label class="setting-label" for="vad_threshold">Ngưỡng im lặng VAD (0-1):</label>
                            <div class="setting-input">
                                <input type="number" id="vad_threshold" name="vad_threshold" value="{{ settings.vad_threshold }}" step="0.01" min="0" max="1" class="form-input" required>
                            </div>
                        </div>
                        
                        <div class="setting-row">
                            <label class="setting-label" for="vad_min_speech_duration">Thời gian tối thiểu phát hiện giọng nói (giây):</label>
                            <div class="setting-input">
                                <input type="number" id="vad_min_speech_duration" name="vad_min_speech_duration" value="{{ settings.vad_min_speech_duration }}" step="0.01" min="0.01" class="form-input" required>
                            </div>
                        </div>
                        
                        <div class="setting-row">
                            <label class="setting-label" for="vad_min_silence_duration">Thời gian tối thiểu im lặng để dừng ghi âm (giây):</label>
                            <div class="setting-input">
                                <input type="number" id="vad_min_silence_duration" name="vad_min_silence_duration" value="{{ settings.vad_min_silence_duration }}" step="0.01" min="0.01" class="form-input" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cài đặt nhận dạng người nói -->
                    <div class="setting-group">
                        <h3><i class="fas fa-user-check"></i> Cài đặt nhận dạng người nói</h3>
                        
                        <div class="setting-row">
                            <label class="setting-label" for="similarity_threshold">Ngưỡng tương đồng giọng nói (0-1):</label>
                            <div class="setting-input">
                                <input type="number" id="similarity_threshold" name="similarity_threshold" value="{{ settings.similarity_threshold }}" step="0.01" min="0" max="1" class="form-input" required>
                            </div>
                        </div>
                        
                        <div class="setting-row" style="display: none;">
                            <label class="setting-label" for="auto_similarity_threshold">Ngưỡng tạo hồ sơ tự động (0-1):</label>
                            <div class="setting-input">
                                <input type="number" id="auto_similarity_threshold" name="auto_similarity_threshold" value="{{ settings.auto_similarity_threshold }}" step="0.01" min="0" max="1" class="form-input" required>
                            </div>
                        </div>
                        
                        <div class="setting-row" style="display: none;">
                            <label class="setting-label" for="embedding_merge_threshold">Ngưỡng gộp đặc trưng giọng nói (0-1):</label>
                            <div class="setting-input">
                                <input type="number" id="embedding_merge_threshold" name="embedding_merge_threshold" value="{{ settings.embedding_merge_threshold }}" step="0.01" min="0" max="1" class="form-input" required>
                            </div>
                        </div>
                        
                        <div class="setting-row" style="display: none;">
                            <label class="setting-label" for="auto_profile_creation">Tự động tạo hồ sơ người nói mới:</label>
                            <div class="setting-input">
                                <label class="toggle-container">
                                    <input type="checkbox" id="auto_profile_creation" name="auto_profile_creation" class="toggle-input" {% if settings.auto_profile_creation %}checked{% endif %}>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cài đặt hiển thị kết quả -->
                    <div class="setting-group">
                        <h3><i class="fas fa-list-alt"></i> Cài đặt hiển thị kết quả</h3>
                          <div class="setting-row">
                            <label class="setting-label" for="merge_same_speaker" title="Bật: Gộp các đoạn 'Người lạ' liên tiếp. Tắt: Hiển thị mỗi đoạn 'Người lạ' riêng biệt. Speakers khác luôn được gộp.">
                                Gộp lời nói "Người lạ":
                                <i class="fas fa-info-circle" style="margin-left: 5px; opacity: 0.7;"></i>
                            </label>
                            <div class="setting-input">
                                <label class="toggle-container">
                                    <input type="checkbox" id="merge_same_speaker" name="merge_same_speaker" class="toggle-input" {% if settings.merge_same_speaker %}checked{% endif %}>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="setting-row">
                            <label class="setting-label" for="merge_all_speakers" title="Bật: Gộp lời nói của tất cả người nói cùng tên thành một đoạn dài. Tắt: Hiển thị từng đoạn nói riêng biệt.">
                                Gộp lời nói tất cả người nói cùng tên:
                                <i class="fas fa-info-circle" style="margin-left: 5px; opacity: 0.7;"></i>
                            </label>
                            <div class="setting-input">
                                <label class="toggle-container">
                                    <input type="checkbox" id="merge_all_speakers" name="merge_all_speakers" class="toggle-input" {% if settings.merge_all_speakers %}checked{% endif %}>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="setting-row" style="display: none;">
                            <label class="setting-label" for="vietnamese_correction_enabled">Bật tính năng sửa văn bản tiếng Việt:</label>
                            <div class="setting-input">
                                <label class="toggle-container">
                                    <input type="checkbox" id="vietnamese_correction_enabled" name="vietnamese_correction_enabled" class="toggle-input" {% if settings.vietnamese_correction_enabled %}checked{% endif %}>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>


                    </div>
                    
                    <!-- Nút lưu cài đặt đã được ẩn vì có auto-save -->
                    <div class="auto-save-info" style="text-align: center; color: #6c757d; font-size: 13px; margin-top: 15px;">
                        <i class="fas fa-save"></i> Cài đặt được lưu tự động khi có thay đổi
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Nút toggle settings (ẩn trên desktop, hiển thị trên mobile) -->
    <button id="settingsToggle" class="settings-toggle">
        <i class="fas fa-cog"></i>
    </button>

    <div id="overlay" class="overlay"></div>
    
    <!-- Modal chỉnh sửa tên người nói -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Chỉnh sửa tên người nói</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <input type="hidden" id="speakerId">
                <label for="speakerName">Tên mới:</label>
                <input type="text" id="speakerName" placeholder="Nhập tên mới" class="form-input" required>
            </div>
            <div class="modal-footer">
                <button class="button button-danger" onclick="closeAllModals()">Hủy bỏ</button>
                <button class="button" onclick="updateSpeakerName()">Cập nhật</button>
            </div>
        </div>
    </div>
    
    <!-- JS Tách riêng -->
    <script src="{{ url_for('static', filename='js/scripts.js') }}?v=1.0.2"></script>
</body>
</html>