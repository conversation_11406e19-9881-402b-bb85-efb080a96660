#!/usr/bin/env python3
"""
Script kiểm tra và cài đặt dependencies cho ChunkFormer
"""

import subprocess
import sys
import importlib
import os

def check_package(package_name, import_name=None):
    """Kiểm tra xem package c<PERSON> được cài đặt không"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """Cài đặt package bằng pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("=" * 60)
    print("    ChunkFormer Dependencies Checker")
    print("=" * 60)
    print()
    
    # Danh sách các package cần thiết
    required_packages = [
        ("torch", "torch"),
        ("torchaudio", "torchaudio"),
        ("transformers", "transformers"),
        ("numpy", "numpy"),
        ("scipy", "scipy"),
        ("scikit-learn", "sklearn"),
        ("librosa", "librosa"),
        ("soundfile", "soundfile"),
        ("pyaudio", "pyaudio"),
        ("webrtcvad", "webrtcvad"),
        ("RealtimeSTT", "RealtimeSTT"),
        ("speechbrain", "speechbrain"),
        ("huggingface_hub", "huggingface_hub"),
        ("tokenizers", "tokenizers"),
        ("sentencepiece", "sentencepiece"),
        ("hyperpyyaml", "hyperpyyaml"),
        ("flask", "flask"),
        ("flask-cors", "flask_cors"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
        ("xlsxwriter", "xlsxwriter"),
        ("requests", "requests"),
        ("tqdm", "tqdm"),
        ("pyyaml", "yaml"),
        ("pyinstaller", "PyInstaller"),
    ]
    
    missing_packages = []
    
    print("Đang kiểm tra dependencies...")
    print()
    
    for package_name, import_name in required_packages:
        if check_package(package_name, import_name):
            print(f"✓ {package_name}")
        else:
            print(f"✗ {package_name} - THIẾU")
            missing_packages.append(package_name)
    
    print()
    
    if missing_packages:
        print(f"Tìm thấy {len(missing_packages)} package thiếu:")
        for package in missing_packages:
            print(f"  - {package}")
        print()
        
        choice = input("Bạn có muốn cài đặt các package thiếu không? (y/n): ").lower()
        if choice == 'y':
            print("\nĐang cài đặt các package thiếu...")
            failed_packages = []
            
            for package in missing_packages:
                print(f"Đang cài đặt {package}...")
                if install_package(package):
                    print(f"✓ Đã cài đặt {package}")
                else:
                    print(f"✗ Lỗi khi cài đặt {package}")
                    failed_packages.append(package)
            
            if failed_packages:
                print(f"\nCác package không thể cài đặt:")
                for package in failed_packages:
                    print(f"  - {package}")
                print("\nVui lòng cài đặt thủ công các package này.")
                return False
            else:
                print("\n✓ Tất cả dependencies đã được cài đặt thành công!")
        else:
            print("Vui lòng cài đặt các package thiếu trước khi build.")
            return False
    else:
        print("✓ Tất cả dependencies đã được cài đặt!")
    
    print()
    print("=" * 60)
    print("Kiểm tra hoàn tất! Bạn có thể tiến hành build ứng dụng.")
    print("=" * 60)
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
