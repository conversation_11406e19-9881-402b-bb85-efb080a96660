﻿# filepath: c:\Users\<USER>\Downloads\Programs\Compressed\app\WhisperLiveKit\chunkformer\realtime_chunkformer.py
"""
RealtimeSTT + ChunkFormer Integration
====================================

This module integrates RealtimeSTT for real-time Voice Activity Detection
with ChunkFormer for high-quality Speech-To-Text transcription.

Features:
- Real-time VAD using RealtimeSTT (WebRTC VAD, Silero VAD)
- High-quality transcription using ChunkFormer 
- Speaker identification and diarization
- Vietnamese text correction
- Real-time streaming via Flask SSE
"""

import os
import sys
import time
import threading
import queue
import json
import numpy as np
import torch
import tempfile
import wave
from datetime import datetime
from typing import Optional, Dict, Any, Callable
from collections import deque

# RealtimeSTT imports
from RealtimeSTT import AudioToTextRecorder

# ChunkFormer and existing components
from decode import (
    init, init_speaker_encoder, init_silero_vad, init_vietnamese_corrector,
    endless_decode, extract_speaker_embedding, find_matching_profile,
    detect_speaker_change_with_profiles, create_auto_profile,
    save_embedding_to_database, load_all_embeddings, load_settings,
    capitalize_first_letter, message_queue, web_results, web_results_lock,
    SIMILARITY_THRESHOLD, AUTO_PROFILE_SIMILARITY_THRESHOLD, EMBEDDING_MERGE_THRESHOLD,
    auto_profile_creation, merge_same_speaker, vietnamese_correction_enabled,
    speaker_embeddings, embeddings_database, current_speaker, unknown_speaker_count
)
from vietnamese_corrector import VietnameseCorrector


class RealtimeChunkformerSTT:
    """
    Integrated real-time speech-to-text system combining RealtimeSTT VAD 
    with ChunkFormer transcription.
    """
    
    def __init__(self, 
                 model_checkpoint: str = "models/chunkformer-large-vie",
                 vad_engine: str = "silero",  # "silero" or "webrtc"
                 sample_rate: int = 16000,
                 chunk_size: int = 64,
                 left_context_size: int = 128,
                 right_context_size: int = 0):
        """
        Initialize the RealtimeChunkformerSTT system.
        
        Args:
            model_checkpoint: Path to ChunkFormer model
            vad_engine: Type of VAD ("silero" or "webrtc")
            sample_rate: Audio sample rate
            chunk_size: ChunkFormer chunk size
            left_context_size: Left context for ChunkFormer
            right_context_size: Right context for ChunkFormer
        """
        self.model_checkpoint = model_checkpoint
        self.vad_engine = vad_engine
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.left_context_size = left_context_size
        self.right_context_size = right_context_size
        
        # Initialize components
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        self.char_dict = None
        self.recorder = None
        self.vietnamese_corrector = None
        
        # Audio processing
        self.audio_queue = queue.Queue()
        self.is_running = False
        self.processing_thread = None
        
        # Speaker tracking
        self.current_speaker_name = None
        self.current_embedding = None
        
        # Callbacks
        self.on_transcription_callback = None
        self.on_speaker_change_callback = None
        
        print(" Initializing RealtimeChunkformerSTT...")
        self._initialize_components()
        
    def _initialize_components(self):
        """Initialize all required components."""
        try:
            # Initialize ChunkFormer model
            print(" Loading ChunkFormer model...")
            self.model, self.char_dict = init(self.model_checkpoint, self.device)
            
            # Initialize speaker encoder
            print(" Initializing speaker encoder...")
            init_speaker_encoder(self.device)
            
            # Initialize Vietnamese corrector
            print(" Initializing Vietnamese corrector...")
            self.vietnamese_corrector = init_vietnamese_corrector()
            
            # Load existing speaker profiles and embeddings
            print(" Loading speaker profiles...")
            load_all_embeddings()
            
            # Load settings
            print(" Loading settings...")
            load_settings()
            
            print(" All components initialized successfully!")
            
        except Exception as e:
            print(f" Error initializing components: {str(e)}")
            raise
            
    def _initialize_realtime_recorder(self):
        """Initialize RealtimeSTT recorder with proper configuration."""
        try:
            print(f" Initializing RealtimeSTT with {self.vad_engine} VAD...")
            
            # RealtimeSTT configuration - using only valid parameters
            recorder_config = {
                'enable_realtime_transcription': False,  # We handle transcription separately
                'realtime_model_type': 'tiny',  # Lightweight for VAD only
                'realtime_processing_pause': 0.2,
                'silero_sensitivity': 0.4,
                'webrtc_sensitivity': 2,
                'post_speech_silence_duration': 0.3,
                'min_length_of_recording': 0.1,
                'min_gap_between_recordings': 0,
                'use_microphone': True,
                'level': 10,  # Reduced logging
                'spinner': False,  # Disable spinner for cleaner output
                'on_recording_start': self._on_recording_start,
                'on_recording_stop': self._on_recording_stop
            }
            
            # Choose VAD engine with fallback options
            if self.vad_engine == "silero":
                recorder_config.update({
                    'silero_use_onnx': True,
                    'silero_deactivity_detection': True,
                })
            elif self.vad_engine == "webrtc":
                recorder_config.update({
                    'use_webrtc': True,
                    'webrtc_sensitivity': 2
                })
            else:
                print(f" Unknown VAD engine '{self.vad_engine}', using silero")
                self.vad_engine = "silero"
                recorder_config.update({
                    'silero_use_onnx': True,
                    'silero_deactivity_detection': True,
                })
            
            # Initialize recorder with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.recorder = AudioToTextRecorder(**recorder_config)
                    print(f" RealtimeSTT recorder initialized with {self.vad_engine} VAD")
                    return
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f" Initialization attempt {attempt + 1} failed: {str(e)}")
                        print(f" Retrying in 2 seconds...")
                        time.sleep(2)
                    else:
                        raise e
            
        except Exception as e:
            print(f" Error initializing RealtimeSTT recorder: {str(e)}")
            print(" Troubleshooting tips:")
            print("   - Check microphone permissions")
            print("   - Verify audio device is connected")
            print("   - Try running as administrator")
            print("   - Check RealtimeSTT installation: pip install RealtimeSTT")
            raise
    
    def _on_recording_start(self):
        """Callback when recording starts"""
        print(" Recording started...")
    
    def _on_recording_stop(self):
        """Callback when recording stops"""
        print(" Recording stopped")
