from flask import Blueprint, request, jsonify, send_file
import tempfile
import os
import json
import csv
import io
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

# Tạo Blueprint cho phần xuất file Excel
export_routes = Blueprint('export', __name__)

@export_routes.route('/export_transcript_excel', methods=['POST'])
def export_transcript_excel():
    """
    API để tạo file Excel từ biên bản tòa án
    """
    try:
        # Lấy dữ liệu từ request
        request_data = request.get_json()
        
        if not request_data or 'data' not in request_data:
            return jsonify({'success': False, 'message': 'Không tìm thấy dữ liệu biên bản'}), 400
        
        data = request_data.get('data', [])
        
        # Tạo một workbook và một sheet mới
        wb = Workbook()
        ws = wb.active
        ws.title = "Biên Bản Phiên Tòa"
        
        # Định dạng tiêu đề
        title_font = Font(name='Times New Roman', size=14, bold=True)
        header_font = Font(name='Times New Roman', size=12, bold=True)
        normal_font = Font(name='Times New Roman', size=12)
        
        # Định dạng border
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Thêm dữ liệu vào Excel
        section_titles = ['I. NHỮNG NGƯỜI TIẾN HÀNH TỐ TỤNG', 'II. NHỮNG NGƯỜI THAM GIA TỐ TỤNG', 
                         'III. PHẦN THỦ TỤC BẮT ĐẦU PHIÊN TÒA', 'IV. PHẦN TRANH TỤNG TẠI PHIÊN TÒA']
        
        # Điền từng dòng vào Excel
        for row_index, row_data in enumerate(data, 1):
            col_index = 1
            
            # Xử lý trường hợp dòng trống
            if not row_data:
                ws.append([''])
                continue
            
            # Nếu là dòng tiêu đề
            if row_data[0] == 'BIÊN BẢN PHIÊN TÒA HÌNH SỰ SƠ THẨM':
                ws.merge_cells(f'A{row_index}:G{row_index}')
                cell = ws.cell(row=row_index, column=1, value=row_data[0])
                cell.font = title_font
                cell.alignment = Alignment(horizontal='center', vertical='center')
                continue
            
            # Nếu là tiêu đề phần
            if any(title in row_data[0] for title in section_titles) or row_data[0] in ['THƯ KÝ GHI BIÊN BẢN PHIÊN TÒA', 'THẨM PHÁN - CHỦ TỌA PHIÊN TÒA']:
                ws.merge_cells(f'A{row_index}:G{row_index}')
                cell = ws.cell(row=row_index, column=1, value=row_data[0])
                cell.font = header_font
                continue
            
            # Nếu là phần tranh tụng
            if row_data[0] in ['1. Kiểm sát viên công bố bản cáo trạng:', '2. Hỏi và trả lời tại phiên tòa:', 
                              '3. Tranh luận tại phiên tòa:', '4. Lời nói sau cùng của bị cáo:']:
                ws.merge_cells(f'A{row_index}:G{row_index}')
                cell = ws.cell(row=row_index, column=1, value=row_data[0])
                cell.font = Font(name='Times New Roman', size=12, bold=True, italic=True)
                continue
                
            # Xử lý dữ liệu người nói
            if len(row_data[0]) > 0 and ': ' in row_data[0]:
                speaker, text = row_data[0].split(': ', 1)
                cell = ws.cell(row=row_index, column=1, value=speaker + ':')
                cell.font = Font(name='Times New Roman', size=12, bold=True)
                
                # Ghi nội dung lời nói
                text_cell = ws.cell(row=row_index, column=2, value=text)
                text_cell.font = normal_font
                
                # Mở rộng ô chứa nội dung
                ws.merge_cells(f'B{row_index}:G{row_index}')
                continue
            
            # Dữ liệu bình thường
            for col_index, cell_value in enumerate(row_data, 1):
                cell = ws.cell(row=row_index, column=col_index, value=cell_value)
                cell.font = normal_font
                
                # Nếu là cột duy nhất, mở rộng nó
                if len(row_data) == 1:
                    ws.merge_cells(f'A{row_index}:G{row_index}')
        
        # Điều chỉnh độ rộng các cột
        for col in range(1, 8):
            ws.column_dimensions[get_column_letter(col)].width = 15
        
        # Cột đầu tiên rộng hơn cho tên người nói
        ws.column_dimensions['A'].width = 25
        
        # Cột thứ hai rộng hơn cho nội dung
        ws.column_dimensions['B'].width = 50
        
        # Lưu file tạm thời
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_file_path = temp_file.name
        temp_file.close()
        
        wb.save(temp_file_path)
        
        # Gửi file về cho client
        response = send_file(
            temp_file_path,
            as_attachment=True,
            download_name='bien_ban_phien_toa.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Thiết lập hàm callback để xóa file tạm sau khi gửi
        @response.call_on_close
        def cleanup():
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
        
        return response
    
    except Exception as e:
        # Xử lý lỗi
        print(f"Lỗi khi xuất Excel: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi xuất file Excel: {str(e)}'}), 500

# Để thêm routes vào ứng dụng Flask, sử dụng register_blueprint trong file app.py:
# app.register_blueprint(export_routes)
