
import torch
from transformers import pipeline
import logging
import re
from typing import List, Optional

class VietnameseCorrector:
    """
    Vietnamese text corrector using bmd1905/vietnamese-correction-v2 model
    """
    
    def __init__(self, model_name: str = "bmd1905/vietnamese-correction-v2", device: Optional[str] = None):
        """
        Initialize the Vietnamese corrector
        
        Args:
            model_name: The HuggingFace model name
            device: Devi<PERSON> to run the model on ('cuda' or 'cpu')
        """
        self.model_name = model_name
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize pipeline
        self.corrector = None
        self.max_length = 512
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Load model
        self._load_model()
    
    def _load_model(self):
        """Load the T5 model pipeline"""
        try:
            self.logger.info(f"Loading Vietnamese correction model: {self.model_name}")
            
            # Load the pipeline with device mapping
            device_id = 0 if self.device == 'cuda' and torch.cuda.is_available() else -1
            
            self.corrector = pipeline(
                "text2text-generation", 
                model=self.model_name,
                device=device_id,
                max_length=self.max_length
            )
            
            self.logger.info(f"Model loaded successfully on device: {self.device}")
            
        except Exception as e:
            self.logger.error(f"Error loading model: {str(e)}")
            raise
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text before correction
        
        Args:
            text: Input text to preprocess
            
        Returns:
            Preprocessed text
        """
        if not text or not text.strip():
            return ""
        
        # Remove extra whitespaces
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Handle common ASR artifacts
        text = text.replace('...', '.')
        text = text.replace('..', '.')
        
        return text
    
    def postprocess_text(self, text: str) -> str:
        """
        Postprocess corrected text
        
        Args:
            text: Corrected text to postprocess
            
        Returns:
            Postprocessed text
        """
        if not text:
            return ""
        
        # Remove extra whitespaces
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Capitalize first letter of sentences
        sentences = text.split('.')
        processed_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                sentence = sentence[0].upper() + sentence[1:] if len(sentence) > 1 else sentence.upper()
                processed_sentences.append(sentence)
        
        if processed_sentences:
            result = '. '.join(processed_sentences)
            if not result.endswith('.') and text.strip().endswith('.'):
                result += '.'
            return result
        
        return text
    
    def correct_text(self, text: str, max_length: Optional[int] = None) -> str:
        """
        Correct Vietnamese text using the T5 model
        
        Args:
            text: Input text to correct
            max_length: Maximum length for generation
            
        Returns:
            Corrected text
        """
        if not text or not text.strip():
            return ""
        
        try:
            # Preprocess text
            processed_text = self.preprocess_text(text)
            
            if not processed_text:
                return ""
            
            # Generate correction using pipeline
            max_gen_length = max_length or min(len(processed_text) + 50, self.max_length)
            
            result = self.corrector(
                processed_text,
                max_length=max_gen_length,
                do_sample=False,
                num_beams=4,
                early_stopping=True
            )
            
            # Extract corrected text
            corrected_text = result[0]['generated_text'] if result else processed_text
            
            # Postprocess
            corrected_text = self.postprocess_text(corrected_text)
            
            return corrected_text
            
        except Exception as e:
            self.logger.error(f"Error correcting text: {str(e)}")
            return text  # Return original text if correction fails
    
    def correct_batch(self, texts: List[str], batch_size: int = 8) -> List[str]:
        """
        Correct multiple texts in batches
        
        Args:
            texts: List of texts to correct
            batch_size: Size of each batch
            
        Returns:
            List of corrected texts
        """
        if not texts:
            return []
        
        corrected_texts = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_results = []
            
            for text in batch:
                corrected = self.correct_text(text)
                batch_results.append(corrected)
            
            corrected_texts.extend(batch_results)
            
            if i + batch_size < len(texts):
                self.logger.info(f"Processed {i + batch_size}/{len(texts)} texts")
        
        return corrected_texts
    
    def correct_segments(self, segments: List[dict]) -> List[dict]:
        """
        Correct text segments from ASR output
        
        Args:
            segments: List of segment dictionaries with 'text' field
            
        Returns:
            List of segments with corrected text
        """
        corrected_segments = []
        
        for segment in segments:
            if isinstance(segment, dict) and 'text' in segment:
                corrected_segment = segment.copy()
                corrected_segment['text'] = self.correct_text(segment['text'])
                corrected_segment['original_text'] = segment['text']
                corrected_segments.append(corrected_segment)
            else:
                corrected_segments.append(segment)
        
        return corrected_segments

def create_corrector(model_name: str = "bmd1905/vietnamese-correction-v2") -> VietnameseCorrector:
    """
    Factory function to create a Vietnamese corrector
    
    Args:
        model_name: The HuggingFace model name
        
    Returns:
        VietnameseCorrector instance
    """
    return VietnameseCorrector(model_name=model_name)

# Example usage
if __name__ == "__main__":
    # Initialize corrector
    corrector = VietnameseCorrector()
    
    # Test texts
    test_texts = [
        "côn viec kin doanh thì rất kho khan nên toi quyết dinh chuyển sang nghề khac",
        "toi dang là sinh diên nam hai ở truong đạ hoc khoa jọc tự nhiên",
        "Tôi đang học AI ở trun tam AI viet nam",
        "chinh phủ luôn cố găng het suc để naggna cao chat luong nền giáo duc",
    ]
    
    print("Vietnamese Text Correction Examples:")
    print("=" * 50)
    
    for text in test_texts:
        corrected = corrector.correct_text(text)
        print(f"Original:  {text}")
        print(f"Corrected: {corrected}")
        print("-" * 30)
