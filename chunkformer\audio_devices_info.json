{"scan_time": "2025-06-19T10:53:08.940997", "total_devices": 51, "virtual_devices_count": 13, "devices": [{"index": 0, "structVersion": 2, "name": "Microsoft Sound Mapper - Input", "hostApi": 0, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": true}, {"index": 1, "structVersion": 2, "name": "CABLE Output (VB-Audio Virtual ", "hostApi": 0, "maxInputChannels": 16, "maxOutputChannels": 0, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": true}, {"index": 2, "structVersion": 2, "name": "Line (2- Astro A50 Game)", "hostApi": 0, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": true}, {"index": 3, "structVersion": 2, "name": "Headset Microphone (2- Astro A5", "hostApi": 0, "maxInputChannels": 1, "maxOutputChannels": 0, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": true}, {"index": 4, "structVersion": 2, "name": "Microsoft Sound Mapper - Output", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 5, "structVersion": 2, "name": "CABLE Input (VB-Audio Virtual C", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 6, "structVersion": 2, "name": "Realtek Digital Output (Realtek", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 7, "structVersion": 2, "name": "Speakers (Steam Streaming Speak", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 8, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 8, "structVersion": 2, "name": "Headphones (2- Astro A50 Game)", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 9, "structVersion": 2, "name": "Speakers (Realtek(R) Audio)", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 6, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 10, "structVersion": 2, "name": "LG ULTRAGEAR (NVIDIA High Defin", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 11, "structVersion": 2, "name": "CABLE In 16ch (VB-Audio Virtual", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 12, "structVersion": 2, "name": "Headset Earphone (2- Astro A50 ", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 13, "structVersion": 2, "name": "Primary Sound Capture Driver", "hostApi": 1, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.12, "defaultLowOutputLatency": 0.0, "defaultHighInputLatency": 0.24, "defaultHighOutputLatency": 0.0, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": true}, {"index": 14, "structVersion": 2, "name": "CABLE Output (VB-Audio Virtual Cable)", "hostApi": 1, "maxInputChannels": 16, "maxOutputChannels": 0, "defaultLowInputLatency": 0.12, "defaultLowOutputLatency": 0.0, "defaultHighInputLatency": 0.24, "defaultHighOutputLatency": 0.0, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": true}, {"index": 15, "structVersion": 2, "name": "Line (2- Astro A50 Game)", "hostApi": 1, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.12, "defaultLowOutputLatency": 0.0, "defaultHighInputLatency": 0.24, "defaultHighOutputLatency": 0.0, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": true}, {"index": 16, "structVersion": 2, "name": "Headset Microphone (2- Astro A50 Voice)", "hostApi": 1, "maxInputChannels": 1, "maxOutputChannels": 0, "defaultLowInputLatency": 0.12, "defaultLowOutputLatency": 0.0, "defaultHighInputLatency": 0.24, "defaultHighOutputLatency": 0.0, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": true}, {"index": 17, "structVersion": 2, "name": "Primary Sound Driver", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 18, "structVersion": 2, "name": "CABLE Input (VB-Audio Virtual Cable)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 19, "structVersion": 2, "name": "Realtek Digital Output (Realtek(R) Audio)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 20, "structVersion": 2, "name": "Speakers (Steam Streaming Speakers)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 8, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 21, "structVersion": 2, "name": "Headphones (2- Astro A50 Game)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 22, "structVersion": 2, "name": "Speakers (Realtek(R) Audio)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 6, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 23, "structVersion": 2, "name": "LG ULTRAGEAR (NVIDIA High Definition Audio)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 24, "structVersion": 2, "name": "CABLE In 16ch (VB-Audio Virtual Cable)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 25, "structVersion": 2, "name": "Headset Earphone (2- Astro A50 Voice)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 26, "structVersion": 2, "name": "Realtek Digital Output (Realtek(R) Audio)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.003, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 27, "structVersion": 2, "name": "CABLE Input (VB-Audio Virtual Cable)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.0020181, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 28, "structVersion": 2, "name": "Speakers (Steam Streaming Speakers)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.003, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 29, "structVersion": 2, "name": "Headphones (2- Astro A50 Game)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.003, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 30, "structVersion": 2, "name": "Speakers (Realtek(R) Audio)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.003, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 31, "structVersion": 2, "name": "LG ULTRAGEAR (NVIDIA High Definition Audio)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.003, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 32, "structVersion": 2, "name": "CABLE In 16ch (VB-Audio Virtual Cable)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.002, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 48000.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 33, "structVersion": 2, "name": "Headset Earphone (2- Astro A50 Voice)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.003, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 34, "structVersion": 2, "name": "Line (2- Astro A50 Game)", "hostApi": 2, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.003, "defaultLowOutputLatency": 0.0, "defaultHighInputLatency": 0.01, "defaultHighOutputLatency": 0.0, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": true}, {"index": 35, "structVersion": 2, "name": "Headset Microphone (2- Astro A50 Voice)", "hostApi": 2, "maxInputChannels": 1, "maxOutputChannels": 0, "defaultLowInputLatency": 0.003, "defaultLowOutputLatency": 0.0, "defaultHighInputLatency": 0.01, "defaultHighOutputLatency": 0.0, "defaultSampleRate": 16000.0, "is_virtual": false, "status": "available", "can_record": true}, {"index": 36, "structVersion": 2, "name": "CABLE Output (VB-Audio Virtual Cable)", "hostApi": 2, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.003, "defaultLowOutputLatency": 0.0, "defaultHighInputLatency": 0.01, "defaultHighOutputLatency": 0.0, "defaultSampleRate": 48000.0, "is_virtual": true, "status": "available", "can_record": true}, {"index": 37, "structVersion": 2, "name": "CABLE Output (VB-Audio Point)", "hostApi": 3, "maxInputChannels": 16, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 38, "structVersion": 2, "name": "Output (VB-Audio Point)", "hostApi": 3, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 39, "structVersion": 2, "name": "Input (VB-Audio Point)", "hostApi": 3, "maxInputChannels": 16, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 40, "structVersion": 2, "name": "Stereo Mix (Realtek HD Audio Stereo input)", "hostApi": 3, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 41, "structVersion": 2, "name": "SPDIF Out (Realtek HDA SPDIF Out)", "hostApi": 3, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 42, "structVersion": 2, "name": "Speakers (Realtek HD Audio output)", "hostApi": 3, "maxInputChannels": 0, "maxOutputChannels": 6, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 43, "structVersion": 2, "name": "Microphone (Realtek HD Audio Mic input)", "hostApi": 3, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 44, "structVersion": 2, "name": "Output (NVIDIA High Definition Audio)", "hostApi": 3, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 45, "structVersion": 2, "name": "Headset Microphone (Astro A50 Voice)", "hostApi": 3, "maxInputChannels": 1, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.08533333333333333, "defaultHighOutputLatency": 0.08533333333333333, "defaultSampleRate": 16000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 46, "structVersion": 2, "name": "Headset Earphone (Astro A50 Voice)", "hostApi": 3, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.08533333333333333, "defaultHighOutputLatency": 0.08533333333333333, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 47, "structVersion": 2, "name": "Input (Steam Streaming Speakers Wave)", "hostApi": 3, "maxInputChannels": 8, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.08533333333333333, "defaultHighOutputLatency": 0.08533333333333333, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 48, "structVersion": 2, "name": "Speakers (Steam Streaming Speakers Wave)", "hostApi": 3, "maxInputChannels": 0, "maxOutputChannels": 8, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.08533333333333333, "defaultHighOutputLatency": 0.08533333333333333, "defaultSampleRate": 44100.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 49, "structVersion": 2, "name": "Headphones (Astro A50 Game)", "hostApi": 3, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.08533333333333333, "defaultHighOutputLatency": 0.08533333333333333, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}, {"index": 50, "structVersion": 2, "name": "Line (Astro A50 Game)", "hostApi": 3, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.08533333333333333, "defaultHighOutputLatency": 0.08533333333333333, "defaultSampleRate": 48000.0, "is_virtual": false, "status": "available", "can_record": false}], "virtual_devices": [{"index": 1, "structVersion": 2, "name": "CABLE Output (VB-Audio Virtual ", "hostApi": 0, "maxInputChannels": 16, "maxOutputChannels": 0, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": true}, {"index": 5, "structVersion": 2, "name": "CABLE Input (VB-Audio Virtual C", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 11, "structVersion": 2, "name": "CABLE In 16ch (VB-Audio Virtual", "hostApi": 0, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.09, "defaultLowOutputLatency": 0.09, "defaultHighInputLatency": 0.18, "defaultHighOutputLatency": 0.18, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 14, "structVersion": 2, "name": "CABLE Output (VB-Audio Virtual Cable)", "hostApi": 1, "maxInputChannels": 16, "maxOutputChannels": 0, "defaultLowInputLatency": 0.12, "defaultLowOutputLatency": 0.0, "defaultHighInputLatency": 0.24, "defaultHighOutputLatency": 0.0, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": true}, {"index": 18, "structVersion": 2, "name": "CABLE Input (VB-Audio Virtual Cable)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 24, "structVersion": 2, "name": "CABLE In 16ch (VB-Audio Virtual Cable)", "hostApi": 1, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.12, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.24, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 27, "structVersion": 2, "name": "CABLE Input (VB-Audio Virtual Cable)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.0020181, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 32, "structVersion": 2, "name": "CABLE In 16ch (VB-Audio Virtual Cable)", "hostApi": 2, "maxInputChannels": 0, "maxOutputChannels": 2, "defaultLowInputLatency": 0.0, "defaultLowOutputLatency": 0.002, "defaultHighInputLatency": 0.0, "defaultHighOutputLatency": 0.01, "defaultSampleRate": 48000.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 36, "structVersion": 2, "name": "CABLE Output (VB-Audio Virtual Cable)", "hostApi": 2, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.003, "defaultLowOutputLatency": 0.0, "defaultHighInputLatency": 0.01, "defaultHighOutputLatency": 0.0, "defaultSampleRate": 48000.0, "is_virtual": true, "status": "available", "can_record": true}, {"index": 37, "structVersion": 2, "name": "CABLE Output (VB-Audio Point)", "hostApi": 3, "maxInputChannels": 16, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 38, "structVersion": 2, "name": "Output (VB-Audio Point)", "hostApi": 3, "maxInputChannels": 0, "maxOutputChannels": 16, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 39, "structVersion": 2, "name": "Input (VB-Audio Point)", "hostApi": 3, "maxInputChannels": 16, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}, {"index": 40, "structVersion": 2, "name": "Stereo Mix (Realtek HD Audio Stereo input)", "hostApi": 3, "maxInputChannels": 2, "maxOutputChannels": 0, "defaultLowInputLatency": 0.01, "defaultLowOutputLatency": 0.01, "defaultHighInputLatency": 0.04, "defaultHighOutputLatency": 0.04, "defaultSampleRate": 44100.0, "is_virtual": true, "status": "available", "can_record": false}], "recommended": [{"index": 1, "name": "CABLE Output (VB-Audio Virtual ", "type": "virtual", "priority": "high", "reason": "<PERSON><PERSON><PERSON><PERSON> bị <PERSON> - có thể nhận audio từ ứng dụng"}, {"index": 14, "name": "CABLE Output (VB-Audio Virtual Cable)", "type": "virtual", "priority": "high", "reason": "<PERSON><PERSON><PERSON><PERSON> bị <PERSON> - có thể nhận audio từ ứng dụng"}, {"index": 36, "name": "CABLE Output (VB-Audio Virtual Cable)", "type": "virtual", "priority": "high", "reason": "<PERSON><PERSON><PERSON><PERSON> bị <PERSON> - có thể nhận audio từ ứng dụng"}, {"index": 0, "name": "Microsoft Sound Mapper - Input", "type": "physical", "priority": "medium", "reason": "<PERSON><PERSON><PERSON><PERSON> bị vật lý - microphone thông thường"}, {"index": 2, "name": "Line (2- Astro A50 Game)", "type": "physical", "priority": "medium", "reason": "<PERSON><PERSON><PERSON><PERSON> bị vật lý - microphone thông thường"}, {"index": 3, "name": "Headset Microphone (2- Astro A5", "type": "physical", "priority": "medium", "reason": "<PERSON><PERSON><PERSON><PERSON> bị vật lý - microphone thông thường"}, {"index": 13, "name": "Primary Sound Capture Driver", "type": "physical", "priority": "medium", "reason": "<PERSON><PERSON><PERSON><PERSON> bị vật lý - microphone thông thường"}, {"index": 15, "name": "Line (2- Astro A50 Game)", "type": "physical", "priority": "medium", "reason": "<PERSON><PERSON><PERSON><PERSON> bị vật lý - microphone thông thường"}, {"index": 16, "name": "Headset Microphone (2- Astro A50 Voice)", "type": "physical", "priority": "medium", "reason": "<PERSON><PERSON><PERSON><PERSON> bị vật lý - microphone thông thường"}, {"index": 34, "name": "Line (2- Astro A50 Game)", "type": "physical", "priority": "medium", "reason": "<PERSON><PERSON><PERSON><PERSON> bị vật lý - microphone thông thường"}, {"index": 35, "name": "Headset Microphone (2- Astro A50 Voice)", "type": "physical", "priority": "medium", "reason": "<PERSON><PERSON><PERSON><PERSON> bị vật lý - microphone thông thường"}]}