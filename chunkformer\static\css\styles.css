/* 
 * styles.css - CSS chung cho ứng dụng nhận dạng giọng nói
 * Phiên bản: 1.0.0
 * Màu sắc: Đỏ-Trắng
 * Bố cục: Ta<PERSON> nó<PERSON> bê<PERSON>, <PERSON><PERSON> qu<PERSON> ở <PERSON>, <PERSON><PERSON><PERSON> bên phải
 */

:root {
    --primary-color: #e63946; /* Đỏ */
    --primary-light: #ffffff; /* Tr<PERSON><PERSON> (thay thế màu hồng nh<PERSON>) */
    --primary-dark: #c1121f; /* Đỏ đậm */
    --secondary-color: #a01a58;
    --accent-color: #d62828;
    --danger-color: #9d0208;
    --success-color: #590d22;
    --warning-color: #ff9f1c;
    --dark-color: #212529;
    --light-color: #fff; /* M<PERSON>u nền trắng */
    --gray-color: #6c757d;
    --gray-light: #e9ecef;
    --gray-dark: #343a40;
    --border-radius: 4px;
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --transition: all 0.2s ease;
    --sidebar-width: 350px; /* Chiều rộng sidebar */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
    line-height: 1.5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.app-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 40px;
    padding: 15px 20px;
    border-bottom: 1px solid var(--gray-light);
    background-color: white;
    box-shadow: var(--box-shadow);
}

.app-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.app-logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.app-title {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
}

.app-subtitle {
    font-size: 1rem;
    font-weight: normal;
    color: var(--gray-color);
    margin-bottom: 5px;
}

.status-indicator {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    background-color: var(--success-color);
    color: white;
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-indicator.listening {
    background-color: var(--primary-color);
}

.status-indicator i {
    margin-right: 8px;
    font-size: 1rem;
}

/* Đơn giản hóa hiệu ứng pulse */
.pulse {
    height: 10px;
    width: 10px;
    background-color: white;
    border-radius: 50%;
    margin-right: 8px;
    opacity: 0.8;
}

.listening .pulse {
    animation: simple-pulse 2s infinite;
}

@keyframes simple-pulse {
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.8;
    }
}

/* Bố cục mới: 3 cột */
.container {
    flex: 1;
    display: flex;
    align-items: stretch;
}

/* Tab navigation ẩn */
.tabs {
    display: none;
}

/* Cột bên trái: Người nói */
#speakersTab {
    width: var(--sidebar-width);
    display: block !important; /* Luôn hiển thị */
    background-color: white;
    padding: 20px;
    box-shadow: var(--box-shadow);
    overflow-y: auto;
    max-height: calc(100vh - 80px);
    border-right: 1px solid var(--gray-light);
}

/* Cột giữa: Kết quả */
.tab-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

#resultsTab {
    display: block !important; /* Luôn hiển thị */
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    min-height: 300px;
}

/* Cột phải: Settings - đã được điều chỉnh để hiển thị cố định */
.settings-panel {
    width: var(--sidebar-width);
    position: relative;
    right: 0;
    height: calc(100vh - 80px);
    background-color: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow-y: auto;
    padding: 15px;
    border-left: 1px solid var(--gray-light);
}

/* Ẩn nút toggle settings */
.settings-toggle {
    display: none;
}

/* Ẩn overlay */
.overlay {
    display: none;
}

#alertsContainer {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1500;
    width: 80%;
    max-width: 500px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.content-title {
    font-size: 1.4rem;
    font-weight: 500;
    color: var(--dark-color);
    margin: 0;
}

.content-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.results-container {
    overflow-y: auto;
    padding-right: 10px;
    border: 1px solid var(--gray-light);
    border-radius: var(--border-radius);
    position: relative;
    min-height: 300px;
    max-height: calc(100vh - 280px);
    height: calc(100vh - 200px);
}

.results-inner {
    padding: 15px;
}

.result-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid var(--primary-color);
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.result-card:hover {
    background-color: rgba(230, 57, 70, 0.05);
}

/* Đã bỏ các hiệu ứng của .result-card.selected */
.result-card.selected {
    /* Thêm border mỏng để phân biệt trạng thái selected */
    border: 1px solid var(--primary-color);
    border-left: 4px solid var(--primary-color);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.result-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.speaker-dropdown {
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--gray-light);
    background-color: var(--light-color);
    color: var(--primary-color);
    font-size: 0.9rem;
    min-width: 130px;
    cursor: pointer;
    appearance: menulist;
    -webkit-appearance: menulist;
}

.speaker-dropdown:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: -1px;
}

.speaker-tag {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    margin-right: 10px;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.9rem;
}

.delete-result-btn {
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: var(--gray-color);
    font-size: 1rem;
    padding: 6px 8px;
    border-radius: 4px;
    transition: var(--transition);
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.delete-result-btn:hover {
    background-color: var(--danger-color) !important;
    color: white !important;
    transform: scale(1.05);
}

.delete-result-btn:active {
    transform: scale(0.95);
}

.delete-result-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    background-color: var(--dark-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    margin-top: 35px;
    margin-left: -20px;
}

/* Nút sửa văn bản */
.correct-text-btn {
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: var(--gray-color);
    font-size: 1rem;
    padding: 6px 8px;
    border-radius: 4px;
    transition: var(--transition);
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
}

.correct-text-btn:hover {
    color: var(--primary-color);
    background-color: rgba(230, 57, 70, 0.1);
    transform: scale(1.1);
}

.correct-text-btn:active {
    transform: scale(0.95);
}

.correct-text-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.correct-text-btn:disabled:hover {
    color: var(--gray-color);
    background-color: transparent;
    transform: none;
}

/* Nút gộp văn bản */
.merge-text-btn {
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: var(--gray-color);
    font-size: 1rem;
    padding: 6px 8px;
    border-radius: 4px;
    transition: var(--transition);
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
}

.merge-text-btn:hover {
    color: #28a745; /* Màu xanh lá cho nút gộp */
    background-color: rgba(40, 167, 69, 0.1);
    transform: scale(1.1);
}

.merge-text-btn:active {
    transform: scale(0.95);
}

.merge-text-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.merge-text-btn:disabled:hover {
    color: var(--gray-color);
    background-color: transparent;
    transform: none;
}

/* Tooltip cho nút gộp văn bản */
.merge-text-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    background-color: var(--dark-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    margin-top: 35px;
    margin-left: -30px;
}

.result-text {
    font-size: 1rem;
    line-height: 1.6;
    padding: 5px;
    border-radius: 4px;
}

.result-text[contenteditable="true"]:focus {
    outline: 2px solid var(--primary-color);
    padding: 5px;
    background-color: white;
}

.result-text[contenteditable="true"]:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

.timestamp {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 0.8rem;
    color: var(--gray-color);
}

/* Settings Section */
.settings-container {
    max-width: 100%;
}

.setting-group {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

.setting-group h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-light);
}

.setting-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.setting-label {
    flex: 1;
    font-weight: 500;
}

.setting-input {
    flex: 1;
}

.form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--gray-light);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.form-input:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--gray-light);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background-color: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.form-select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.btn {
    padding: 8px 12px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-secondary {
    background-color: var(--gray-medium);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--gray-dark);
}

.toggle-container {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-light);
    transition: var(--transition);
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

.toggle-input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

.toggle-input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.button {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    text-align: center;
}

.button:hover {
    background-color: var(--primary-dark);
}

.button-danger {
    background-color: var(--danger-color);
}

.button-danger:hover {
    background-color: #7f0000;
}

.button-success {
    background-color: var(--success-color);
}

.button-success:hover {
    background-color: #4a0d1c;
}

.button-info {
    background-color: var(--secondary-color);
}

.button-info:hover {
    background-color: #801348;
}

.button-export {
    background-color: #ae2012;
}

.button-export:hover {
    background-color: #9b1b0c;
}

.button i {
    margin-right: 5px;
}

.button-primary {
    background-color: var(--primary-color);
    color: white;
}

.button-primary:hover {
    background-color: var(--primary-dark);
}



/* Speakers Section */
.speakers-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.speakers-table th,
.speakers-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--gray-light);
}

.speakers-table th {
    font-weight: 500;
    background-color: var(--gray-light);
    color: var(--dark-color);
}

.speakers-table tr:hover {
    background-color: var(--gray-light);
}

.speakers-table td:last-child {
    text-align: right;
}

.speaker-actions {
    display: flex;
    gap: 5px;
    justify-content: flex-end;
}

.action-button {
    padding: 6px 10px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    cursor: pointer;
    color: white;
}

.edit-button {
    background-color: var(--success-color);
}

.edit-button:hover {
    background-color: #4a0d1c;
}

.play-button {
    background-color: var(--secondary-color);
}

.play-button:hover {
    background-color: #801348;
}

.delete-button {
    background-color: var(--danger-color);
}

.delete-button:hover {
    background-color: #7f0000;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: white;
    margin: 10% auto;
    padding: 25px;
    width: 90%;
    max-width: 500px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-title {
    font-size: 1.4rem;
    font-weight: 500;
    margin: 0;
}

.modal-close {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-color);
}

.modal-close:hover {
    color: var(--danger-color);
}

.modal-body {
    margin-bottom: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Audio player */
.audio-player {
    width: 100%;
    margin: 15px 0;
}

.transcript {
    background-color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    margin: 15px 0;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* Alert messages */
.alert {
    padding: 12px 15px;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}

.alert i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
    border-left: 4px solid var(--success-color);
}

.alert-error {
    background-color: #f8d7da;
    color: #842029;
    border-left: 4px solid var(--danger-color);
}

.alert-info {
    background-color: #cff4fc;
    color: #055160;
    border-left: 4px solid #0dcaf0;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray-color);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
}

.empty-state-text {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-light);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-dark);
}

/* Dropdown styling */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    cursor: pointer;
}

.dropdown-menu {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 160px;
    box-shadow: var(--box-shadow);
    z-index: 1;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.dropdown-item {
    color: var(--dark-color);
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}

.dropdown-item:hover {
    background-color: var(--gray-light);
}

.dropdown:hover .dropdown-menu {
    display: block;
}

/* Loading indicator for delete operations */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
    color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border: 1px solid var(--gray-light);
    display: flex;
    align-items: center;
    gap: 5px;
}

.loading-indicator span {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

/* Improved delete button hover effect */
.delete-result-btn {
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.delete-result-btn:hover {
    background-color: var(--danger-color) !important;
    color: white !important;
    transform: scale(1.05);
}

.delete-result-btn:active {
    transform: scale(0.95);
}

/* Result card improvements for better delete UX */
.result-card {
    transition: opacity 0.3s ease, transform 0.2s ease;
}

.result-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.result-card.deleting {
    opacity: 0.5;
    pointer-events: none;
    transform: scale(0.98);
}

/* Loading state for buttons */
.button.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: var(--primary-color);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Optimized alert styles */
.alert {
    opacity: 1;
    transition: opacity 0.3s ease;
    max-width: 400px;
    word-wrap: break-word;
}

.alert.hiding {
    opacity: 0;
}

/* Performance optimization for large lists */
.results-inner {
    will-change: transform;
}

.result-card {
    will-change: opacity, transform;
}

/* Speaker selection info */
.speaker-selection-info {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: var(--gray-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-color);
}

.selected-count {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 14px;
}

.selection-hint {
    margin-left: auto;
    font-size: 13px;
    color: #666;
    font-style: italic;
}

/* Enhanced speakers table for selection */
.speakers-table tr.selected {
    background: #e3f2fd !important;
    border-left: 4px solid var(--primary-color);
}

.speakers-table tr.selected td {
    color: #0056b3;
    font-weight: 500;
}

/* Speaker name cell - clickable */
.speaker-name-cell {
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 12px 15px;
    border-radius: 4px;
    position: relative;
}

/*  */

/* Better mobile responsiveness for delete actions */
@media (max-width: 768px) {
    .loading-indicator {
        font-size: 11px;
        padding: 6px 10px;
    }

    .delete-result-btn {
        min-width: 40px;
        min-height: 40px;
    }

    .button {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .speaker-selection-info {
        flex-direction: column;
        align-items: center;
        gap: 8px;
        text-align: center;
    }

    .selection-hint {
        margin-left: 0;
        font-size: 12px;
    }
}

/* Audio Level Monitor */
.audio-monitor {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.audio-monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.audio-level-label {
    font-weight: 500;
    color: #495057;
}

.audio-level-value {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 16px;
    min-width: 30px;
    text-align: right;
}

.audio-level-container {
    margin-bottom: 15px;
}

.audio-level-bar {
    position: relative;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 5px;
    border: 1px solid #dee2e6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.audio-level-bar:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(230, 57, 70, 0.1);
}

.audio-level-indicator {
    height: 100%;
    background: linear-gradient(90deg,
        #28a745 0%,
        #28a745 30%,
        #ffc107 30%,
        #ffc107 70%,
        #dc3545 70%,
        #dc3545 100%);
    width: 0%;
    transition: width 0.1s ease-out;
    border-radius: 10px;
}

.silence-threshold-line {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dc3545;
    left: 20%;
    transition: left 0.2s ease;
    z-index: 2;
}

.silence-threshold-line::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -3px;
    width: 8px;
    height: 8px;
    background: #dc3545;
    border-radius: 50%;
}

.silence-threshold-line::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: -3px;
    width: 8px;
    height: 8px;
    background: #dc3545;
    border-radius: 50%;
}

.audio-level-labels {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: #6c757d;
    margin-top: 2px;
}

.silence-threshold-control {
    margin-top: 15px;
}

.audio-threshold-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    margin: 10px 0;
    -webkit-appearance: none;
    appearance: none;
}

.audio-threshold-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.audio-threshold-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.threshold-help {
    margin-top: 5px;
    color: #6c757d;
}

.threshold-help small {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Audio level status indicators */
.audio-level-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 15px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 13px;
}

.audio-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #6c757d;
    transition: all 0.3s ease;
}

.audio-status-indicator.speaking {
    background: #28a745;
    animation: pulse-speaking 1s infinite;
    box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
}

.audio-status-indicator.silent {
    background: #dc3545;
}

.audio-status-indicator.error {
    background: #ffc107;
    animation: pulse-error 2s infinite;
}

@keyframes pulse-speaking {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes pulse-error {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}



#audioStatusText {
    font-weight: 500;
    color: #495057;
}

/* New Speaker Button */
#newSpeakerBtn {
    position: relative;
    transition: all 0.3s ease;
    min-width: 120px;
}

#newSpeakerBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

#newSpeakerBtn.active {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    transform: translateY(-2px);
    animation: pulse-new-speaker 2s infinite;
}

#newSpeakerBtn.active:hover {
    background: linear-gradient(135deg, #20c997, #28a745);
}

@keyframes pulse-new-speaker {
    0% { box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3); }
    50% { box-shadow: 0 6px 20px rgba(40, 167, 69, 0.5); }
    100% { box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3); }
}

/* New Speaker Mode Indicator */
.new-speaker-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    z-index: 1000;
    display: none;
    animation: slideInRight 0.3s ease;
}

.new-speaker-indicator.show {
    display: block;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Speaker Selection Indicator */
.speaker-selection-indicator {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    min-width: 350px;
    max-width: 600px;
    min-height: 80px;
    max-height: 120px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
    margin: 0 20px;
    display: flex;
    flex-direction: column;
}

.speaker-selection-indicator .indicator-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px 10px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.speaker-selection-indicator .indicator-title {
    font-size: 14px;
    font-weight: 600;
    flex: 1;
}

.speaker-selection-indicator .selected-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
    min-width: 24px;
    text-align: center;
}

.speaker-selection-indicator .selected-speakers-list {
    padding: 8px 12px;
    flex: 1;
    overflow-x: auto;
    overflow-y: auto;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 6px;
    align-items: flex-start;
    align-content: flex-start;
    max-height: 80px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

/* Custom scrollbar for WebKit browsers */
.speaker-selection-indicator .selected-speakers-list::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

.speaker-selection-indicator .selected-speakers-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.speaker-selection-indicator .selected-speakers-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.speaker-selection-indicator .selected-speakers-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.speaker-selection-indicator .no-selection {
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
    font-size: 13px;
}

.speaker-selection-indicator .speaker-tag {
    display: inline-block;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    padding: 4px 10px;
    margin: 0;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 0;
    max-width: 120px;
    min-width: 60px;
    line-height: 1.2;
}

.speaker-selection-indicator .speaker-tag:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.speaker-selection-indicator .speaker-tag.new-speaker {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Hide indicator when no speakers selected */
.speaker-selection-indicator.hidden {
    display: none;
}



/* Responsive adjustments */
@media (max-width: 768px) {
    .app-header {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .speaker-selection-indicator {
        min-width: auto;
        max-width: none;
        width: 100%;
        margin: 0;
        min-height: 70px;
        max-height: 120px;
    }

    .speaker-selection-indicator .selected-speakers-list {
        padding: 6px 10px;
        gap: 4px;
        max-height: 90px;
    }

    .speaker-selection-indicator .speaker-tag {
        max-width: 100px;
        font-size: 10px;
        padding: 3px 6px;
        border-radius: 12px;
    }
}

@media (max-width: 480px) {
    .speaker-selection-indicator {
        min-height: 60px;
        max-height: 100px;
    }

    .speaker-selection-indicator .indicator-header {
        padding: 6px 8px;
        font-size: 12px;
    }

    .speaker-selection-indicator .indicator-title {
        font-size: 12px;
    }

    .speaker-selection-indicator .selected-count {
        padding: 2px 6px;
        font-size: 10px;
        min-width: 20px;
    }

    .speaker-selection-indicator .selected-speakers-list {
        padding: 4px 8px;
        gap: 3px;
        max-height: 80px;
    }

    .speaker-selection-indicator .speaker-tag {
        font-size: 9px;
        padding: 2px 5px;
        max-width: 70px;
        border-radius: 10px;
        line-height: 1.1;
    }
}

/* Special Speaker Styling - Similar to normal speakers but with subtle differences */
.speaker-name-cell.special-speaker {
    background: #f8f9fa;
    color: #495057;
    font-weight: 500;
    border-left: 3px solid #007bff;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.speaker-name-cell.special-speaker:hover {
    background: #e9ecef;
    border-left-color: #0056b3;
}

.speaker-name-cell.special-speaker.selected {
    background: #28a745;
    color: white;
    border-left-color: #155724;
}

.speaker-name-cell.special-speaker.selected:hover {
    background: #218838;
}

.speaker-name-cell.special-speaker i {
    margin-right: 8px;
    color: #007bff;
    font-size: 14px;
}

.speaker-name-cell.special-speaker.selected i {
    color: white;
}

/* Small indicator that this is special */
.speaker-name-cell.special-speaker::after {
    content: "●";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #007bff;
    font-size: 8px;
    opacity: 0.6;
}

.speaker-name-cell.special-speaker.selected::after {
    color: white;
}

/* Special Speaker Label */
.special-speaker-label {
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 12px;
    border: 1px solid #dee2e6;
    display: inline-block;
    font-weight: 500;
}



/* Mobile responsive for audio monitor */
@media (max-width: 768px) {
    .audio-monitor {
        padding: 12px;
    }

    .audio-level-bar {
        height: 16px;
    }

    .audio-level-value {
        font-size: 14px;
    }

    .audio-threshold-slider::-webkit-slider-thumb {
        width: 16px;
        height: 16px;
    }

    .audio-threshold-slider::-moz-range-thumb {
        width: 16px;
        height: 16px;
    }
}

/* Latest result highlight - Light red background */
.result-card.latest {
    background: linear-gradient(135deg, #ffe6e6, #fff0f0) !important;
}