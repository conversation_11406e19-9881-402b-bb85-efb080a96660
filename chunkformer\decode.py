import os
import warnings
# Tắt warnings không cần thiết
warnings.filterwarnings("ignore", category=UserWarning, module="speechbrain")
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", message=".*torchaudio.*")
warnings.filterwarnings("ignore", message=".*torch.nn.utils.weight_norm.*")
warnings.filterwarnings("ignore", message=".*torch.nn.utils.spectral_norm.*")
# Tắt warnings từ pyannote-audio
warnings.filterwarnings("ignore", message=".*TensorFloat-32.*")
warnings.filterwarnings("ignore", message=".*std.*degrees of freedom.*")
warnings.filterwarnings("ignore", category=UserWarning, module="pyannote")
# Tắt ReproducibilityWarning
from pyannote.audio.utils.reproducibility import ReproducibilityWarning
warnings.filterwarnings("ignore", category=ReproducibilityWarning)

import torch
import torchaudio
import yaml
import argparse
import pyaudio
import wave
import time
import threading
import queue
import signal
import sys
import numpy as np
import re
from tqdm import tqdm
import subprocess

import json
import shutil
import tempfile
from contextlib import nullcontext
from pydub import AudioSegment
from datetime import datetime

from flask import Flask, render_template, jsonify, request, flash, redirect, url_for, send_from_directory, send_file, Response

import xlsxwriter
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler



# Pyannote imports for speaker diarization
try:
    from pyannote.audio import Pipeline
    import librosa
    import soundfile as sf
    PYANNOTE_AVAILABLE = True
    print("✅ Pyannote.audio available for speaker diarization")
except ImportError:
    PYANNOTE_AVAILABLE = False
    print("⚠️ Pyannote.audio not available. Install with: pip install pyannote.audio librosa soundfile")

# Import Vietnamese corrector
from vietnamese_corrector import VietnameseCorrector





# ===== PYANNOTE SPEAKER DIARIZATION =====

# Global pyannote pipeline
diarization_pipeline = None

def init_pyannote_pipeline():
    """Khởi tạo pyannote speaker diarization pipeline"""
    global diarization_pipeline

    if not PYANNOTE_AVAILABLE:
        print("⚠️ Pyannote.audio không khả dụng, bỏ qua khởi tạo diarization")
        return False

    try:
        print("🔄 Đang khởi tạo pyannote speaker diarization pipeline...")
        
        # Kiểm tra Hugging Face token
        if not HF_TOKEN or HF_TOKEN == "":
            print("❌ Hugging Face token không hợp lệ")
            return False
        
        print(f"🔑 Sử dụng Hugging Face token: {HF_TOKEN[:8]}...")

        # Sử dụng pipeline từ Hugging Face với token được xác thực
        diarization_pipeline = Pipeline.from_pretrained(
            "pyannote/speaker-diarization-3.1",
            use_auth_token=HF_TOKEN
        )

        # Chuyển pipeline sang GPU nếu có
        if torch.cuda.is_available():
            diarization_pipeline.to(torch.device("cuda"))
            print("🚀 Pyannote pipeline đã được chuyển sang GPU")
        else:
            print("💻 Pyannote pipeline đang chạy trên CPU")

        print("✅ Pyannote speaker diarization pipeline đã sẵn sàng")
        return True

    except Exception as e:
        print(f"❌ Lỗi khởi tạo pyannote pipeline: {str(e)}")
        print("💡 Hãy đảm bảo bạn có token Hugging Face hợp lệ")
        print("💡 Kiểm tra kết nối internet và quyền truy cập model")
        print("💡 Thử chạy: huggingface-cli login")
        diarization_pipeline = None
        return False

def perform_speaker_diarization(audio_file_path):
    """
    Thực hiện speaker diarization trên file audio

    Args:
        audio_file_path: Đường dẫn đến file audio
        overlap: Có phát hiện overlap hay không

    Returns:
        List of segments với thông tin [start_time, end_time, speaker_label]
    """
    global diarization_pipeline

    if diarization_pipeline is None:
        print("⚠️ Pyannote pipeline chưa được khởi tạo")
        return []

    try:
        print(f"🎯 Đang thực hiện speaker diarization cho: {audio_file_path}")

        # Load audio với librosa để chuẩn hóa
        audio, sr = librosa.load(audio_file_path, sr=16000)

        # Lưu audio đã chuẩn hóa vào file tạm
        temp_audio_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
        temp_audio_file.close()

        import soundfile as sf
        sf.write(temp_audio_file.name, audio, sr)

        # Thực hiện diarization
        diarization = diarization_pipeline(temp_audio_file.name)

        # Chuyển đổi kết quả thành list
        segments = []
        for turn, _, speaker in diarization.itertracks(yield_label=True):
            segments.append({
                'start': turn.start,
                'end': turn.end,
                'speaker': speaker,
                'duration': turn.end - turn.start
            })

        # Cleanup temp file
        os.unlink(temp_audio_file.name)

        print(f"✅ Phát hiện {len(segments)} đoạn nói từ {len(set(s['speaker'] for s in segments))} người nói")

        # Debug output
        for i, segment in enumerate(segments):
            print(f"   Đoạn {i+1}: {segment['start']:.2f}s - {segment['end']:.2f}s | {segment['speaker']} | {segment['duration']:.2f}s")

        return segments

    except Exception as e:
        print(f"❌ Lỗi khi thực hiện speaker diarization: {str(e)}")
        return []

def identify_speakers_in_segments(audio_file_path, diarization_segments):
    """
    Nhận dạng người nói trong từng segment bằng ECAPA-TDNN

    Args:
        audio_file_path: Đường dẫn file audio gốc
        diarization_segments: Kết quả từ speaker diarization

    Returns:
        List of segments với speaker names đã được nhận dạng
    """
    if not diarization_segments:
        return []

    try:
        print(f"🔍 Đang nhận dạng người nói cho {len(diarization_segments)} đoạn...")

        # Load audio
        audio, sr = librosa.load(audio_file_path, sr=16000)

        # Nhóm segments theo speaker_id để tránh tạo nhiều profile cho cùng một người
        speaker_groups = {}
        for segment in diarization_segments:
            speaker_id = segment['speaker']
            if speaker_id not in speaker_groups:
                speaker_groups[speaker_id] = []
            speaker_groups[speaker_id].append(segment)

        print(f"📊 Phát hiện {len(speaker_groups)} người nói khác nhau: {list(speaker_groups.keys())}")

        # Nhận dạng từng nhóm speaker bằng cách gộp tất cả segments của cùng speaker_id
        speaker_mappings = {}  # Map từ speaker_id sang thông tin nhận dạng

        for speaker_id, speaker_segments in speaker_groups.items():
            print(f"🔍 Đang xử lý {speaker_id} với {len(speaker_segments)} segments...")

            # Gộp tất cả audio segments của cùng speaker_id thành một đoạn liên tục
            combined_audio_segments = []
            total_duration = 0

            for segment in speaker_segments:
                start_sample = int(segment['start'] * sr)
                end_sample = int(segment['end'] * sr)
                segment_audio = audio[start_sample:end_sample]
                combined_audio_segments.append(segment_audio)
                total_duration += segment['end'] - segment['start']

            # Nối tất cả segments lại thành một đoạn audio duy nhất
            combined_audio = np.concatenate(combined_audio_segments)

            print(f"   📊 Gộp {len(speaker_segments)} segments → {total_duration:.2f}s audio cho {speaker_id}")

            # Lưu combined audio vào file tạm để extract embedding
            temp_combined_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
            temp_combined_file.close()

            import soundfile as sf
            sf.write(temp_combined_file.name, combined_audio, sr)

            try:
                # Extract embedding cho toàn bộ audio của speaker này
                device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                embedding = extract_speaker_embedding(temp_combined_file.name, device)

                if embedding is not None:
                    # Tạo văn bản tạm thời để có thể tạo profile (sẽ được cập nhật sau)
                    temp_text = f"Audio segment from {speaker_id}"

                    # Tìm matching profile với khả năng tự động tạo profile mới (chỉ khi auto_profile_creation=True)
                    best_match, similarity = find_matching_profile(
                        embedding,
                        text=temp_text,
                        audio_file=temp_combined_file.name,
                        auto_create_if_no_match=auto_profile_creation  # ← Check setting
                    )
                    print(f"   🔍 So sánh với profiles: best_match={best_match}, similarity={similarity:.3f}")

                    speaker_name = "Chờ xác định người nói"
                    confidence = 0.0

                    # Kiểm tra kết quả
                    if best_match and similarity >= SIMILARITY_THRESHOLD:
                        speaker_name = best_match
                        confidence = similarity
                        print(f"   ✅ Nhận dạng từ profile: {best_match} (tin cậy: {similarity:.3f})")
                    else:
                        # Kiểm tra trong database nếu không tìm thấy trong profiles
                        similar_embedding, db_similarity = find_similar_embedding_in_database(embedding)
                        if similar_embedding and db_similarity >= SIMILARITY_THRESHOLD:
                            if similar_embedding.get('is_known', False) and similar_embedding.get('speaker_name'):
                                speaker_name = similar_embedding['speaker_name']
                                confidence = db_similarity
                                print(f"   ✅ Nhận dạng từ database: {speaker_name} (tin cậy: {db_similarity:.3f})")
                            else:
                                speaker_name = "Người đã gặp, chưa đặt tên"
                                confidence = db_similarity
                                print(f"   ❓ Người đã gặp nhưng chưa đặt tên (tin cậy: {db_similarity:.3f})")
                        else:
                            # Không nhận dạng được - đánh dấu để xử lý sau
                            if total_duration >= 3.0:  # Đủ audio để tạo profile
                                print(f"   🆕 Không nhận dạng được, sẽ xử lý tạo profile sau khi có văn bản")
                                speaker_name = f"Người Nói Mới {speaker_id}"  # Tạm thời, sẽ được xử lý sau
                                confidence = 0.0
                            else:
                                print(f"   ⚠️ Không nhận dạng được + audio quá ngắn ({total_duration:.2f}s < 3.0s)")
                                speaker_name = "Người lạ"
                                confidence = 0.0

                    # Lưu mapping cho tất cả segments của speaker này
                    speaker_mappings[speaker_id] = {
                        'speaker_name': speaker_name,
                        'confidence': confidence,
                        'embedding': embedding,
                        'combined_duration': total_duration,
                        'segments_count': len(speaker_segments),
                        'needs_auto_profile': speaker_name.startswith("Người Nói Mới")
                    }

                    print(f"   ✅ {speaker_id} → {speaker_name} (tin cậy: {confidence:.3f}) [từ {len(speaker_segments)} segments, {total_duration:.2f}s]")

            except Exception as e:
                print(f"⚠️ Lỗi khi nhận dạng {speaker_id}: {str(e)}")
                speaker_mappings[speaker_id] = {
                    'speaker_name': "Lỗi nhận dạng",
                    'confidence': 0.0,
                    'embedding': None,
                    'combined_duration': total_duration,
                    'segments_count': len(speaker_segments)
                }
            finally:
                # Cleanup temp file
                if os.path.exists(temp_combined_file.name):
                    os.unlink(temp_combined_file.name)

        # In ra mapping đã xác định
        print(f"🎯 Speaker mapping đã xác định:")
        for speaker_id, mapping in speaker_mappings.items():
            print(f"   {speaker_id} → {mapping['speaker_name']} (tin cậy: {mapping['confidence']:.3f})")

        # Áp dụng kết quả nhận dạng cho tất cả segments
        print(f"🔄 Áp dụng mapping cho {len(diarization_segments)} segments...")
        identified_segments = []
        for segment in diarization_segments:
            speaker_id = segment['speaker']
            if speaker_id in speaker_mappings:
                mapping = speaker_mappings[speaker_id]
                identified_segments.append({
                    'start': segment['start'],
                    'end': segment['end'],
                    'duration': segment['duration'],
                    'diarization_speaker': speaker_id,
                    'identified_speaker': mapping['speaker_name'],
                    'confidence': mapping['confidence'],
                    'embedding': mapping['embedding']
                })
            else:
                identified_segments.append({
                    'start': segment['start'],
                    'end': segment['end'],
                    'duration': segment['duration'],
                    'diarization_speaker': speaker_id,
                    'identified_speaker': "Lỗi nhận dạng",
                    'confidence': 0.0,
                    'embedding': None
                })

        print(f"✅ Đã áp dụng mapping cho tất cả segments:")
        for speaker_id, mapping in speaker_mappings.items():
            segment_count = sum(1 for seg in identified_segments if seg['diarization_speaker'] == speaker_id)
            print(f"   {speaker_id} → {mapping['speaker_name']}: {segment_count} segments")

        # Xử lý tự động tạo profile cho speakers mới (chỉ khi auto_profile_creation=True)
        if auto_profile_creation:
            print(f"🔍 Kiểm tra speakers cần tự tạo profile...")
            for speaker_id, mapping in speaker_mappings.items():
                if mapping.get('needs_auto_profile', False):
                    print(f"🆕 Tự tạo profile cho {speaker_id}...")

                    # Tạo tên profile mới
                    global unknown_speaker_count
                    unknown_speaker_count += 1
                    new_profile_name = f"Người Nói {unknown_speaker_count}"                    # Tạo profile với embedding đã có
                    if mapping['embedding'] is not None:
                        try:
                            # Sử dụng hàm save_embedding để tạo profile JSON đầy đủ
                            text_sample = mapping.get('combined_text', 'Tự động tạo từ speaker diarization')
                            audio_file = mapping.get('audio_file', None)

                            # Lướu profile JSON với thông tin đầy đủ
                            save_success = save_embedding(
                                new_profile_name,
                                mapping['embedding'],
                                text_sample,
                                audio_file,
                                notify_created=True
                            )

                            if save_success:
                                print(f"✅ Đã tạo profile tự động: {new_profile_name} cho {speaker_id}")
                            else:
                                print(f"❌ Lỗi khi tạo profile JSON cho {new_profile_name}")
                                continue

                            # Cập nhật mapping
                            speaker_mappings[speaker_id]['speaker_name'] = new_profile_name
                            speaker_mappings[speaker_id]['needs_auto_profile'] = False

                            # Cập nhật identified_segments
                            for seg in identified_segments:
                                if seg['diarization_speaker'] == speaker_id:
                                    seg['identified_speaker'] = new_profile_name

                        except Exception as e:
                            print(f"❌ Lỗi khi tạo profile cho {speaker_id}: {str(e)}")
                            # Fallback to "Người lạ"
                            speaker_mappings[speaker_id]['speaker_name'] = "Người lạ"
                            for seg in identified_segments:
                                if seg['diarization_speaker'] == speaker_id:
                                    seg['identified_speaker'] = "Người lạ"
        else:
            print(f"⚠️ Tự động tạo profile bị tắt (auto_profile_creation = {auto_profile_creation})")
            # Đặt tất cả speakers cần auto profile thành "Người lạ"
            for speaker_id, mapping in speaker_mappings.items():
                if mapping.get('needs_auto_profile', False):
                    print(f"⚠️ {speaker_id}: không tạo profile vì auto_profile_creation = False → gán là 'Người lạ'")
                    speaker_mappings[speaker_id]['speaker_name'] = "Người lạ"
                    for seg in identified_segments:
                        if seg['diarization_speaker'] == speaker_id:
                            seg['identified_speaker'] = "Người lạ"

        print(f"✅ Hoàn thành xử lý speaker identification:")
        for speaker_id, mapping in speaker_mappings.items():
            segment_count = sum(1 for seg in identified_segments if seg['diarization_speaker'] == speaker_id)
            print(f"   {speaker_id} → {mapping['speaker_name']}: {segment_count} segments")

        return identified_segments

    except Exception as e:
        print(f"❌ Lỗi khi nhận dạng speakers: {str(e)}")
        return []

def create_audio_segment(audio_file_path, start_time, end_time):
    """
    Tạo file audio segment từ file gốc

    Args:
        audio_file_path: Đường dẫn file audio gốc
        start_time: Thời gian bắt đầu (giây)
        end_time: Thời gian kết thúc (giây)

    Returns:
        Đường dẫn file segment đã tạo
    """
    try:
        # Load audio với librosa
        audio, sr = librosa.load(audio_file_path, sr=16000)

        # Tính sample positions
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)

        # Extract segment
        segment_audio = audio[start_sample:end_sample]

        # Tạo file tạm cho segment
        temp_segment_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
        temp_segment_file.close()

        # Lưu segment
        import soundfile as sf
        sf.write(temp_segment_file.name, segment_audio, sr)

        return temp_segment_file.name

    except Exception as e:
        print(f"❌ Lỗi khi tạo audio segment {start_time:.2f}s-{end_time:.2f}s: {str(e)}")
        return None

# Hugging Face token for authentication
HF_TOKEN = "*************************************"

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'super_secret_key'  # Required for flash messages

# Import and register export routes blueprint
try:
    from export_routes import export_routes
    app.register_blueprint(export_routes)
    print("✓ Export routes blueprint registered successfully")
except ImportError as e:
    print(f"⚠ Warning: Could not import export_routes blueprint: {e}")
except Exception as e:
    print(f"⚠ Warning: Could not register export_routes blueprint: {e}")

# Global variables for results and settings
web_results = []
web_results_lock = threading.Lock()
settings_lock = threading.Lock()

# Global variable for selected speakers for comparison
selected_speakers_for_comparison = set()
selected_speakers_lock = threading.Lock()

# Đường dẫn file JSON để lưu kết quả
RESULTS_FILE = "saved_results.json"

# Simple Message Queue for SSE
class SimpleMessageQueue:
    def __init__(self):
        self.listeners = {}
        self.lock = threading.Lock()
    
    def publish(self, message, type='message'):
        formatted_message = {'data': message, 'type': type}
        with self.lock:
            for channel, queues in self.listeners.items():
                for q in queues[:]:  # Sao chép danh sách để tránh lỗi khi xóa
                    try:
                        q.put(formatted_message)
                    except:
                        # Loại bỏ queue không hoạt động
                        queues.remove(q)
    
    def subscribe(self, channel):
        with self.lock:
            if channel not in self.listeners:
                self.listeners[channel] = []
            q = queue.Queue()
            self.listeners[channel].append(q)
            return q

    def unsubscribe(self, channel, queue_obj):
        with self.lock:
            if channel in self.listeners and queue_obj in self.listeners[channel]:
                self.listeners[channel].remove(queue_obj)

# Khởi tạo MessageQueue toàn cục
message_queue = SimpleMessageQueue()

# File watcher for settings.json changes
class SettingsFileHandler(FileSystemEventHandler):
    def __init__(self, settings_file='settings.json'):
        self.settings_file = settings_file
        self.last_modified = 0
        
    def on_modified(self, event):
        if event.is_directory:
            return
            
        if event.src_path.endswith(self.settings_file):
            # Tránh xử lý nhiều lần cùng lúc
            current_time = time.time()
            if current_time - self.last_modified < 1:  # Debounce 1 giây
                return
            self.last_modified = current_time
            
            print("🔄 Phát hiện thay đổi trong settings.json, đang tải lại cài đặt...")
            time.sleep(0.1)  # Chờ file được ghi xong
            load_settings()
            
            # Gửi thông báo đến frontend qua SSE
            settings_data = get_current_settings()
            message_queue.publish(json.dumps({
                'action': 'settings_reloaded',
                'settings': settings_data,
                'timestamp': time.time()
            }), 'message')
            print("✅ Đã tải lại cài đặt và thông báo cho frontend")

def start_file_watcher():
    """Khởi tạo file watcher để theo dõi thay đổi trong settings.json"""
    event_handler = SettingsFileHandler()
    observer = Observer()
    observer.schedule(event_handler, path='.', recursive=False)
    observer.start()
    print("🔍 File watcher đã khởi động - theo dõi thay đổi settings.json")
    return observer

def get_current_settings():
    """Lấy tất cả cài đặt hiện tại"""
    return {
        'similarity_threshold': SIMILARITY_THRESHOLD,
        'auto_similarity_threshold': AUTO_PROFILE_SIMILARITY_THRESHOLD,
        'embedding_merge_threshold': EMBEDDING_MERGE_THRESHOLD,
        'vad_threshold': VAD_THRESHOLD,
        'vad_min_speech_duration': VAD_MIN_SPEECH_DURATION,
        'vad_min_silence_duration': VAD_MIN_SILENCE_DURATION,
        'auto_profile_creation': auto_profile_creation,
        'merge_same_speaker': merge_same_speaker,
        'merge_all_speakers': merge_all_speakers,
        'vietnamese_correction_enabled': vietnamese_correction_enabled
    }

# Cài đặt các gói phụ thuộc nếu chưa có
def install_dependencies():
    required_packages = [
        'speechbrain', 'scipy', 'flask', 'xlsxwriter', 'torch', 'torchaudio', 
        'onnxruntime', 'pyannote.audio', 'librosa', 'soundfile'
    ]
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ Gói {package} đã được cài đặt")
        except ImportError:
            print(f"Đang cài đặt gói {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ Đã cài đặt {package} thành công")

print("Kiểm tra và cài đặt các gói phụ thuộc...")
install_dependencies()

import torchaudio.compliance.kaldi as kaldi
from scipy.spatial.distance import cosine

# Silero VAD
import onnxruntime
from collections import deque

try:
    from model.utils.init_model import init_model
    from model.utils.checkpoint import load_checkpoint
    from model.utils.file_utils import read_symbol_table
    from model.utils.ctc_utils import get_output_with_timestamps
except ImportError:
    print("Không tìm thấy các module 'model'. Hãy đảm bảo bạn đã đặt script này đúng vị trí.")
    sys.exit(1)

try:
    from speechbrain.inference import EncoderClassifier
except ImportError:
    print("Lỗi khi import SpeechBrain. Hãy cài đặt lại thủ công: pip install speechbrain")
    sys.exit(1)

# Hàm tải kết quả từ file khi khởi động
def load_results_from_file():
    global web_results
    try:
        if os.path.exists(RESULTS_FILE):
            with open(RESULTS_FILE, 'r', encoding='utf-8') as f:
                loaded_results = json.load(f)
                with web_results_lock:
                    web_results = loaded_results.get('results', [])
            print(f"Đã tải {len(web_results)} kết quả từ file '{RESULTS_FILE}'")
        else:
            print(f"File '{RESULTS_FILE}' không tồn tại. Bắt đầu với danh sách kết quả trống.")
    except Exception as e:
        print(f"Lỗi khi tải kết quả từ file: {str(e)}")

# Hàm lưu kết quả vào file sau mỗi lần có kết quả mới
def save_results_to_file():
    try:
        with web_results_lock:
            results_data = {'results': web_results, 'timestamp': datetime.now().isoformat()}
            with open(RESULTS_FILE, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, ensure_ascii=False, indent=2)
        print(f"Đã lưu {len(web_results)} kết quả vào file '{RESULTS_FILE}'")
    except Exception as e:
        print(f"Lỗi khi lưu kết quả vào file: {str(e)}")

def save_results_to_file_safe():
    """Lưu kết quả vào file một cách an toàn, optimized để giảm lock contention"""
    try:
        # Thử acquire lock với timeout ngắn hơn (1 giây)
        if web_results_lock.acquire(timeout=1.0):
            try:
                # Tạo bản sao nhanh để giảm thời gian hold lock
                current_results = web_results.copy() if web_results else []
                current_time = datetime.now()
            finally:
                # Release lock ngay sau khi copy xong
                web_results_lock.release()

            # Xử lý file I/O sau khi đã release lock
            try:
                results_data = {'results': current_results, 'timestamp': current_time.isoformat()}

                # Lưu với backup trước khi ghi đè
                backup_file = RESULTS_FILE + '.backup'
                if os.path.exists(RESULTS_FILE):
                    shutil.copy2(RESULTS_FILE, backup_file)

                # Ghi file mới
                with open(RESULTS_FILE, 'w', encoding='utf-8') as f:
                    json.dump(results_data, f, ensure_ascii=False, indent=2)

                print(f"✅ Đã lưu an toàn {len(current_results)} kết quả vào file '{RESULTS_FILE}'")

                # Xóa backup nếu lưu thành công
                if os.path.exists(backup_file):
                    os.remove(backup_file)
            except Exception as e:
                print(f"❌ Lỗi khi ghi file (data được bảo vệ): {str(e)}")
                # Khôi phục từ backup nếu có lỗi
                backup_file = RESULTS_FILE + '.backup'
                if os.path.exists(backup_file):
                    shutil.copy2(backup_file, RESULTS_FILE)
                    print(f"🔄 Đã khôi phục từ backup")
        else:
            # Timeout - MERGE dữ liệu thay vì ghi đè
            print("⚠️ Lock timeout (1s), đang merge dữ liệu để không mất data...")
            
            # Lấy snapshot nhanh của current results bằng cách khác
            current_results = []
            try:
                # Thử lấy snapshot nhanh hơn với timeout rất ngắn
                if web_results_lock.acquire(timeout=0.1):
                    try:
                        current_results = list(web_results) if web_results else []
                    finally:
                        web_results_lock.release()
                else:
                    print("⚠️ Không thể lấy snapshot current results, sử dụng merge strategy")
            except Exception as e:
                print(f"⚠️ Lỗi khi lấy snapshot: {str(e)}")

            # Đọc dữ liệu hiện có trong file
            existing_results = []
            if os.path.exists(RESULTS_FILE):
                try:
                    with open(RESULTS_FILE, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                        existing_results = existing_data.get('results', [])
                except Exception as e:
                    print(f"⚠️ Lỗi khi đọc file hiện có: {str(e)}")
                    existing_results = []
            
            # Merge với results mới
            merged_results = existing_results.copy()
            
            # Thêm results mới vào cuối (avoid duplication bằng timestamp check)
            current_time = datetime.now()
            for result in current_results:
                # Check if result already exists based on content similarity
                if not any(result.strip() == existing.strip() for existing in merged_results[-5:]):  # Check last 5 results
                    merged_results.append(result)
            
            results_data = {
                'results': merged_results, 
                'timestamp': current_time.isoformat(),
                'note': 'Merged due to lock timeout'
            }

            # Lưu với backup
            backup_file = RESULTS_FILE + '.backup'
            if os.path.exists(RESULTS_FILE):
                shutil.copy2(RESULTS_FILE, backup_file)

            # Ghi file merged
            with open(RESULTS_FILE, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, ensure_ascii=False, indent=2)

            print(f"✅ Đã merge và lưu {len(merged_results)} kết quả vào file '{RESULTS_FILE}' (bảo vệ data cũ)")

            # Xóa backup nếu lưu thành công
            if os.path.exists(backup_file):
                os.remove(backup_file)

    except Exception as e:
        print(f"❌ Lỗi khi lưu kết quả vào file: {str(e)}")
        # Khôi phục từ backup nếu có lỗi
        backup_file = RESULTS_FILE + '.backup'
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, RESULTS_FILE)
            print(f"🔄 Đã khôi phục từ backup")

# SSE endpoint
@app.route('/stream')
def stream():
    def generate():
        q = message_queue.subscribe('messages')
        try:
            # Gửi heartbeat ban đầu để khởi tạo kết nối
            yield "data: {}\n\n"
            
            while True:
                message = q.get()
                
                event_type = message.get('type', 'message')
                data = json.dumps(message.get('data'))
                
                output = ""
                if event_type != 'message':
                    output += f"event: {event_type}\n"
                
                # Chia dữ liệu JSON thành từng dòng
                for line in data.splitlines():
                    output += f"data: {line}\n"
                
                output += "\n"
                yield output
        finally:
            message_queue.unsubscribe('messages', q)
    
    return Response(generate(), mimetype='text/event-stream')

# Flask routes
@app.route('/')
def index():
    with web_results_lock, settings_lock:
        current_settings = {
            'similarity_threshold': SIMILARITY_THRESHOLD,
            'auto_similarity_threshold': AUTO_PROFILE_SIMILARITY_THRESHOLD,
            'embedding_merge_threshold': EMBEDDING_MERGE_THRESHOLD,
            'vad_threshold': VAD_THRESHOLD,
            'vad_min_speech_duration': VAD_MIN_SPEECH_DURATION,
            'vad_min_silence_duration': VAD_MIN_SILENCE_DURATION,
            'auto_profile_creation': auto_profile_creation,
            'merge_same_speaker': merge_same_speaker,
            'merge_all_speakers': merge_all_speakers,
            'vietnamese_correction_enabled': vietnamese_correction_enabled
        }
        return render_template('index.html', results=web_results, settings=current_settings)

@app.route('/results')
def get_results():
    with web_results_lock:
        return jsonify({'results': web_results})

@app.route('/settings', methods=['POST'])
def settings():
    global SIMILARITY_THRESHOLD, AUTO_PROFILE_SIMILARITY_THRESHOLD, EMBEDDING_MERGE_THRESHOLD
    global VAD_THRESHOLD, VAD_MIN_SPEECH_DURATION, VAD_MIN_SILENCE_DURATION
    global auto_profile_creation
    global merge_same_speaker, merge_all_speakers  # Thêm biến này
    global vietnamese_correction_enabled

    try:
        with settings_lock:
            # Cập nhật cài đặt từ dữ liệu biểu mẫu
            SIMILARITY_THRESHOLD = float(request.form['similarity_threshold'])
            AUTO_PROFILE_SIMILARITY_THRESHOLD = float(request.form['auto_similarity_threshold'])
            EMBEDDING_MERGE_THRESHOLD = float(request.form['embedding_merge_threshold'])
            
            # Cập nhật cài đặt Silero VAD
            VAD_THRESHOLD = float(request.form['vad_threshold'])
            VAD_MIN_SPEECH_DURATION = float(request.form['vad_min_speech_duration'])
            VAD_MIN_SILENCE_DURATION = float(request.form['vad_min_silence_duration'])
            
            auto_profile_creation = request.form.get('auto_profile_creation') == 'on'
            merge_same_speaker = request.form.get('merge_same_speaker') == 'on'
            merge_all_speakers = request.form.get('merge_all_speakers') == 'on'
            vietnamese_correction_enabled = request.form.get('vietnamese_correction_enabled') == 'on'

            
            print(f"📝 Cài đặt đã cập nhật:")
            print(f"   - Ghi âm liên tục: Bật")
            print(f"   - Phát hiện thay đổi người nói: Bật")
            print(f"   - Ngưỡng VAD: {VAD_THRESHOLD}")
            print(f"   - Thời gian tối thiểu giọng nói: {VAD_MIN_SPEECH_DURATION}s")
            print(f"   - Thời gian tối thiểu im lặng: {VAD_MIN_SILENCE_DURATION}s")
            print(f"   - Tự động tạo profile: {'Bật' if auto_profile_creation else 'Tắt'}")
            print(f"   - Sửa lỗi tiếng Việt: {'Bật' if vietnamese_correction_enabled else 'Tắt'}")


            # Xác thực dữ liệu đầu vào
            if not (0 <= SIMILARITY_THRESHOLD <= 1):
                flash("Ngưỡng tương đồng phải nằm trong khoảng 0 đến 1.", "error")
                return redirect(url_for('index'))
            if not (0 <= AUTO_PROFILE_SIMILARITY_THRESHOLD <= 1):
                flash("Ngưỡng tương đồng tự động phải nằm trong khoảng 0 đến 1.", "error")
                return redirect(url_for('index'))
            if not (0 <= EMBEDDING_MERGE_THRESHOLD <= 1):
                flash("Ngưỡng gộp embedding phải nằm trong khoảng 0 đến 1.", "error")
                return redirect(url_for('index'))
            
            # Xác thực cài đặt Silero VAD
            if not (0 <= VAD_THRESHOLD <= 1):
                flash("Ngưỡng VAD phải nằm trong khoảng 0 đến 1.", "error")
                return redirect(url_for('index'))
            if VAD_MIN_SPEECH_DURATION <= 0:
                flash("Thời gian tối thiểu phát hiện giọng nói phải lớn hơn 0.", "error")
                return redirect(url_for('index'))
            if VAD_MIN_SILENCE_DURATION <= 0:
                flash("Thời gian tối thiểu im lặng VAD phải lớn hơn 0.", "error")
                return redirect(url_for('index'))

            flash("Cài đặt đã được cập nhật thành công!", "success")
            return redirect(url_for('index'))
    except ValueError as e:
        flash("Dữ liệu nhập vào không hợp lệ. Vui lòng kiểm tra lại.", "error")
        return redirect(url_for('index'))
    except Exception as e:
        flash(f"Lỗi khi cập nhật cài đặt: {str(e)}", "error")
        return redirect(url_for('index'))

@app.route('/update_setting', methods=['POST'])
def update_setting():
    """Cập nhật một cài đặt riêng lẻ thông qua AJAX."""
    global SIMILARITY_THRESHOLD, AUTO_PROFILE_SIMILARITY_THRESHOLD, EMBEDDING_MERGE_THRESHOLD
    global VAD_THRESHOLD, VAD_MIN_SPEECH_DURATION, VAD_MIN_SILENCE_DURATION
    global auto_profile_creation
    global merge_same_speaker, merge_all_speakers
    global vietnamese_correction_enabled

    try:
        data = request.json
        setting_name = data.get('name')
        setting_value = data.get('value')
        
        if not setting_name:
            return jsonify({'success': False, 'message': 'Thiếu tên cài đặt'})
        
        with settings_lock:
            # Cập nhật cài đặt dựa trên tên
            if setting_name == 'similarity_threshold':
                SIMILARITY_THRESHOLD = float(setting_value)
                if not (0 <= SIMILARITY_THRESHOLD <= 1):
                    return jsonify({'success': False, 'message': 'Ngưỡng tương đồng phải nằm trong khoảng 0 đến 1'})
            elif setting_name == 'auto_similarity_threshold':
                AUTO_PROFILE_SIMILARITY_THRESHOLD = float(setting_value)
                if not (0 <= AUTO_PROFILE_SIMILARITY_THRESHOLD <= 1):
                    return jsonify({'success': False, 'message': 'Ngưỡng tương đồng tự động phải nằm trong khoảng 0 đến 1'})
            elif setting_name == 'embedding_merge_threshold':
                EMBEDDING_MERGE_THRESHOLD = float(setting_value)
                if not (0 <= EMBEDDING_MERGE_THRESHOLD <= 1):
                    return jsonify({'success': False, 'message': 'Ngưỡng gộp embedding phải nằm trong khoảng 0 đến 1'})
            elif setting_name == 'vad_threshold':
                VAD_THRESHOLD = float(setting_value)
                if not (0 <= VAD_THRESHOLD <= 1):
                    return jsonify({'success': False, 'message': 'Ngưỡng VAD phải nằm trong khoảng 0 đến 1'})
            elif setting_name == 'vad_min_speech_duration':
                VAD_MIN_SPEECH_DURATION = float(setting_value)
                if VAD_MIN_SPEECH_DURATION <= 0:
                    return jsonify({'success': False, 'message': 'Thời gian tối thiểu phát hiện giọng nói phải lớn hơn 0'})
            elif setting_name == 'vad_min_silence_duration':
                VAD_MIN_SILENCE_DURATION = float(setting_value)
                if VAD_MIN_SILENCE_DURATION <= 0:
                    return jsonify({'success': False, 'message': 'Thời gian tối thiểu im lặng VAD phải lớn hơn 0'})
            elif setting_name == 'auto_profile_creation':
                auto_profile_creation = setting_value
            elif setting_name == 'merge_same_speaker':
                merge_same_speaker = setting_value
            elif setting_name == 'merge_all_speakers':
                merge_all_speakers = setting_value
            elif setting_name == 'vietnamese_correction_enabled':
                vietnamese_correction_enabled = setting_value

            else:
                return jsonify({'success': False, 'message': f'Cài đặt không hợp lệ: {setting_name}'})
            
            # Lưu cài đặt vào file settings.json
            settings_data = {
                'similarity_threshold': SIMILARITY_THRESHOLD,
                'auto_similarity_threshold': AUTO_PROFILE_SIMILARITY_THRESHOLD,
                'embedding_merge_threshold': EMBEDDING_MERGE_THRESHOLD,
                'vad_threshold': VAD_THRESHOLD,
                'vad_min_speech_duration': VAD_MIN_SPEECH_DURATION,
                'vad_min_silence_duration': VAD_MIN_SILENCE_DURATION,
                'auto_profile_creation': auto_profile_creation,
                'merge_same_speaker': merge_same_speaker,
                'merge_all_speakers': merge_all_speakers,
                'vietnamese_correction_enabled': vietnamese_correction_enabled,

            }
            
            try:
                with open('settings.json', 'w', encoding='utf-8') as f:
                    json.dump(settings_data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"Lỗi khi lưu settings.json: {str(e)}")
            
            return jsonify({'success': True, 'message': f'Đã cập nhật {setting_name} thành công'})
            
    except ValueError as e:
        return jsonify({'success': False, 'message': 'Dữ liệu nhập vào không hợp lệ'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi cập nhật cài đặt: {str(e)}'})

@app.route('/update_silence_threshold', methods=['POST'])
def update_silence_threshold():
    """Cập nhật ngưỡng im lặng cho audio monitoring"""
    global VAD_THRESHOLD
    try:
        data = request.json
        threshold = data.get('threshold')

        if threshold is None:
            return jsonify({'success': False, 'message': 'Ngưỡng im lặng không được để trống'})

        # Validate threshold range (0-1)
        if not (0 <= threshold <= 1):
            return jsonify({'success': False, 'message': 'Ngưỡng im lặng phải trong khoảng 0-1'})

        with settings_lock:
            # Update VAD threshold setting
            VAD_THRESHOLD = threshold

            # Lưu cài đặt vào file settings.json
            settings_data = {
                'similarity_threshold': SIMILARITY_THRESHOLD,
                'auto_similarity_threshold': AUTO_PROFILE_SIMILARITY_THRESHOLD,
                'embedding_merge_threshold': EMBEDDING_MERGE_THRESHOLD,
                'vad_threshold': VAD_THRESHOLD,
                'vad_min_speech_duration': VAD_MIN_SPEECH_DURATION,
                'vad_min_silence_duration': VAD_MIN_SILENCE_DURATION,
                'auto_profile_creation': auto_profile_creation,
                'merge_same_speaker': merge_same_speaker,
                'merge_all_speakers': merge_all_speakers,
                'vietnamese_correction_enabled': vietnamese_correction_enabled,
            }

            try:
                with open('settings.json', 'w', encoding='utf-8') as f:
                    json.dump(settings_data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"Lỗi khi lưu settings.json: {str(e)}")

        print(f"🔧 Silence threshold updated to: {threshold} ({threshold*100}%)")

        return jsonify({
            'success': True,
            'message': f'Đã cập nhật ngưỡng im lặng: {threshold*100:.0f}%',
            'threshold': threshold
        })

    except Exception as e:
        print(f"❌ Error updating silence threshold: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/get_settings', methods=['GET'])
def get_settings():
    """Lấy tất cả cài đặt hiện tại từ server"""
    try:
        with settings_lock:
            settings_data = get_current_settings()
            return jsonify({'success': True, 'settings': settings_data})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi lấy cài đặt: {str(e)}'})

@app.route('/speakers')
def get_speakers():
    """Trả về danh sách tất cả hồ sơ người nói."""
    speakers_list = []
    try:
        if os.path.exists(PROFILE_FOLDER):
            profile_files = [f for f in os.listdir(PROFILE_FOLDER) if f.endswith('.json')]
            for file in profile_files:
                try:
                    profile_path = os.path.join(PROFILE_FOLDER, file)
                    with open(profile_path, 'r', encoding='utf-8') as f:
                        profile = json.load(f)
                    
                    # Tạo ID từ tên file
                    speaker_id = os.path.splitext(file)[0]
                    
                    # Thông tin speaker
                    speaker_info = {
                        'id': speaker_id,
                        'name': profile.get('name', 'Không tên'),
                        'created_at': profile.get('created_at', 'Không xác định'),
                        'text': profile.get('text', ''),
                    }
                    
                    # Thêm đường dẫn file âm thanh nếu có
                    if 'audio_file' in profile:
                        speaker_info['audio_file'] = profile['audio_file']
                    
                    speakers_list.append(speaker_info)
                except Exception as e:
                    print(f"Lỗi khi đọc profile {file}: {str(e)}")
        
        return jsonify({'speakers': speakers_list})
    except Exception as e:
        print(f"Lỗi khi lấy danh sách người nói: {str(e)}")
        return jsonify({'speakers': []})

@app.route('/update_speaker', methods=['POST'])
def update_speaker():
    """Cập nhật tên của hồ sơ người nói."""
    global web_results
    try:
        data = request.json
        speaker_id = data.get('id')
        new_name = data.get('name')
        
        if not speaker_id or not new_name:
            return jsonify({'success': False, 'message': 'Thiếu ID hoặc tên mới'})
        
        # Tạo đường dẫn file từ ID
        profile_path = os.path.join(PROFILE_FOLDER, f"{speaker_id}.json")
        
        if not os.path.exists(profile_path):
            return jsonify({'success': False, 'message': 'Hồ sơ người nói không tồn tại'})
        
        # Đọc profile hiện tại
        with open(profile_path, 'r', encoding='utf-8') as f:
            profile = json.load(f)
          # Cập nhật tên
        old_name = profile['name']
        
        # Kiểm tra tên mới không trùng với tên hiện có
        if new_name != old_name:
            # Kiểm tra xem tên mới đã tồn tại chưa
            if new_name in speaker_embeddings:
                return jsonify({'success': False, 'message': f'Tên "{new_name}" đã được sử dụng cho speaker khác'})
            
            # Kiểm tra file JSON của tên mới
            new_profile_path = os.path.join(PROFILE_FOLDER, f"{new_name}.json")
            if os.path.exists(new_profile_path):
                return jsonify({'success': False, 'message': f'File profile cho tên "{new_name}" đã tồn tại'})
            
            # Kiểm tra tên dành riêng
            if is_profile_name_reserved(new_name):
                return jsonify({'success': False, 'message': f'Tên "{new_name}" là tên dành riêng, không thể sử dụng'})
        
        profile['name'] = new_name
        profile['updated_at'] = time.strftime('%Y-%m-%d %H:%M:%S')
          # Lưu lại profile với tên mới
        with open(profile_path, 'w', encoding='utf-8') as f:
            json.dump(profile, f, ensure_ascii=False, indent=2)
        
        # Nếu tên thay đổi, tạo file mới và xóa file cũ
        if new_name != old_name:
            new_profile_path = os.path.join(PROFILE_FOLDER, f"{new_name}.json")
            
            # Tạo file mới với tên mới
            with open(new_profile_path, 'w', encoding='utf-8') as f:
                json.dump(profile, f, ensure_ascii=False, indent=2)
            
            # Xóa file cũ nếu tên khác nhau
            if profile_path != new_profile_path and os.path.exists(profile_path):
                os.remove(profile_path)
                print(f"🗑️ Đã xóa file profile cũ: {os.path.basename(profile_path)}")
            
            print(f"💾 Đã tạo file profile mới: {new_name}.json")        # Cập nhật trong speaker_embeddings (atomic operation để tránh race condition)
        if old_name in speaker_embeddings:
            embedding = speaker_embeddings[old_name]
            # Chỉ xóa và thêm nếu tên thực sự thay đổi
            if new_name != old_name:
                del speaker_embeddings[old_name]
                speaker_embeddings[new_name] = embedding
                print(f"🔄 Đã cập nhật tên người nói trong memory: '{old_name}' → '{new_name}'")
            else:
                print(f"ℹ️ Tên không thay đổi, giữ nguyên trong memory: '{new_name}'")
        
        # Cập nhật trong embeddings_database
        for item in embeddings_database:
            if item.get('speaker_name') == old_name:
                item['speaker_name'] = new_name
          # Cập nhật unknown_speaker_count nếu cần thiết
        # Kiểm tra xem còn ai có tên "Người Nói X" không để cập nhật counter
        print(f"🔄 Trước khi cập nhật counter: unknown_speaker_count = {unknown_speaker_count}")
        print(f"🔄 Renamed: '{old_name}' → '{new_name}'")
        update_unknown_speaker_count()
        print(f"🔄 Sau khi cập nhật counter: unknown_speaker_count = {unknown_speaker_count}")
        
        # Cập nhật trong web_results
        with web_results_lock:
            for i, result in enumerate(web_results):
                # Kiểm tra format mới với timestamp: [HH:MM:SS] [Tên người nói]
                if f"] [{old_name}]" in result:
                    # Thay thế tên cũ bằng tên mới trong format với timestamp
                    web_results[i] = result.replace(f"] [{old_name}]", f"] [{new_name}]")
                elif result.startswith(f"[{old_name}]"):
                    # Thay thế tên cũ bằng tên mới trong format cũ
                    web_results[i] = result.replace(f"[{old_name}]", f"[{new_name}]")
            
            # Phát sự kiện cập nhật toàn bộ
            message_queue.publish({"action": "refresh_all"}, type='refresh_results')
            # Phát sự kiện cập nhật danh sách người nói
            message_queue.publish({"action": "speaker_updated", "old_name": old_name, "new_name": new_name}, type='speaker_update')
          # Lưu kết quả đã cập nhật vào file
        save_results_to_file()
        
        # Kiểm tra tính nhất quán sau khi cập nhật
        print(f"✅ Hoàn thành cập nhật profile:")
        print(f"   - Tên cũ: '{old_name}'")
        print(f"   - Tên mới: '{new_name}'")
        print(f"   - Có trong memory: {new_name in speaker_embeddings}")
        print(f"   - File tồn tại: {os.path.exists(os.path.join(PROFILE_FOLDER, f'{new_name}.json'))}")
        print(f"   - Unknown speaker count: {unknown_speaker_count}")
        
        return jsonify({'success': True, 'message': f'Đã đổi tên thành công: "{old_name}" → "{new_name}"'})
    except Exception as e:
        print(f"Lỗi khi cập nhật tên người nói: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/toggle_pause', methods=['POST'])
def toggle_pause():
    """Toggle trạng thái tạm dừng lắng nghe."""
    global is_listening_paused
    try:
        is_listening_paused = not is_listening_paused
        status = 'paused' if is_listening_paused else 'active'
        message = 'Đã tạm dừng lắng nghe' if is_listening_paused else 'Đã tiếp tục lắng nghe'
        print(f"Trạng thái lắng nghe: {message}")
        return jsonify({'success': True, 'status': status, 'message': message})
    except Exception as e:
        print(f"Lỗi khi toggle pause: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})



@app.route('/delete_speaker', methods=['POST'])
def delete_speaker():
    """Xóa một hồ sơ người nói."""
    try:
        data = request.json
        speaker_id = data.get('id')
        
        if not speaker_id:
            return jsonify({'success': False, 'message': 'Thiếu ID người nói'})
        
        # Tạo đường dẫn file từ ID
        profile_path = os.path.join(PROFILE_FOLDER, f"{speaker_id}.json")
        
        if not os.path.exists(profile_path):
            return jsonify({'success': False, 'message': 'Hồ sơ người nói không tồn tại'})
        
        # Đọc tên người nói từ file
        with open(profile_path, 'r', encoding='utf-8') as f:
            profile = json.load(f)
        speaker_name = profile.get('name')
        
        # Xóa file hồ sơ
        os.remove(profile_path)
        
        # Xóa khỏi speaker_embeddings
        if speaker_name in speaker_embeddings:
            del speaker_embeddings[speaker_name]
        
        # Đánh dấu là không biết trong embeddings_database
        for item in embeddings_database:
            if item.get('speaker_name') == speaker_name:
                item['is_known'] = False
                item['speaker_name'] = None
        
        print(f"Đã xóa hồ sơ người nói: {speaker_name}")
        # Phát sự kiện thông báo người nói đã bị xóa
        message_queue.publish({"action": "speaker_deleted", "speaker_name": speaker_name}, type='speaker_update')
        return jsonify({'success': True})
    except Exception as e:
        print(f"Lỗi khi xóa hồ sơ người nói: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/delete_all_speakers', methods=['POST'])
def delete_all_speakers():
    """Xóa tất cả hồ sơ người nói."""
    try:
        global speaker_embeddings, embeddings_database, unknown_speaker_count
        
        # Xóa tất cả file hồ sơ
        if os.path.exists(PROFILE_FOLDER):
            profile_files = [f for f in os.listdir(PROFILE_FOLDER) if f.endswith('.json')]
            for file in profile_files:
                try:
                    profile_path = os.path.join(PROFILE_FOLDER, file)
                    os.remove(profile_path)
                except Exception as e:
                    print(f"Lỗi khi xóa file {file}: {str(e)}")
        
        # Đánh dấu tất cả embeddings là không biết
        for item in embeddings_database:
            item['is_known'] = False
            item['speaker_name'] = None
        
        # Xóa tất cả speaker_embeddings
        speaker_embeddings.clear()
        
        # Reset biến đếm người nói
        unknown_speaker_count = 0
        
        print("Đã xóa tất cả hồ sơ người nói")
        # Phát sự kiện thông báo tất cả người nói đã bị xóa
        message_queue.publish({"action": "all_speakers_deleted"}, type='speaker_update')
        return jsonify({'success': True})
    except Exception as e:
        print(f"Lỗi khi xóa tất cả hồ sơ người nói: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/audio/<filename>')
def serve_audio(filename):
    """Phục vụ file âm thanh để nghe lại."""
    try:
        # Đầu tiên tìm trong thư mục unknown_speakers
        unknown_dir = "unknown_speakers"
        if os.path.exists(os.path.join(unknown_dir, filename)):
            return send_from_directory(unknown_dir, filename)
        
        # Nếu không tìm thấy, báo lỗi
        return "File không tồn tại", 404
    except Exception as e:
        print(f"Lỗi khi phục vụ file âm thanh: {str(e)}")
        return "Lỗi khi phục vụ file âm thanh", 500

@app.route('/court_transcript')
def court_transcript():
    """Hiển thị trang biên bản tòa án tương tác."""
    return render_template('court_transcript.html')

@app.route('/save_transcript', methods=['POST'])
def save_transcript():
    """Lưu biên bản đã hoàn thành."""
    try:
        data = request.json
        content = data.get('content', '')

        # Tạo thư mục để lưu biên bản nếu chưa có
        transcript_folder = "transcripts"
        os.makedirs(transcript_folder, exist_ok=True)

        # Tạo tên file với timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{transcript_folder}/bien_ban_{timestamp}.html"

        # Ghi ra file
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        return jsonify({
            'success': True,
            'message': 'Đã lưu biên bản thành công',
            'filename': os.path.basename(filename)
        })
    except Exception as e:
        print(f"Lỗi khi lưu biên bản: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/selected_speakers', methods=['GET'])
def get_selected_speakers():
    """Lấy danh sách speakers được chọn để so sánh."""
    with selected_speakers_lock:
        return jsonify({
            'success': True,
            'selected_speakers': list(selected_speakers_for_comparison)
        })

@app.route('/selected_speakers', methods=['POST'])
def update_selected_speakers():
    """Cập nhật danh sách speakers được chọn để so sánh."""
    try:
        data = request.json
        selected_speakers = data.get('selected_speakers', [])

        with selected_speakers_lock:
            selected_speakers_for_comparison.clear()
            selected_speakers_for_comparison.update(selected_speakers)

        print(f"📋 Cập nhật danh sách speakers được chọn: {selected_speakers}")

        return jsonify({
            'success': True,
            'message': f'Đã cập nhật danh sách speakers được chọn ({len(selected_speakers)} speakers)',
            'selected_speakers': selected_speakers
        })
    except Exception as e:
        print(f"Lỗi khi cập nhật danh sách speakers được chọn: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/toggle_speaker_selection', methods=['POST'])
def toggle_speaker_selection():
    """Bật/tắt việc chọn một speaker cụ thể."""
    try:
        data = request.json
        speaker_name = data.get('speaker_name', '')

        if not speaker_name:
            return jsonify({'success': False, 'message': 'Tên speaker không được để trống'})

        with selected_speakers_lock:
            if speaker_name in selected_speakers_for_comparison:
                selected_speakers_for_comparison.remove(speaker_name)
                action = 'removed'
            else:
                selected_speakers_for_comparison.add(speaker_name)
                action = 'added'

            selected_list = list(selected_speakers_for_comparison)

        print(f"🔄 {action.capitalize()} speaker '{speaker_name}' {'from' if action == 'removed' else 'to'} comparison list")

        return jsonify({
            'success': True,
            'action': action,
            'speaker_name': speaker_name,
            'selected_speakers': selected_list,
            'message': f'Đã {"bỏ chọn" if action == "removed" else "chọn"} speaker: {speaker_name}'
        })
    except Exception as e:
        print(f"Lỗi khi toggle speaker selection: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/correct_text', methods=['POST'])
def correct_text():
    """Sửa văn bản tiếng Việt cho một đoạn cụ thể."""
    global vietnamese_corrector
    try:
        data = request.json
        text = data.get('text', '').strip()
        
        if not text:
            return jsonify({'success': False, 'message': 'Không có văn bản để sửa'})
        
        # Kiểm tra xem Vietnamese corrector có khả dụng không
        if not vietnamese_correction_enabled or vietnamese_corrector is None:
            return jsonify({'success': False, 'message': 'Tính năng sửa văn bản tiếng Việt không khả dụng'})
        
        try:
            corrected_text = vietnamese_corrector.correct_text(text)
            if corrected_text and corrected_text.strip():
                return jsonify({
                    'success': True, 
                    'original_text': text,
                    'corrected_text': corrected_text.strip()
                })
            else:
                return jsonify({'success': False, 'message': 'Không thể sửa văn bản này'})
        except Exception as e:
            print(f"Lỗi khi sửa văn bản Vietnamese: {str(e)}")
            return jsonify({'success': False, 'message': f'Lỗi khi sửa văn bản: {str(e)}'})
            
    except Exception as e:
        print(f"Lỗi trong API correct_text: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi API: {str(e)}'})

@app.route('/save_results', methods=['POST'])
def save_results():
    """Lưu danh sách kết quả đã chỉnh sửa - CHỈ cho phép cập nhật, không ghi đè toàn bộ."""
    global web_results
    try:
        data = request.json
        if 'results' in data:
            # Kiểm tra xem có thay đổi speaker nào không (để quyết định có cần refresh hay không)
            force_refresh = data.get('force_refresh', False)

            # Thử acquire lock với timeout ngắn hơn để tránh treo
            if web_results_lock.acquire(timeout=0.5):
                try:
                    incoming_results = data['results']

                    # Kiểm tra xem có phải là thao tác xóa không
                    is_deletion = len(incoming_results) < len(web_results)

                    # Cho phép cập nhật nếu:
                    # 1. Có nhiều kết quả hơn hoặc bằng hiện tại (thêm mới)
                    # 2. Có ít kết quả hơn nhưng force_refresh = True (xóa)
                    # 3. Có ít kết quả hơn nhưng chênh lệch không quá lớn (có thể là xóa hợp lệ)
                    should_update = (
                        len(incoming_results) >= len(web_results) or  # Thêm mới
                        force_refresh or  # Force refresh (thường là từ thao tác xóa)
                        (is_deletion and len(web_results) - len(incoming_results) <= 10)  # Xóa hợp lệ (không quá 10 items)
                    )

                    if should_update:
                        action_type = "xóa" if is_deletion else "cập nhật"
                        print(f"📝 {action_type.capitalize()} kết quả: {len(web_results)} → {len(incoming_results)} kết quả")
                        web_results = incoming_results
                        # Chỉ phát sự kiện refresh_all nếu thực sự cần thiết
                        if force_refresh:
                            message_queue.publish({"action": "refresh_all"}, type='refresh_results')
                    else:
                        print(f"⚠️ Từ chối cập nhật: Frontend có {len(incoming_results)} kết quả, server có {len(web_results)} kết quả (chênh lệch quá lớn)")
                        return jsonify({'success': False, 'message': f'Từ chối ghi đè: Chênh lệch quá lớn ({len(incoming_results)} vs {len(web_results)})'})
                finally:
                    web_results_lock.release()
                
                # Lưu file async để không block response
                import threading
                threading.Thread(target=save_results_to_file_safe, daemon=True).start()
                
                return jsonify({'success': True})
            else:
                print("⚠️ save_results: web_results_lock timeout (0.5s)")
                return jsonify({'success': False, 'message': 'Server đang bận, vui lòng thử lại sau'})
        return jsonify({'success': False, 'message': 'Không tìm thấy dữ liệu kết quả'})
    except Exception as e:
        print(f"Lỗi khi lưu kết quả: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/export_txt')
def export_txt():
    """Xuất kết quả nhận dạng ra file TXT"""
    try:
        with web_results_lock:
            if not web_results:
                return jsonify({'success': False, 'message': 'Không có kết quả để xuất'})
            
            # Tạo thư mục nếu chưa tồn tại
            os.makedirs("exports", exist_ok=True)
            
            # Tạo nội dung file txt
            content = []
            for result in web_results:
                # Xử lý để bao gồm timestamp và tên người nói
                # Kiểm tra format mới với timestamp: [HH:MM:SS] [Tên người nói] Nội dung
                new_format_match = re.match(r'^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$', result)
                if new_format_match:
                    timestamp = new_format_match.group(1)
                    speaker_name = new_format_match.group(2)
                    content_text = new_format_match.group(3).strip()
                    # Bao gồm timestamp, tên người nói và nội dung
                    content.append(f"[{timestamp}] [{speaker_name}] {content_text}")
                elif result.find('[') == 0 and ']' in result:
                    # Format cũ: [Tên người nói] Nội dung
                    speaker_info = result[1:result.find(']')]
                    content_text = result[result.find(']')+1:].strip()
                    
                    # Tên người nói
                    speaker_name = speaker_info;
                    
                    # Format cũ không có timestamp riêng, sử dụng timestamp hiện tại
                    current_time = datetime.now().strftime("%H:%M:%S");
                    content.append(f"[{current_time}] [{speaker_name}] {content_text}")
                else:
                    # Không có thông tin cấu trúc, thêm timestamp hiện tại
                    current_time = datetime.now().strftime("%H:%M:%S")
                    content.append(f"[{current_time}] {result}")
            
            # Đảm bảo có nội dung để xuất
            if not content:
                return jsonify({'success': False, 'message': 'Không có nội dung để xuất'})
            
            # Tạo file với timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"ket-qua-nhan-dang-{timestamp}.txt"
            file_path = os.path.join("exports", file_name)
            
            # Ghi nội dung ra file txt
            with open(file_path, 'w', encoding='utf-8') as f:
                for line in content:
                    f.write(line + "\n\n")
            
            return send_file(file_path, as_attachment=True, download_name=file_name)
    except Exception as e:
        print(f"Lỗi khi xuất file TXT: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/export_transcript_excel', methods=['POST'])
def export_transcript_excel():
    """API để tạo file Excel từ biên bản tòa án"""
    try:
        # Lấy dữ liệu từ request
        request_data = request.get_json()

        if not request_data or 'data' not in request_data:
            return jsonify({'success': False, 'message': 'Không tìm thấy dữ liệu biên bản'}), 400

        data = request_data.get('data', [])

        # Import openpyxl
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
            from openpyxl.utils import get_column_letter
        except ImportError:
            return jsonify({'success': False, 'message': 'Thiếu thư viện openpyxl'}), 500

        # Tạo một workbook và một sheet mới
        wb = Workbook()
        ws = wb.active
        ws.title = "Biên Bản Phiên Tòa"

        # Định dạng tiêu đề
        title_font = Font(name='Times New Roman', size=14, bold=True)
        header_font = Font(name='Times New Roman', size=12, bold=True)
        normal_font = Font(name='Times New Roman', size=12)

        # Định dạng border
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Thêm tiêu đề chính
        ws.merge_cells('A1:G1')
        title_cell = ws['A1']
        title_cell.value = "BIÊN BẢN PHIÊN TÒA"
        title_cell.font = title_font
        title_cell.alignment = Alignment(horizontal='center', vertical='center')

        # Thêm dữ liệu vào Excel
        section_titles = ['I. NHỮNG NGƯỜI TIẾN HÀNH TỐ TỤNG', 'II. NHỮNG NGƯỜI THAM GIA TỐ TỤNG',
                         'III. PHẦN THỦ TỤC BẮT ĐẦU PHIÊN TÒA', 'IV. PHẦN TRANH TỤNG TẠI PHIÊN TÒA']

        row_index = 3  # Bắt đầu từ hàng 3

        for row_data in data:
            if not row_data or len(row_data) == 0:
                continue

            # Kiểm tra xem có phải là tiêu đề section không
            if len(row_data) == 1 and any(title in str(row_data[0]) for title in section_titles):
                ws.merge_cells(f'A{row_index}:G{row_index}')
                cell = ws.cell(row=row_index, column=1, value=row_data[0])
                cell.font = header_font
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = thin_border
                row_index += 1
                continue

            # Xử lý dữ liệu người nói
            if len(row_data[0]) > 0 and ': ' in row_data[0]:
                speaker, text = row_data[0].split(': ', 1)
                cell = ws.cell(row=row_index, column=1, value=speaker + ':')
                cell.font = Font(name='Times New Roman', size=12, bold=True)

                # Ghi nội dung lời nói
                text_cell = ws.cell(row=row_index, column=2, value=text)
                text_cell.font = normal_font

                # Mở rộng ô chứa nội dung
                ws.merge_cells(f'B{row_index}:G{row_index}')
                row_index += 1
                continue

            # Dữ liệu bình thường
            for col_index, cell_value in enumerate(row_data, 1):
                cell = ws.cell(row=row_index, column=col_index, value=cell_value)
                cell.font = normal_font

                # Nếu là cột duy nhất, mở rộng nó
                if len(row_data) == 1:
                    ws.merge_cells(f'A{row_index}:G{row_index}')

            row_index += 1

        # Điều chỉnh độ rộng các cột
        for col in range(1, 8):
            ws.column_dimensions[get_column_letter(col)].width = 15

        # Cột đầu tiên rộng hơn cho tên người nói
        ws.column_dimensions['A'].width = 25

        # Cột thứ hai rộng hơn cho nội dung
        ws.column_dimensions['B'].width = 50

        # Lưu file tạm thời
        import tempfile
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_file_path = temp_file.name
        temp_file.close()

        wb.save(temp_file_path)

        # Gửi file về cho client
        response = send_file(
            temp_file_path,
            as_attachment=True,
            download_name='bien_ban_phien_toa.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # Thiết lập hàm callback để xóa file tạm sau khi gửi
        @response.call_on_close
        def cleanup():
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

        return response

    except Exception as e:
        # Xử lý lỗi
        print(f"Lỗi khi xuất Excel: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi xuất file Excel: {str(e)}'}), 500

@app.route('/export_excel')
def export_excel():
    """Xuất kết quả nhận dạng ra file Excel"""
    try:
        with web_results_lock:
            if not web_results:
                return jsonify({'success': False, 'message': 'Không có kết quả để xuất'})

            # Tạo thư mục nếu chưa tồn tại
            os.makedirs("exports", exist_ok=True)

            # Tạo tên file với timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"ket-qua-nhan-dang-{timestamp}.xlsx"
            file_path = os.path.join("exports", file_name)

            # Tạo workbook Excel mới
            workbook = xlsxwriter.Workbook(file_path)
            worksheet = workbook.add_worksheet("Kết quả nhận dạng")

            # Tạo định dạng
            header_format = workbook.add_format({
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'border': 1,
                'bg_color': '#4361ee',
                'color': 'white'
            })
            
            cell_format = workbook.add_format({
                'border': 1,
                'valign': 'vcenter',
                'text_wrap': True
            })
            
            # Thiết lập độ rộng cột - thêm cột thời gian
            worksheet.set_column('A:A', 15)  # Thời gian
            worksheet.set_column('B:B', 25)  # Người nói
            worksheet.set_column('C:C', 60)  # Nội dung

            # Viết tiêu đề - thêm cột thời gian
            headers = ["Thời gian", "Người nói", "Nội dung"]
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)
            
            # Duyệt qua các kết quả và viết vào Excel
            for row, result in enumerate(web_results):
                # Xử lý để bao gồm timestamp và tên người nói
                # Kiểm tra format mới với timestamp: [HH:MM:SS] [Tên người nói] Nội dung
                new_format_match = re.match(r'^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$', result)
                if new_format_match:
                    timestamp = new_format_match.group(1)
                    speaker_name = new_format_match.group(2)
                    content = new_format_match.group(3).strip()
                elif result.find('[') == 0 and ']' in result:
                    # Format cũ: [Tên người nói] Nội dung
                    speaker_info = result[1:result.find(']')]
                    content = result[result.find(']')+1:].strip()
                    
                    # Tên người nói
                    speaker_name = speaker_info
                    # Format cũ không có timestamp riêng, sử dụng timestamp hiện tại
                    timestamp = datetime.now().strftime("%H:%M:%S")
                else:
                    speaker_name = "Không xác định"
                    content = result
                    timestamp = datetime.now().strftime("%H:%M:%S")
                
                # Ghi dữ liệu vào Excel - ghi 3 cột
                worksheet.write(row + 1, 0, timestamp, cell_format)
                worksheet.write(row + 1, 1, speaker_name, cell_format)
                worksheet.write(row + 1, 2, content, cell_format)
            
            # Đóng workbook
            workbook.close()
            
            return send_file(file_path, as_attachment=True, download_name=file_name)
    except Exception as e:
        print(f"Lỗi khi xuất file Excel: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/delete_all_results', methods=['POST'])
def delete_all_results():
    """Xóa tất cả kết quả nhận dạng"""
    try:
        global web_results
        with web_results_lock:
            web_results = []
            # Phát sự kiện làm mới
            message_queue.publish({"action": "refresh_all"}, type='refresh_results')

        # Lưu trạng thái mới (trống) vào file
        save_results_to_file()

        return jsonify({'success': True})
    except Exception as e:
        print(f"Lỗi khi xóa tất cả kết quả: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/refresh_all_speaker_names', methods=['POST'])
def refresh_all_speaker_names():
    """Refresh tất cả tên người nói trong saved_results.json dựa trên danh sách speakers hiện tại"""
    try:
        import re
        global web_results

        # Lấy danh sách speakers hiện tại từ speaker_embeddings (in-memory)
        current_speakers = set(speaker_embeddings.keys())

        # Cũng lấy từ profile files để đảm bảo đầy đủ
        if os.path.exists(PROFILE_FOLDER):
            for filename in os.listdir(PROFILE_FOLDER):
                if filename.endswith('.json'):
                    try:
                        profile_path = os.path.join(PROFILE_FOLDER, filename)
                        with open(profile_path, 'r', encoding='utf-8') as f:
                            profile = json.load(f)
                            speaker_name = profile.get('name', filename[:-5])
                            current_speakers.add(speaker_name)
                    except Exception as e:
                        print(f"Lỗi khi đọc profile {filename}: {str(e)}")

        print(f"🔄 Refreshing speaker names with {len(current_speakers)} current speakers")
        print(f"📋 Current speakers: {list(current_speakers)}")

        # Đếm số lượng thay đổi
        changes_count = 0

        with web_results_lock:
            for i, result in enumerate(web_results):
                # Xử lý format mới với timestamp: [HH:MM:SS] [Tên người nói]
                new_format_match = re.match(r'^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$', result)
                if new_format_match:
                    timestamp = new_format_match.group(1)
                    current_speaker = new_format_match.group(2)
                    text = new_format_match.group(3)

                    # Kiểm tra xem speaker có tồn tại trong danh sách hiện tại không
                    if current_speaker not in current_speakers:
                        # Speaker không tồn tại, có thể đã bị xóa hoặc đổi tên
                        # Kiểm tra các trạng thái đặc biệt trước
                        if current_speaker not in ["Người lạ", "Chờ xác định người nói", "Lỗi nhận dạng"]:
                            # Gán về "Người lạ" nếu không tìm thấy
                            web_results[i] = f"[{timestamp}] [Người lạ] {text}"
                            changes_count += 1
                            print(f"📝 Updated missing speaker: '{current_speaker}' → 'Người lạ'")

                # Xử lý format cũ: [Tên người nói] text
                elif result.startswith('[') and ']' in result:
                    name_match = re.match(r'^\[(.*?)\]\s*(.*)$', result)
                    if name_match:
                        current_speaker = name_match.group(1)
                        text = name_match.group(2)

                        # Kiểm tra xem speaker có tồn tại trong danh sách hiện tại không
                        if current_speaker not in current_speakers:
                            # Speaker không tồn tại, có thể đã bị xóa hoặc đổi tên
                            if current_speaker not in ["Người lạ", "Chờ xác định người nói", "Lỗi nhận dạng"]:
                                # Gán về "Người lạ" nếu không tìm thấy
                                web_results[i] = f"[Người lạ] {text}"
                                changes_count += 1
                                print(f"📝 Updated missing speaker: '{current_speaker}' → 'Người lạ'")

        # Cập nhật saved_results.json nếu có thay đổi
        saved_changes_count = 0
        results_file = 'saved_results.json'

        if os.path.exists(results_file):
            try:
                with open(results_file, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)

                # saved_results.json có cấu trúc: {"results": [...], "timestamp": "..."}
                if 'results' in saved_data and isinstance(saved_data['results'], list):
                    # Duyệt qua từng kết quả string và cập nhật tên người nói
                    for i, result_str in enumerate(saved_data['results']):
                        if isinstance(result_str, str):
                            # Xử lý format: [HH:MM:SS] [Tên người nói] text
                            new_format_match = re.match(r'^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$', result_str)
                            if new_format_match:
                                timestamp = new_format_match.group(1)
                                current_speaker = new_format_match.group(2)
                                text = new_format_match.group(3)

                                # Kiểm tra xem speaker có tồn tại trong danh sách hiện tại không
                                if current_speaker not in current_speakers:
                                    # Speaker không tồn tại, có thể đã bị xóa hoặc đổi tên
                                    if current_speaker not in ["Người lạ", "Chờ xác định người nói", "Lỗi nhận dạng"]:
                                        # Gán về "Người lạ" nếu không tìm thấy
                                        saved_data['results'][i] = f"[{timestamp}] [Người lạ] {text}"
                                        saved_changes_count += 1
                                        print(f"🔄 Cập nhật saved_results.json: '{current_speaker}' ➜ 'Người lạ'")

                    # Lưu lại file nếu có thay đổi
                    if saved_changes_count > 0:
                        # Backup trước khi lưu
                        backup_file = results_file + '.backup'
                        shutil.copy2(results_file, backup_file)

                        with open(results_file, 'w', encoding='utf-8') as f:
                            json.dump(saved_data, f, ensure_ascii=False, indent=2)
                        print(f"💾 Đã lưu {saved_changes_count} thay đổi vào file '{results_file}'")

            except Exception as e:
                print(f"❌ Lỗi khi cập nhật saved_results.json: {str(e)}")

        # Lưu vào file và phát sự kiện refresh nếu có thay đổi
        total_changes = changes_count + saved_changes_count
        if total_changes > 0:
            if changes_count > 0:
                save_results_to_file_safe()
                # Phát sự kiện refresh để cập nhật UI
                message_queue.publish({"action": "refresh_all"}, type='refresh_results')
                print(f"✅ Refreshed {changes_count} speaker names in web results")
            print(f"✅ Tổng cộng đã cập nhật {total_changes} tên người nói")
        else:
            print("ℹ️ No speaker name changes needed")

        return jsonify({
            'success': True,
            'changes_count': total_changes,
            'web_changes': changes_count,
            'saved_changes': saved_changes_count,
            'total_results': len(web_results),
            'available_speakers': list(current_speakers),
            'message': f'Đã kiểm tra và cập nhật {total_changes} tên người nói (Web: {changes_count}, Saved: {saved_changes_count})'
        })

    except Exception as e:
        print(f"❌ Lỗi khi refresh speaker names: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

# Vô hiệu hóa symlink trong SpeechBrain để tránh lỗi quyền truy cập
os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = "1"
os.environ['HF_HUB_DISABLE_SYMLINKS'] = "1"
os.environ['HF_HUB_DISABLE_EXPERIMENTAL_WARNING'] = "1"
os.environ['HF_TOKEN'] = HF_TOKEN

# Tắt cảnh báo deprecation của SpeechBrain và pyannote
import warnings
warnings.filterwarnings("ignore", message=".*speechbrain.pretrained.*deprecated.*", category=UserWarning)
warnings.filterwarnings("ignore", message=".*was deprecated.*", category=UserWarning)
warnings.filterwarnings("ignore", message=".*FutureWarning.*", category=FutureWarning)

# Cấu hình ghi âm
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000
CHUNK = 1024
WAVE_OUTPUT_FOLDER = "temp_audio_chunks"
PROFILE_FOLDER = "profile"
EMBEDDINGS_FOLDER = os.path.join(PROFILE_FOLDER, "embeddings")

MAX_AUDIO_FILES = 3
CURRENT_AUDIO_FILE_INDEX = 0
AUDIO_FILE_LOCK = threading.Lock()

# Cấu hình Silero VAD - Tối ưu để không mất chữ đầu câu
VAD_THRESHOLD = 0.2   # Giảm ngưỡng để phát hiện sớm hơn (0-1)
VAD_MIN_SPEECH_DURATION = 0.1  # Giảm xuống để phát hiện nhanh hơn (giây)
VAD_MIN_SILENCE_DURATION = 0.8  # Giảm thời gian im lặng để phản hồi nhanh hơn (giây)
VAD_WINDOW_SIZE = 1024  # Tăng kích thước cửa sổ cho độ chính xác cao hơn (samples)
VAD_STEP_SIZE = 512   # Tăng bước nhảy tương ứng (samples)



# Cấu hình Pre-recording Buffer - Để không mất chữ đầu câu
PRE_RECORDING_DURATION = 0.5  # Lưu 0.5 giây audio trước khi phát hiện giọng nói
PRE_RECORDING_BUFFER_SIZE = int(PRE_RECORDING_DURATION * 16000 / 1024)  # Số chunks cần lưu

# Cấu hình Audio Processing - Thêm để cải thiện chất lượng âm thanh
AUDIO_NOISE_REDUCTION = True  # Bật noise reduction
AUDIO_NORMALIZATION = True   # Bật audio normalization
AUDIO_HIGH_PASS_FREQ = 80    # High-pass filter frequency (Hz)
AUDIO_LOW_PASS_FREQ = 8000   # Low-pass filter frequency (Hz)



# Cấu hình so sánh giọng nói
SIMILARITY_THRESHOLD = 0.4  # Ngưỡng so sánh profile chính
AUTO_PROFILE_SIMILARITY_THRESHOLD = 0.85
EMBEDDING_MERGE_THRESHOLD = 0.8



# Biến toàn cục
is_running = True
audio_queue = queue.Queue()
results = []
speaker_embeddings = {}
embeddings_database = []
speaker_classifier = None
vad_model = None  # Silero VAD model

unknown_speaker_count = 0
auto_profile_creation = False
merge_same_speaker = False
merge_all_speakers = False
current_speaker = None
is_listening_paused = False

# Vietnamese text correction settings
vietnamese_correction_enabled = True
vietnamese_corrector = None

def capitalize_first_letter(text):
    if not text:
        return text
    first_char = text[0]
    rest_of_text = text[1:]
    return first_char.upper() + rest_of_text

def apply_audio_preprocessing(audio_data, sample_rate=16000):
    """
    Áp dụng các kỹ thuật tiền xử lý âm thanh để cải thiện chất lượng

    Args:
        audio_data: bytes hoặc numpy array của audio data
        sample_rate: tần số lấy mẫu

    Returns:
        numpy array của audio data đã được xử lý
    """
    try:
        import scipy.signal
        from scipy.signal import butter, filtfilt

        # Chuyển đổi audio_data thành numpy array nếu cần
        if isinstance(audio_data, bytes):
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
        else:
            audio_array = audio_data

        # Chuyển đổi sang float32 để xử lý
        audio_float = audio_array.astype(np.float32)

        # Normalization - chuẩn hóa âm lượng
        if AUDIO_NORMALIZATION:
            max_val = np.max(np.abs(audio_float))
            if max_val > 0:
                audio_float = audio_float / max_val * 0.8  # Giữ một chút headroom

        # High-pass filter - loại bỏ tần số thấp (noise)
        if AUDIO_HIGH_PASS_FREQ > 0:
            nyquist = sample_rate / 2
            high_freq = min(AUDIO_HIGH_PASS_FREQ / nyquist, 0.99)
            b, a = butter(2, high_freq, btype='high')
            audio_float = filtfilt(b, a, audio_float)

        # Low-pass filter - loại bỏ tần số cao không cần thiết
        if AUDIO_LOW_PASS_FREQ > 0 and AUDIO_LOW_PASS_FREQ < sample_rate / 2:
            nyquist = sample_rate / 2
            low_freq = min(AUDIO_LOW_PASS_FREQ / nyquist, 0.99)
            b, a = butter(2, low_freq, btype='low')
            audio_float = filtfilt(b, a, audio_float)

        # Noise reduction đơn giản - spectral subtraction
        if AUDIO_NOISE_REDUCTION:
            # Ước tính noise từ 100ms đầu
            noise_samples = int(0.1 * sample_rate)
            if len(audio_float) > noise_samples:
                noise_level = np.std(audio_float[:noise_samples])
                # Áp dụng noise gate đơn giản
                threshold = noise_level * 2
                audio_float = np.where(np.abs(audio_float) < threshold,
                                     audio_float * 0.1, audio_float)

        # Chuyển về int16
        audio_processed = (audio_float * 32767).astype(np.int16)

        return audio_processed

    except Exception as e:
        print(f"Lỗi khi xử lý âm thanh: {str(e)}")
        return audio_data  # Trả về audio gốc nếu có lỗi

def enhance_vad_detection(audio_chunk):
    """
    Cải thiện phát hiện giọng nói bằng cách kết hợp nhiều phương pháp

    Args:
        audio_chunk: bytes hoặc numpy array của audio data

    Returns:
        bool: True nếu phát hiện giọng nói
    """
    try:
        # Chuyển đổi audio_chunk thành numpy array nếu cần
        if isinstance(audio_chunk, bytes):
            audio_array = np.frombuffer(audio_chunk, dtype=np.int16)
        else:
            audio_array = audio_chunk

        # Sử dụng Silero VAD
        if vad_model is not None:
            return detect_speech_silero(audio_array)

        # Phương pháp 3: Fallback về RMS-based detection
        else:
            return is_voice_legacy(audio_array)

    except Exception as e:
        print(f"Lỗi khi phát hiện giọng nói: {str(e)}")
        return is_voice_legacy(audio_chunk)  # Fallback

def signal_handler(sig, frame):
    global is_running
    print("\nDang dung ghi am...")
    is_running = False
    sys.exit(0)

@torch.no_grad()
def init(model_checkpoint, device):
    config_path = os.path.join(model_checkpoint, "config.yaml")
    checkpoint_path = os.path.join(model_checkpoint, "pytorch_model.bin")
    symbol_table_path = os.path.join(model_checkpoint, "vocab.txt")

    if not os.path.exists(config_path) or not os.path.exists(checkpoint_path) or not os.path.exists(symbol_table_path):
        print(f"Không tìm thấy các file model tại {model_checkpoint}")
        sys.exit(1)

    with open(config_path, 'r') as fin:
        config = yaml.load(fin, Loader=yaml.FullLoader)
    model = init_model(config, config_path)
    model.eval()
    load_checkpoint(model , checkpoint_path)

    model.encoder = model.encoder.to(device)
    model.ctc = model.ctc.to(device)

    symbol_table = read_symbol_table(symbol_table_path)
    char_dict = {v: k for k, v in symbol_table.items()}
    return model, char_dict

def load_audio(audio_path):
    audio = AudioSegment.from_file(audio_path)
    audio = audio.set_frame_rate(16000)
    audio = audio.set_sample_width(2)
    audio = audio.set_channels(1)
    audio = torch.as_tensor(audio.get_array_of_samples(), dtype=torch.float32).unsqueeze(0)
    return audio

def download_model_files():
    try:
        import huggingface_hub
        print("Đang tải model ECAPA-TDNN trực tiếp từ Hugging Face Hub...")
        target_dir = "pretrained_models/spkrec-ecapa-voxceleb"
        os.makedirs(target_dir, exist_ok=True)
        model_files = [
            "embedding_model.ckpt",
            "mean_var_norm_emb.ckpt",
            "classifier.ckpt",
            "label_encoder.txt",
            "hyperparams.yaml"
        ]
        for file in model_files:
            dest_path = os.path.join(target_dir, file)
            if not os.path.exists(dest_path):
                huggingface_hub.hf_hub_download(
                    repo_id="speechbrain/spkrec-ecapa-voxceleb",
                    filename=file,
                    local_dir=target_dir,
                    token=HF_TOKEN,
                    force_download=True
                )
                print(f"Đã tải {file} thành công")
        return True
    except Exception as e:
        print(f"Lỗi khi tải model: {str(e)}")
        return False

def init_speaker_encoder(device):
    global speaker_classifier
    print("Đang khởi tạo mô hình nhận dạng giọng nói...")
    os.makedirs("pretrained_models/spkrec-ecapa-voxceleb", exist_ok=True)
    try:
        speaker_classifier = EncoderClassifier.from_hparams(
            source="speechbrain/spkrec-ecapa-voxceleb", 
            savedir="pretrained_models/spkrec-ecapa-voxceleb",
            run_opts={"device": device},
            use_auth_token=HF_TOKEN
        )
        print("Khởi tạo mô hình nhận dạng giọng nói thành công!")
    except Exception as e:
        print(f"Lỗi khi khởi tạo mô hình nhận dạng giọng nói: {str(e)}")
        if download_model_files():
            try:
                speaker_classifier = EncoderClassifier.from_hparams(
                    source="pretrained_models/spkrec-ecapa-voxceleb",
                    run_opts={"device": device}
                )
                print("Khởi tạo mô hình nhận dạng giọng nói thành công (Phương pháp 2)!")
            except Exception as e:
                print(f"Phương pháp 2 thất bại: {str(e)}")
                speaker_classifier = None

def init_silero_vad():
    """Khởi tạo Silero VAD model"""
    global vad_model
    try:
        print("Đang khởi tạo Silero VAD...")
        # Tải Silero VAD model từ torch.hub
        vad_model, _ = torch.hub.load(
            repo_or_dir='snakers4/silero-vad',
            model='silero_vad',
            force_reload=False,
            onnx=False
        )

        # Silero VAD hoạt động tốt nhất trên CPU (tránh device mismatch)
        vad_model = vad_model.cpu()
        print("💻 Silero VAD đang chạy trên CPU (tối ưu cho VAD)")

        vad_model.eval()
        print("Khởi tạo Silero VAD thành công!")
        return True
    except Exception as e:
        print(f"Lỗi khi khởi tạo Silero VAD: {str(e)}")
        vad_model = None
        return False



def load_settings():
    """Tải cài đặt từ file settings.json"""
    global SIMILARITY_THRESHOLD, AUTO_PROFILE_SIMILARITY_THRESHOLD, EMBEDDING_MERGE_THRESHOLD
    global VAD_THRESHOLD, VAD_MIN_SPEECH_DURATION, VAD_MIN_SILENCE_DURATION
    global auto_profile_creation, merge_same_speaker, merge_all_speakers, vietnamese_correction_enabled
    
    try:
        if os.path.exists('settings.json'):
            with open('settings.json', 'r', encoding='utf-8') as f:
                settings_data = json.load(f)
                
            # Tải các cài đặt, sử dụng giá trị mặc định nếu không tồn tại
            SIMILARITY_THRESHOLD = settings_data.get('similarity_threshold', SIMILARITY_THRESHOLD)
            AUTO_PROFILE_SIMILARITY_THRESHOLD = settings_data.get('auto_similarity_threshold', AUTO_PROFILE_SIMILARITY_THRESHOLD)
            EMBEDDING_MERGE_THRESHOLD = settings_data.get('embedding_merge_threshold', EMBEDDING_MERGE_THRESHOLD)
            VAD_THRESHOLD = settings_data.get('vad_threshold', VAD_THRESHOLD)
            VAD_MIN_SPEECH_DURATION = settings_data.get('vad_min_speech_duration', VAD_MIN_SPEECH_DURATION)
            VAD_MIN_SILENCE_DURATION = settings_data.get('vad_min_silence_duration', VAD_MIN_SILENCE_DURATION)
            auto_profile_creation = settings_data.get('auto_profile_creation', auto_profile_creation)
            merge_same_speaker = settings_data.get('merge_same_speaker', merge_same_speaker)
            merge_all_speakers = settings_data.get('merge_all_speakers', merge_all_speakers)
            vietnamese_correction_enabled = settings_data.get('vietnamese_correction_enabled', vietnamese_correction_enabled)
            
            print("✓ Đã tải cài đặt từ settings.json")
        else:
            print("⚠ Không tìm thấy settings.json, sử dụng cài đặt mặc định")
    except Exception as e:
        print(f"⚠ Lỗi khi tải settings.json: {str(e)}, sử dụng cài đặt mặc định")

def init_vietnamese_corrector():
    """Khởi tạo Vietnamese text corrector"""
    global vietnamese_corrector
    try:
        if not vietnamese_correction_enabled:
            print("Vietnamese text correction bị tắt")
            return False
            
        print("Đang khởi tạo Vietnamese Text Corrector...")
        vietnamese_corrector = VietnameseCorrector()
        print("✓ Vietnamese Text Corrector đã được khởi tạo thành công")
        return True
    except Exception as e:
        print(f"⚠ Lỗi khi khởi tạo Vietnamese Text Corrector: {str(e)}")
        vietnamese_corrector = None
        return False

def is_voice_legacy(audio_chunk):
    """
    Legacy RMS-based voice activity detection (fallback method)
    
    Args:
        audio_chunk: bytes hoặc numpy array của audio data
    
    Returns:
        bool: True nếu phát hiện giọng nói, False nếu không
    """
    try:
        # Chuyển đổi audio chunk thành numpy array nếu cần
        if isinstance(audio_chunk, bytes):
            audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
        else:
            audio_data = audio_chunk
        
        # Tính RMS (Root Mean Square) của audio
        rms = np.sqrt(np.mean(audio_data.astype(np.float32) ** 2))
        
        # Ngưỡng RMS để phát hiện giọng nói (có thể điều chỉnh)
        rms_threshold = 500  # Ngưỡng thấp hơn để phát hiện giọng nói yếu
        
        return rms > rms_threshold
        
    except Exception as e:
        print(f"Lỗi trong legacy VAD: {str(e)}")
        return False

def detect_speech_silero(audio_chunk):
    """
    Sử dụng Silero VAD để phát hiện giọng nói trong audio chunk

    Args:
        audio_chunk: numpy array của audio data (16kHz, mono)

    Returns:
        bool: True nếu phát hiện giọng nói, False nếu không
    """
    global vad_model

    if vad_model is None:
        # Fallback về phương pháp cũ nếu VAD không khả dụng
        return is_voice_legacy(audio_chunk)

    try:
        # Chuyển đổi audio chunk thành tensor
        if isinstance(audio_chunk, bytes):
            audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
        else:
            audio_data = audio_chunk

        # Chuẩn hóa audio data về [-1, 1]
        audio_tensor = torch.from_numpy(audio_data.astype(np.float32) / 32768.0)

        # Silero VAD luôn chạy trên CPU, không cần chuyển device
        audio_tensor = audio_tensor.cpu()

        # Silero VAD yêu cầu chính xác 512 samples cho 16kHz
        required_samples = 512
        
        if len(audio_tensor) > required_samples:
            # Chia audio thành các chunks 512 samples và kiểm tra từng chunk
            speech_detected = False
            for i in range(0, len(audio_tensor), required_samples):
                chunk = audio_tensor[i:i+required_samples]
                
                # Pad chunk nếu cần thiết
                if len(chunk) < required_samples:
                    padding = required_samples - len(chunk)
                    chunk = torch.cat([chunk, torch.zeros(padding)])
                
                # Áp dụng VAD cho chunk này
                speech_prob = vad_model(chunk, 16000).item()
                
                # Nếu bất kỳ chunk nào có giọng nói, trả về True
                if speech_prob > VAD_THRESHOLD:
                    speech_detected = True
                    break
            
            return speech_detected
        else:
            # Pad với zeros nếu quá ngắn
            if len(audio_tensor) < required_samples:
                padding = required_samples - len(audio_tensor)
                audio_tensor = torch.cat([audio_tensor, torch.zeros(padding)])
            
            # Áp dụng VAD
            speech_prob = vad_model(audio_tensor, 16000).item()
            
            # Trả về True nếu xác suất giọng nói > ngưỡng
            return speech_prob > VAD_THRESHOLD
        
    except Exception as e:
        print(f"Lỗi khi sử dụng Silero VAD: {str(e)}")
        # Fallback về phương pháp cũ
        return is_voice_legacy(audio_chunk)



def extract_speaker_embedding(audio_file, device):
    global speaker_classifier
    if speaker_classifier is None:
        try:
            init_speaker_encoder(device)
        except Exception as e:
            print(f"Không thể khởi tạo mô hình nhận dạng giọng nói: {str(e)}")
            return None
    if speaker_classifier is None:
        return None
    try:
        signal, fs = torchaudio.load(audio_file)
        if signal.shape[0] > 1:
            signal = torch.mean(signal, dim=0).unsqueeze(0)
        if fs != 16000:
            signal = torchaudio.functional.resample(signal, fs, 16000)
        signal = signal.to(device)
        embedding = speaker_classifier.encode_batch(signal)
        return embedding.squeeze().cpu().numpy()
    except Exception as e:
        print(f"Lỗi khi trích xuất đặc trưng giọng nói: {str(e)}")
        return None

def delete_embedding_file(embedding_info):
    try:
        embedding_file_path = embedding_info.get('path')
        if embedding_file_path and os.path.exists(embedding_file_path):
            os.remove(embedding_file_path)
            print(f"Đã xóa file embedding: {os.path.basename(embedding_file_path)}")
            return True
        return False
    except Exception as e:
        print(f"Lỗi khi xóa file embedding: {str(e)}")
        return False

def merge_embeddings(embedding1, embedding2, weight1=0.5, weight2=0.5, method="sum"):
    if embedding1 is None or embedding2 is None:
        return embedding1 if embedding1 is not None else embedding2
    if method == "sum":
        merged_embedding = embedding1 + embedding2
    else:
        merged_embedding = weight1 * embedding1 + weight2 * embedding2
    merged_embedding = merged_embedding / np.linalg.norm(merged_embedding)
    return merged_embedding

def save_embedding(name, embedding, text, audio_file=None, notify_created=True):
    if embedding is None:
        print(f"Không thể lưu profile cho {name}: embedding là None")
        return False
    os.makedirs(PROFILE_FOLDER, exist_ok=True)
    profile_info = {
        'name': name,
        'text': text,
        'embedding': embedding.tolist(),
        'created_at': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    if audio_file:
        profile_info['audio_file'] = audio_file
    profile_path = os.path.join(PROFILE_FOLDER, f"{name}.json")
    with open(profile_path, 'w', encoding='utf-8') as f:
        json.dump(profile_info, f, ensure_ascii=False, indent=2)
    print(f"Đã lưu profile cho {name}")
    speaker_embeddings[name] = embedding

    # Phát sự kiện thông báo có người nói mới được tạo để làm mới giao diện
    if notify_created:
        message_queue.publish({"action": "speaker_created", "speaker_name": name}, type='speaker_update')
        print(f"Đã phát sự kiện speaker_created cho {name}")

    return True

def save_embedding_to_database(embedding, text=None, audio_file=None, is_known=False, speaker_name=None):
    global embeddings_database
    if embedding is None:
        return False
    os.makedirs(EMBEDDINGS_FOLDER, exist_ok=True)
    embedding_info = {
        'embedding': embedding.tolist(),
        'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
        'is_known': is_known
    }
    if text:
        embedding_info['text'] = text
    if audio_file:
        embedding_info['audio_file'] = audio_file
    if speaker_name:
        embedding_info['speaker_name'] = speaker_name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
    embedding_path = os.path.join(EMBEDDINGS_FOLDER, f"embedding_{timestamp}.json")
    try:
        with open(embedding_path, 'w', encoding='utf-8') as f:
            json.dump(embedding_info, f, ensure_ascii=False, indent=2)
        embeddings_database.append({
            'embedding': embedding,
            'path': embedding_path,
            'is_known': is_known,
            'speaker_name': speaker_name
        })
        return True
    except Exception as e:
        print(f"Lỗi khi lưu embedding vào cơ sở dữ liệu: {str(e)}")
        return False

def load_all_embeddings():
    global embeddings_database
    if not os.path.exists(EMBEDDINGS_FOLDER):
        os.makedirs(EMBEDDINGS_FOLDER, exist_ok=True)
        return
    embedding_files = [f for f in os.listdir(EMBEDDINGS_FOLDER) if f.endswith('.json')]
    if not embedding_files:
        print("Chưa có embedding nào được lưu trong cơ sở dữ liệu.")
        return
    print(f"Đang tải {len(embedding_files)} embeddings từ cơ sở dữ liệu...")
    loaded_count = 0
    for file in embedding_files:
        try:
            embedding_path = os.path.join(EMBEDDINGS_FOLDER, file)
            with open(embedding_path, 'r', encoding='utf-8') as f:
                embedding_info = json.load(f)
            embedding = np.array(embedding_info['embedding'])
            is_known = embedding_info.get('is_known', False)
            speaker_name = embedding_info.get('speaker_name', None)
            embeddings_database.append({
                'embedding': embedding,
                'path': embedding_path,
                'is_known': is_known,
                'speaker_name': speaker_name
            })
            loaded_count += 1
        except Exception as e:
            print(f"Lỗi khi tải embedding {file}: {str(e)}")
    print(f"Tải embeddings hoàn tất. Đã tải {loaded_count}/{len(embedding_files)} embeddings.")

def get_next_available_speaker_number():
    """Lấy số tiếp theo có thể sử dụng cho tên 'Người Nói X'"""
    global unknown_speaker_count
    
    # Kiểm tra tất cả các tên đang được sử dụng
    used_numbers = set()
    
    # Kiểm tra trong memory
    for name in speaker_embeddings.keys():
        if name.startswith("Người Nói "):
            try:
                parts = name.split(" ")
                if len(parts) >= 3:
                    number = int(parts[2])
                    used_numbers.add(number)
            except:
                continue
    
    # Kiểm tra trên disk
    if os.path.exists(PROFILE_FOLDER):
        for filename in os.listdir(PROFILE_FOLDER):
            if filename.endswith('.json'):
                try:
                    profile_path = os.path.join(PROFILE_FOLDER, filename)
                    with open(profile_path, 'r', encoding='utf-8') as f:
                        profile = json.load(f)
                        name = profile.get('name', '')
                        if name.startswith("Người Nói "):
                            parts = name.split(" ")
                            if len(parts) >= 3:
                                number = int(parts[2])
                                used_numbers.add(number)
                except:
                    continue
    
    # Tìm số tiếp theo chưa được sử dụng
    next_number = 1
    while next_number in used_numbers:
        next_number += 1
    
    # Cập nhật counter toàn cục
    unknown_speaker_count = max(unknown_speaker_count, next_number)
    
    print(f"🔢 Số được sử dụng: {sorted(used_numbers)}")
    print(f"🔢 Số tiếp theo available: {next_number}")
    
    return next_number

def is_profile_name_reserved(name):
    """Kiểm tra xem tên profile có phải là tên dành riêng không"""
    reserved_names = [
        "Người lạ", "Chờ xác định người nói", "Lỗi nhận dạng", 
        "Người đã gặp, chưa đặt tên", "không tồn tại", "lỗi tải"
    ]
    return name in reserved_names

def update_unknown_speaker_count():
    """Cập nhật lại unknown_speaker_count dựa trên các profile hiện có"""
    global unknown_speaker_count
    max_number = 0
    
    # Kiểm tra cả trong speaker_embeddings và trên disk
    all_speaker_names = set(speaker_embeddings.keys())
    
    # Thêm từ các file JSON profile trên disk
    if os.path.exists(PROFILE_FOLDER):
        for filename in os.listdir(PROFILE_FOLDER):
            if filename.endswith('.json'):
                try:
                    profile_path = os.path.join(PROFILE_FOLDER, filename)
                    with open(profile_path, 'r', encoding='utf-8') as f:
                        profile = json.load(f)
                        speaker_name = profile.get('name', filename[:-5])  # Fallback to filename without .json
                        all_speaker_names.add(speaker_name)
                except:
                    continue
    
    # Tìm số lớn nhất trong các tên "Người Nói X"
    for name in all_speaker_names:
        if name.startswith("Người Nói "):
            try:
                parts = name.split(" ")
                if len(parts) >= 3:  # "Người", "Nói", "X"
                    number = int(parts[2])
                    max_number = max(max_number, number)
            except (ValueError, IndexError):
                continue
    
    # Chỉ cập nhật unknown_speaker_count nếu giá trị mới lớn hơn giá trị hiện tại
    # Điều này tránh việc giảm counter và gây xung đột khi tạo profile mới
    if max_number > unknown_speaker_count:
        unknown_speaker_count = max_number
        print(f"Đã cập nhật unknown_speaker_count = {unknown_speaker_count}")
    else:
        print(f"Giữ nguyên unknown_speaker_count = {unknown_speaker_count} (max found: {max_number})")

def load_all_profiles():
    global speaker_embeddings, unknown_speaker_count
    if not os.path.exists(PROFILE_FOLDER):
        os.makedirs(PROFILE_FOLDER, exist_ok=True)
        return
    
    # Đọc profile JSON files
    profile_files = [f for f in os.listdir(PROFILE_FOLDER) if f.endswith('.json')]
    
    # Đọc legacy .npy files từ thư mục embeddings (backward compatibility)
    legacy_npy_files = []
    if os.path.exists(EMBEDDINGS_FOLDER):
        legacy_npy_files = [f for f in os.listdir(EMBEDDINGS_FOLDER) if f.endswith('.npy')]
    
    total_files = len(profile_files) + len(legacy_npy_files)
    
    if total_files == 0:
        print("Chưa có profile nào được lưu.")
        return
        
    print(f"Đang tải {total_files} profile ({len(profile_files)} JSON, {len(legacy_npy_files)} NPY legacy)...")
    auto_profile_count = 0
    
    # Tải profile JSON files
    for file in profile_files:
        try:
            profile_path = os.path.join(PROFILE_FOLDER, file)
            with open(profile_path, 'r', encoding='utf-8') as f:
                profile = json.load(f)
            name = profile['name']
            embedding = np.array(profile['embedding'])
            speaker_embeddings[name] = embedding
            # Xử lý định dạng tên để tìm số lớn nhất
            if name.startswith("Người Nói "):
                try:
                    number = int(name.split(" ")[2])
                    auto_profile_count = max(auto_profile_count, number)
                except:
                    pass
            print(f"  - Đã tải profile JSON: {name}")
        except Exception as e:
            print(f"Lỗi khi tải profile {file}: {str(e)}")
    
    # Tải legacy .npy files và chuyển đổi thành JSON
    for file in legacy_npy_files:
        try:
            npy_path = os.path.join(EMBEDDINGS_FOLDER, file)
            embedding = np.load(npy_path)
            name = file.replace('.npy', '')
            
            # Kiểm tra xem đã có profile JSON cho name này chưa
            json_path = os.path.join(PROFILE_FOLDER, f"{name}.json")
            if not os.path.exists(json_path):
                # Tạo profile JSON từ .npy file
                speaker_embeddings[name] = embedding
                
                # Tạo file JSON để tương thích
                profile_info = {
                    'name': name,
                    'text': 'Được chuyển đổi từ file NPY legacy',
                    'embedding': embedding.tolist(),
                    'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'converted_from_npy': True
                }
                
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(profile_info, f, ensure_ascii=False, indent=2)
                
                print(f"  - Đã chuyển đổi NPY -> JSON: {name}")
                
                # Xóa file .npy cũ sau khi chuyển đổi thành công
                os.remove(npy_path)
                print(f"  - Đã xóa file NPY legacy: {file}")
            else:
                print(f"  - Bỏ qua NPY (đã có JSON): {name}")
                # Xóa file .npy thừa vì đã có JSON
                os.remove(npy_path)
            
            # Xử lý định dạng tên để tìm số lớn nhất
            if name.startswith("Người Nói "):
                try:
                    number = int(name.split(" ")[2])
                    auto_profile_count = max(auto_profile_count, number)
                except:
                    pass
                    
        except Exception as e:
            print(f"Lỗi khi tải legacy NPY {file}: {str(e)}")
    
    unknown_speaker_count = auto_profile_count
    print(f"Tải profile hoàn tất. Có {len(speaker_embeddings)} profile.")
    
    # Hiển thị cài đặt ngưỡng
    display_threshold_settings()

def find_similar_embedding_in_database(embedding, threshold=SIMILARITY_THRESHOLD):
    global embeddings_database
    if embedding is None or not embeddings_database:
        return None, 0.0
    best_match = None
    highest_similarity = 0.0
    for stored_embedding_info in embeddings_database:
        try:
            stored_embedding = stored_embedding_info['embedding']
            similarity = 1.0 - cosine(embedding, stored_embedding)
            if similarity > highest_similarity:
                highest_similarity = similarity
                best_match = stored_embedding_info
        except Exception as e:
            continue
    if highest_similarity >= threshold:
        return best_match, highest_similarity
    return None, highest_similarity

def find_matching_profile(embedding, text=None, audio_file=None, auto_create_if_no_match=False):
    """
    Tìm profile khớp nhất với embedding.
    Nếu có danh sách speakers được chọn, chỉ so sánh với những speakers đó.
    Nếu không đạt ngưỡng và auto_create_if_no_match=True, sẽ tự động tạo profile mới.

    Args:
        embedding: Speaker embedding để so sánh
        text: Văn bản (cần thiết cho việc tạo profile tự động)
        audio_file: File audio (cần thiết cho việc tạo profile tự động)
        auto_create_if_no_match: Có tự động tạo profile mới nếu không khớp

    Returns:
        tuple: (best_match_name, similarity)
    """
    if embedding is None or not speaker_embeddings:
        # Nếu không có profiles nào và được phép tạo tự động
        if auto_create_if_no_match and text and audio_file and auto_profile_creation:
            word_count = len(text.split()) if text else 0
            if word_count > 7:
                print(f"🆕 Không có profiles nào, tạo profile đầu tiên với {word_count} từ")
                new_profile_name = create_auto_profile(embedding, text, audio_file)
                if new_profile_name:
                    return new_profile_name, 1.0  # Similarity cao vì là profile mới tạo
        return None, 0.0

    # Lấy danh sách speakers để so sánh
    with selected_speakers_lock:
        speakers_to_compare = selected_speakers_for_comparison.copy() if selected_speakers_for_comparison else set(speaker_embeddings.keys())

    # Nếu không có speakers nào được chọn, so sánh với tất cả
    if not speakers_to_compare:
        speakers_to_compare = set(speaker_embeddings.keys())

    best_match = None
    highest_similarity = 0.0

    # So sánh với speakers được chọn
    for name, stored_embedding in speaker_embeddings.items():
        # Chỉ so sánh với speakers được chọn
        if name not in speakers_to_compare:
            continue

        try:
            similarity = 1.0 - cosine(embedding, stored_embedding)
            if similarity > highest_similarity:
                highest_similarity = similarity
                best_match = name
        except:
            continue

    # Kiểm tra xem có đạt ngưỡng không
    if best_match and highest_similarity >= SIMILARITY_THRESHOLD:
        print(f"✅ Tìm thấy match: {best_match} (similarity: {highest_similarity:.3f})")
        return best_match, highest_similarity

    # Không đạt ngưỡng - xem xét tạo profile tự động
    if auto_create_if_no_match and text and audio_file and auto_profile_creation:
        word_count = len(text.split()) if text else 0

        if word_count > 7:
            print(f"🔍 Không đạt ngưỡng similarity ({highest_similarity:.3f} < {SIMILARITY_THRESHOLD})")
            print(f"🆕 Tạo profile tự động với {word_count} từ...")

            new_profile_name = create_auto_profile(embedding, text, audio_file)
            if new_profile_name:
                print(f"✅ Đã tạo profile mới: {new_profile_name}")
                return new_profile_name, 1.0  # Similarity cao vì là profile mới tạo
            else:
                print(f"❌ Không thể tạo profile tự động")
        else:
            print(f"⚠️ Văn bản quá ngắn ({word_count} từ, cần > 7 từ) - không tạo profile")

    return best_match, highest_similarity




def update_profile_embedding(profile_name, new_embedding, weight_new=0.3, weight_old=0.7):
    """Cập nhật embedding cho profile - chỉ khi vượt ngưỡng EMBEDDING_MERGE_THRESHOLD"""
    if profile_name not in speaker_embeddings or new_embedding is None:
        return False
    
    old_embedding = speaker_embeddings[profile_name]
    
    # Kiểm tra độ tương đồng trước khi gộp
    try:
        similarity = 1.0 - cosine(new_embedding, old_embedding)
        
        if similarity < EMBEDDING_MERGE_THRESHOLD:
            print(f"[Profile Update] Bỏ qua cập nhật profile '{profile_name}': độ tương đồng ({similarity:.3f}) < ngưỡng gộp ({EMBEDDING_MERGE_THRESHOLD})")
            return False
            
        print(f"[Profile Update] Cập nhật profile '{profile_name}' với similarity: {similarity:.3f}")
        
    except Exception as e:
        print(f"[Profile Update] Lỗi khi tính similarity cho '{profile_name}': {str(e)}")
        return False
    
    merged_embedding = merge_embeddings(old_embedding, new_embedding, method="sum")
    profile_path = os.path.join(PROFILE_FOLDER, f"{profile_name}.json")
    try:
        with open(profile_path, 'r', encoding='utf-8') as f:
            profile = json.load(f)
        profile['embedding'] = merged_embedding.tolist()
        profile['updated_at'] = time.strftime('%Y-%m-%d %H:%M:%S')
        with open(profile_path, 'w', encoding='utf-8') as f:
            json.dump(profile, f, ensure_ascii=False, indent=2)
        speaker_embeddings[profile_name] = merged_embedding
        print(f"Đã cập nhật profile {profile_name} với embedding mới (similarity: {similarity:.3f})")
        return True
    except Exception as e:
        print(f"Lỗi khi cập nhật profile {profile_name}: {str(e)}")
        return False



def update_speaker_profile_auto(speaker_name, new_embedding):
    """Tự động cập nhật profile của người nói với embedding mới - chỉ khi vượt ngưỡng EMBEDDING_MERGE_THRESHOLD"""
    if speaker_name and speaker_name in speaker_embeddings and new_embedding is not None:
        try:
            old_embedding = speaker_embeddings[speaker_name]
            
            # Kiểm tra độ tương đồng trước khi gộp
            similarity = 1.0 - cosine(new_embedding, old_embedding)
            
            if similarity < EMBEDDING_MERGE_THRESHOLD:
                print(f"[Auto Update] Bỏ qua cập nhật profile '{speaker_name}': độ tương đồng ({similarity:.3f}) < ngưỡng gộp ({EMBEDDING_MERGE_THRESHOLD})")
                return False
                
            merged_embedding = merge_embeddings(old_embedding, new_embedding, weight1=0.8, weight2=0.2)
            
            profile_path = os.path.join(PROFILE_FOLDER, f"{speaker_name}.json")
            if os.path.exists(profile_path):
                with open(profile_path, 'r', encoding='utf-8') as f:
                    profile = json.load(f)
                
                profile['embedding'] = merged_embedding.tolist()
                profile['last_updated'] = time.strftime('%Y-%m-%d %H:%M:%S')
                
                with open(profile_path, 'w', encoding='utf-8') as f:
                    json.dump(profile, f, ensure_ascii=False, indent=2)
                
                speaker_embeddings[speaker_name] = merged_embedding
                print(f"[Auto Update] Đã cập nhật profile '{speaker_name}' với embedding mới (similarity: {similarity:.3f})")
                return True
        except Exception as e:
            print(f"[Auto Update] Lỗi khi cập nhật profile '{speaker_name}': {str(e)}")
    return False

def display_recording_status():
    """Hiển thị trạng thái ghi âm liên tục hiện tại"""
    print("🎙️ TRẠNG THÁI GHI ÂM LIÊN TỤC:")
    print(f"   ✅ Ghi âm liên tục: ĐÃ BẬT")
    print(f"   ✅ Phát hiện VAD (Voice Activity Detection): ĐÃ BẬT")
    print(f"   ✅ Phát hiện thay đổi người nói: ĐÃ BẬT")
    print(f"   📊 Ngưỡng VAD: {VAD_THRESHOLD}")
    print(f"   ⏱️  Thời gian tối thiểu giọng nói: {VAD_MIN_SPEECH_DURATION}s")
    print(f"   ⏸️  Thời gian tối thiểu im lặng: {VAD_MIN_SILENCE_DURATION}s")
    print(f"   🎯 Ngưỡng nhận dạng người nói: {SIMILARITY_THRESHOLD}")
    print(f"   🔄 Tạo profile tự động: {'ĐÃ BẬT' if auto_profile_creation else 'TẮT'}")
    print()

def display_speaker_statistics():
    """Hiển thị thống kê về các người nói đã được phát hiện"""
    print("\n" + "="*50)
    print("📊 THỐNG KÊ NGƯỜI NÓI")
    print("="*50)
    
    if speaker_embeddings:
        print(f"Tổng số profile đã lưu: {len(speaker_embeddings)}")
        print("Danh sách người nói:")
        for i, name in enumerate(speaker_embeddings.keys(), 1):
            print(f"  {i}. {name}")
    else:
        print("Chưa có profile nào được lưu")
    
    if embeddings_database:
        known_count = sum(1 for emb in embeddings_database if emb.get('is_known', False))
        unknown_count = len(embeddings_database) - known_count
        print(f"\nTổng số embedding trong database: {len(embeddings_database)}")
        print(f"  - Embedding đã biết: {known_count}")
        print(f"  - Embedding chưa biết: {unknown_count}")
    else:
        print("\nChưa có embedding nào trong database")
    
    print("="*50)

def log_speaker_change_event(old_speaker, new_speaker, similarity, confidence_level="HIGH"):
    """Ghi log chi tiết về sự kiện thay đổi người nói"""
    timestamp = time.strftime('%H:%M:%S')
    
    if old_speaker and new_speaker and old_speaker != new_speaker:
        print(f"🔄 [{timestamp}] THAY ĐỔI NGƯỜI NÓI: '{old_speaker}' ➜ '{new_speaker}' (similarity: {similarity:.3f}, confidence: {confidence_level})")
    elif new_speaker and not old_speaker:
        print(f"🆕 [{timestamp}] NGƯỜI NÓI MỚI: '{new_speaker}' (similarity: {similarity:.3f}, confidence: {confidence_level})")
    elif old_speaker and new_speaker and old_speaker == new_speaker:
        print(f"✅ [{timestamp}] TIẾP TỤC: '{new_speaker}' (similarity: {similarity:.3f})")
    else:
        print(f"❓ [{timestamp}] KHÔNG XÁC ĐỊNH (similarity: {similarity:.3f})")

def get_confidence_level(similarity, threshold):
    """Xác định mức độ tin cậy dựa trên similarity score"""
    if similarity >= threshold + 0.2:
        return "VERY HIGH"
    elif similarity >= threshold + 0.1:
        return "HIGH" 
    elif similarity >= threshold:
        return "MEDIUM"
    else:
        return "LOW"

def display_threshold_settings():
    """Hiển thị tất cả ngưỡng hiện tại đang sử dụng"""
    print("\n" + "="*50)
    print("        CÀI ĐẶT NGƯỮNG HIỆN TẠI")
    print("="*50)
    print(f"📊 Ngưỡng nhận dạng chính (profile):     {SIMILARITY_THRESHOLD:.3f}")
    print(f"🤖 Ngưỡng tạo profile tự động:          {AUTO_PROFILE_SIMILARITY_THRESHOLD:.3f}")
    print(f"🔗 Ngưỡng gộp embedding:                {EMBEDDING_MERGE_THRESHOLD:.3f}")
    print("="*50)
    print("📝 Ghi chú:")
    print("   - Ngưỡng chính cho độ chính xác profile")
    print("   - Ngưỡng tự động cao để tránh tạo profile sai")
    print("   - Ngưỡng gộp embedding bảo vệ profile khỏi bị đè sai")
    print("="*50 + "\n")

def check_embedding_merge_eligibility(speaker_name, new_embedding):
    """Kiểm tra xem embedding mới có đủ điều kiện để gộp vào profile không"""
    if speaker_name not in speaker_embeddings or new_embedding is None:
        return False, 0.0, "Profile không tồn tại hoặc embedding None"
    
    old_embedding = speaker_embeddings[speaker_name]
    try:
        similarity = 1.0 - cosine(new_embedding, old_embedding)
        
        if similarity >= EMBEDDING_MERGE_THRESHOLD:
            return True, similarity, f"Đủ điều kiện gộp (similarity: {similarity:.3f} ≥ {EMBEDDING_MERGE_THRESHOLD})"
        else:
            return False, similarity, f"Không đủ điều kiện gộp (similarity: {similarity:.3f} < {EMBEDDING_MERGE_THRESHOLD})"
            
    except Exception as e:
        return False, 0.0, f"Lỗi khi tính similarity: {str(e)}"

def get_next_audio_filename():
    global CURRENT_AUDIO_FILE_INDEX
    with AUDIO_FILE_LOCK:
        CURRENT_AUDIO_FILE_INDEX = (CURRENT_AUDIO_FILE_INDEX + 1) % MAX_AUDIO_FILES
        filename = os.path.join(WAVE_OUTPUT_FOLDER, f"chunk_{CURRENT_AUDIO_FILE_INDEX}.wav")
    return filename



@torch.no_grad()
def endless_decode(args, model, char_dict, audio_path, no_color=False):
    try:
        def get_max_input_context(c, r, n):
            return r + max(c, r) * (n-1)

        device = next(model.parameters()).device

        subsampling_factor = model.encoder.embed.subsampling_factor
        chunk_size = args.chunk_size
        left_context_size = args.left_context_size
        right_context_size = args.right_context_size
        conv_lorder = model.encoder.cnn_module_kernel // 2

        max_length_limited_context = args.total_batch_duration
        max_length_limited_context = int((max_length_limited_context // 0.01))//2

        multiply_n = max_length_limited_context // chunk_size // subsampling_factor
        truncated_context_size = chunk_size * multiply_n

        rel_right_context_size = get_max_input_context(chunk_size, max(right_context_size, conv_lorder), model.encoder.num_blocks)
        rel_right_context_size = rel_right_context_size * subsampling_factor

        # Kiểm tra file audio tồn tại
        if not os.path.exists(audio_path):
            print(f"File audio không tồn tại: {audio_path}")
            return ""

        waveform = load_audio(audio_path)
        if waveform is None or len(waveform) == 0:
            print(f"Không thể load audio từ file: {audio_path}")
            return ""

        offset = torch.zeros(1, dtype=torch.int, device=device)

        xs = kaldi.fbank(waveform,
                                num_mel_bins=80,
                                frame_length=25,
                                frame_shift=10,
                                dither=0.0,
                                energy_floor=0.0,
                                sample_frequency=16000).unsqueeze(0)

        if xs.shape[1] == 0:
            print("Audio features rỗng, không thể xử lý")
            return ""
    except Exception as e:
        print(f"Lỗi khởi tạo endless_decode: {str(e)}")
        return ""

    hyps = []
    att_cache = torch.zeros((model.encoder.num_blocks, left_context_size, model.encoder.attention_heads, model.encoder._output_size * 2 // model.encoder.attention_heads)).to(device)
    cnn_cache = torch.zeros((model.encoder.num_blocks, model.encoder._output_size, conv_lorder)).to(device)

    try:
        # Sử dụng tqdm với error handling
        range_list = list(enumerate(range(0, xs.shape[1], truncated_context_size * subsampling_factor)))
        for idx, _ in tqdm(range_list, desc="Processing audio chunks"):
            start = max(truncated_context_size * subsampling_factor * idx, 0)
            end = min(truncated_context_size * subsampling_factor * (idx+1) + 7, xs.shape[1])

            x = xs[:, start:end+rel_right_context_size]
            x_len = torch.tensor([x[0].shape[0]], dtype=torch.int).to(device)

            encoder_outs, encoder_lens, _, att_cache, cnn_cache, offset = model.encoder.forward_parallel_chunk(xs=x,
                                                                        xs_origin_lens=x_len,
                                                                        chunk_size=chunk_size,
                                                                        left_context_size=left_context_size,
                                                                        right_context_size=right_context_size,
                                                                        att_cache=att_cache,
                                                                        cnn_cache=cnn_cache,
                                                                        truncated_context_size=truncated_context_size,
                                                                        offset=offset
                                                                        )
            encoder_outs = encoder_outs.reshape(1, -1, encoder_outs.shape[-1])[:, :encoder_lens]
            if chunk_size * multiply_n * subsampling_factor * idx + rel_right_context_size < xs.shape[1]:
                encoder_outs = encoder_outs[:, :truncated_context_size]
            offset = offset - encoder_lens + encoder_outs.shape[1]

            hyp = model.encoder.ctc_forward(encoder_outs).squeeze(0)
            hyps.append(hyp)
            if device.type == "cuda":
                torch.cuda.empty_cache()
            if chunk_size * multiply_n * subsampling_factor * idx + rel_right_context_size >= xs.shape[1]:
                break
    except Exception as e:
        print(f"Lỗi trong quá trình xử lý audio chunks: {str(e)}")
        # Nếu có lỗi, thử xử lý mà không có progress bar
        for idx, _ in enumerate(range(0, xs.shape[1], truncated_context_size * subsampling_factor)):
            try:
                start = max(truncated_context_size * subsampling_factor * idx, 0)
                end = min(truncated_context_size * subsampling_factor * (idx+1) + 7, xs.shape[1])

                x = xs[:, start:end+rel_right_context_size]
                x_len = torch.tensor([x[0].shape[0]], dtype=torch.int).to(device)

                encoder_outs, encoder_lens, _, att_cache, cnn_cache, offset = model.encoder.forward_parallel_chunk(xs=x,
                                                                            xs_origin_lens=x_len,
                                                                            chunk_size=chunk_size,
                                                                            left_context_size=left_context_size,
                                                                            right_context_size=right_context_size,
                                                                            att_cache=att_cache,
                                                                            cnn_cache=cnn_cache,
                                                                            truncated_context_size=truncated_context_size,
                                                                            offset=offset
                                                                            )
                encoder_outs = encoder_outs.reshape(1, -1, encoder_outs.shape[-1])[:, :encoder_lens]
                if chunk_size * multiply_n * subsampling_factor * idx + rel_right_context_size < xs.shape[1]:
                    encoder_outs = encoder_outs[:, :truncated_context_size]
                offset = offset - encoder_lens + encoder_outs.shape[1]

                hyp = model.encoder.ctc_forward(encoder_outs).squeeze(0)
                hyps.append(hyp)
                if device.type == "cuda":
                    torch.cuda.empty_cache()
                if chunk_size * multiply_n * subsampling_factor * idx + rel_right_context_size >= xs.shape[1]:
                    break
            except Exception as inner_e:
                print(f"Lỗi khi xử lý chunk {idx}: {str(inner_e)}")
                continue

    # Kiểm tra xem có hyps nào không
    if not hyps:
        print("Không có dữ liệu audio để xử lý")
        return ""

    try:
        hyps = torch.cat(hyps)
        decode = get_output_with_timestamps([hyps], char_dict)[0]

        result_text = ""
        for item in decode:
            start_time = item['start']
            end_time = item['end']
            line = f"{start_time} - {end_time}: {item['decode']}"
            result_text += item['decode'] + " "
            print(line)

        result_text = result_text.strip()
        return result_text
    except Exception as e:
        print(f"Lỗi khi decode kết quả: {str(e)}")
        return ""

def record_audio(args):
    global is_running, audio_queue
    global speaker_classifier, embeddings_database, speaker_embeddings
    
    p = pyaudio.PyAudio()
    stream = p.open(format=FORMAT,
                    channels=CHANNELS,
                    rate=RATE,
                    input=True,
                    frames_per_buffer=CHUNK)
    
    print("* Đang lắng nghe với Silero VAD và phát hiện thay đổi giọng nói... Nhấn Ctrl+C để dừng.")
    
    # Hiển thị thông tin cấu hình
    print(f"📋 Cấu hình phát hiện:")
    print(f"   - Ngưỡng tương đồng: {SIMILARITY_THRESHOLD}")
    print(f"   - Ngưỡng VAD: {VAD_THRESHOLD}")
    
    # Hiển thị thống kê ban đầu
    display_speaker_statistics()
    
    chunk_count = 0
    recording = False
    frames = []
    silent_chunks = 0
    
    # Buffer để tích lũy audio cho VAD
    effective_min_speech = VAD_MIN_SPEECH_DURATION
    effective_min_silence = VAD_MIN_SILENCE_DURATION
    print(f"📝 Chế độ xử lý theo đoạn: BẬT")
    print(f"   📊 Thời gian tối thiểu giọng nói: {effective_min_speech}s")
    print(f"   📊 Thời gian tối thiểu im lặng: {effective_min_silence}s")

    audio_buffer = deque(maxlen=int(effective_min_speech * RATE / CHUNK))
    speech_chunks_count = 0
    min_speech_chunks = int(effective_min_speech * RATE / CHUNK)
    min_silence_chunks = int(effective_min_silence * RATE / CHUNK)

    # Pre-recording buffer để không mất chữ đầu câu
    pre_recording_buffer = deque(maxlen=PRE_RECORDING_BUFFER_SIZE)
    print(f"📋 Pre-recording buffer: {PRE_RECORDING_DURATION}s ({PRE_RECORDING_BUFFER_SIZE} chunks)")
    
    voice_start_time = None
    
    # ===== SIMPLIFIED AUDIO PROCESSING =====

    # Legacy variables for compatibility
    embedding_buffer = []
    embedding_buffer_duration = 1.2  # Lưu 1.2 giây audio để kiểm tra
    embedding_buffer_size = int(embedding_buffer_duration * RATE / CHUNK)

    # QUAN TRỌNG: Buffer để lưu audio overlap khi chuyển người nói
    overlap_buffer_duration = 2.0  # Lưu 2 giây audio gần nhất
    overlap_buffer_size = int(overlap_buffer_duration * RATE / CHUNK)
    overlap_buffer = deque(maxlen=overlap_buffer_size)





    while is_running:
        data = stream.read(CHUNK, exception_on_overflow=False)



        # ===== OPTIMIZED AUDIO PROCESSING (CHẾ ĐỘ THƯỜNG) =====

        # Luôn thêm vào overlap buffer
        overlap_buffer.append(data)

        # Áp dụng tiền xử lý âm thanh để cải thiện chất lượng
        processed_data = apply_audio_preprocessing(data, RATE)

        # Sử dụng enhanced VAD để phát hiện giọng nói
        voice_detected = enhance_vad_detection(processed_data)

        # Luôn thêm vào pre-recording buffer để không mất chữ đầu câu
        pre_recording_buffer.append(data)

        # Thêm vào buffer
        audio_buffer.append(data)

        if voice_detected:
            speech_chunks_count += 1

            # ===== PYANNOTE DIARIZATION WILL BE USED INSTEAD =====
            # Chức năng embedding check liên tục đã được bỏ
            # Sẽ sử dụng pyannote speaker diarization sau khi ghi âm hoàn tất

            # Thêm vào embedding buffer và frames nếu đang ghi âm
            if recording:
                frames.append(data)
                embedding_buffer.append(data)

                # Giới hạn kích thước embedding buffer
                if len(embedding_buffer) > embedding_buffer_size * 2:
                    embedding_buffer = embedding_buffer[-embedding_buffer_size:]
            
            if not recording and speech_chunks_count >= min_speech_chunks:
                # Bắt đầu ghi âm chỉ sau khi phát hiện đủ chunks có giọng nói
                recording = True
                voice_start_time = time.time()

                # Hiển thị thông tin bắt đầu ghi âm
                print(f"[Enhanced VAD] Bắt đầu ghi âm đoạn {chunk_count}")

                # QUAN TRỌNG: Sử dụng pre-recording buffer để không mất chữ đầu câu
                frames = list(pre_recording_buffer) + list(audio_buffer)
                embedding_buffer = list(pre_recording_buffer) + list(audio_buffer)

                print(f"📋 Đã thêm {len(pre_recording_buffer)} chunks từ pre-recording buffer")
                print(f"📋 Tổng cộng {len(frames)} chunks để xử lý")


                
            if recording:
                silent_chunks = 0
        else:
            speech_chunks_count = max(0, speech_chunks_count - 1)  # Giảm dần số lượng speech chunks
            
            if recording:
                frames.append(data)
                embedding_buffer.append(data)
                silent_chunks += 1
                
                # Dừng ghi âm sau khi có đủ chunks im lặng
                if silent_chunks >= min_silence_chunks:
                    # Hiển thị thông tin tổng kết
                    duration = time.time() - voice_start_time if voice_start_time else 0
                    print(f"[Silero VAD] Kết thúc ghi âm đoạn {chunk_count} (Thời gian: {duration:.1f}s)")
                    
                    print(f"🔍 Debug: frames={len(frames) if frames else 0} chunks, min_required={min_speech_chunks}")
                    if frames and len(frames) > min_speech_chunks:
                        for attempt in range(3):
                            try:
                                filename = get_next_audio_filename()
                                os.makedirs(os.path.dirname(filename), exist_ok=True)
                                wf = wave.open(filename, 'wb')
                                wf.setnchannels(CHANNELS)
                                wf.setsampwidth(p.get_sample_size(FORMAT))
                                wf.setframerate(RATE)
                                wf.writeframes(b''.join(frames))
                                wf.close()
                                audio_queue.put(filename)
                                break
                            except PermissionError:
                                print(f"Không thể ghi file {filename}, đang thử file khác...")
                                time.sleep(0.1)
                            except Exception as e:
                                print(f"Lỗi khi ghi file âm thanh: {str(e)}")
                                break
                    chunk_count += 1
                    recording = False
                    frames = []
                    embedding_buffer = []
                    speech_chunks_count = 0

    
    print("Đã dừng ghi âm.")

    stream.stop_stream()
    stream.close()
    p.terminate()
    
def create_auto_profile(embedding, text, audio_file):
    global unknown_speaker_count, embeddings_database
    word_count = len(text.split())
    if word_count <= 7:
        print(f"Bỏ qua tạo profile: Văn bản quá ngắn ({word_count} từ, cần > 7 từ)")
        return None
    
    similar_embedding, similarity = find_similar_embedding_in_database(embedding, AUTO_PROFILE_SIMILARITY_THRESHOLD)
    if similar_embedding is not None:
        if similarity >= EMBEDDING_MERGE_THRESHOLD:
            print(f"Phát hiện cùng một người nói với độ tương đồng cao: {similarity:.2f}")
            if delete_embedding_file(similar_embedding):
                embeddings_database = [e for e in embeddings_database if e.get('path') != similar_embedding.get('path')]
                print("Đã xóa embedding cũ khỏi cơ sở dữ liệu")
            if similar_embedding.get('is_known', False) and similar_embedding.get('speaker_name'):
                speaker_name = similar_embedding['speaker_name']
                if speaker_name in speaker_embeddings:
                    update_profile_embedding(speaker_name, embedding)
                    print(f"Đã gộp embedding mới vào profile: {speaker_name}")
                    return speaker_name
        elif similarity >= AUTO_PROFILE_SIMILARITY_THRESHOLD:
            print(f"Phát hiện cùng một người nói đã có trong cơ sở dữ liệu (độ tương đồng: {similarity:.2f})")
            if similar_embedding.get('is_known', False) and similar_embedding.get('speaker_name'):
                print(f"Người nói này đã được nhận dạng là: {similar_embedding['speaker_name']}")
                return similar_embedding['speaker_name']    # Sử dụng hàm get_next_available_speaker_number để tránh xung đột
    next_number = get_next_available_speaker_number()
    profile_name = f"Người Nói {next_number}"
    
    # Double-check để đảm bảo tên không bị trùng
    if (profile_name in speaker_embeddings or 
        os.path.exists(os.path.join(PROFILE_FOLDER, f"{profile_name}.json")) or
        is_profile_name_reserved(profile_name)):
        print(f"⚠️ Tên profile {profile_name} vẫn bị trùng, tìm tên khác...")
        # Fallback: tìm số tiếp theo
        for i in range(next_number + 1, next_number + 50):
            fallback_name = f"Người Nói {i}"
            if (fallback_name not in speaker_embeddings and 
                not os.path.exists(os.path.join(PROFILE_FOLDER, f"{fallback_name}.json")) and
                not is_profile_name_reserved(fallback_name)):
                profile_name = fallback_name
                unknown_speaker_count = i
                break
        else:
            print(f"❌ Không thể tìm tên profile phù hợp")
            return None
    
    print(f"🆕 Tạo profile mới với tên: {profile_name} (số: {next_number})")
    
    save_success = save_embedding(profile_name, embedding, text, audio_file, notify_created=True)
    save_embedding_to_database(embedding, text, audio_file, is_known=True, speaker_name=profile_name)
    if save_success:
        print(f"Đã tự động tạo profile cho {profile_name}")
        return profile_name
    return None

def display_similarity_table(embedding):
    if embedding is None or not speaker_embeddings:
        print("Không có dữ liệu để hiển thị bảng độ tin cậy")
        return
    similarities = []
    for name, stored_embedding in speaker_embeddings.items():
        try:
            similarity = 1.0 - cosine(embedding, stored_embedding)
            similarities.append((name, similarity))
        except:
            continue
    similarities.sort(key=lambda x: x[1], reverse=True)
    print("\n===== BẢNG ĐỘ TIN CẬY SO SÁNH =====")
    print("{:<20} {:<10}".format("Tên người nói", "Độ tin cậy"))
    print("-" * 30)
    for name, similarity in similarities:
        marker = ""
        if similarity >= SIMILARITY_THRESHOLD:
            marker = "*"  # Đủ cho nhận dạng profile
        if similarity >= EMBEDDING_MERGE_THRESHOLD:
            marker = "**" # Đủ để gộp embedding
        print("{:<20} {:<.4f} {}".format(name, similarity, marker))
    print("\n* Vượt ngưỡng nhận dạng (≥ {:.2f})".format(SIMILARITY_THRESHOLD))
    print("** Vượt ngưỡng gộp embedding (≥ {:.2f})".format(EMBEDDING_MERGE_THRESHOLD))
    print("=====================================\n")

def clear_embeddings_directory(current_embedding=None):
    global embeddings_database
    try:
        if os.path.exists(EMBEDDINGS_FOLDER):
            print(f"Đang xóa tất cả embeddings trong {EMBEDDINGS_FOLDER}...")
            for file in os.listdir(EMBEDDINGS_FOLDER):
                if file.endswith('.json'):
                    file_path = os.path.join(EMBEDDINGS_FOLDER, file)
                    os.remove(file_path)
            embeddings_database = []
            print("Đã xóa tất cả embeddings")
            if current_embedding is not None:
                display_similarity_table(current_embedding)
    except Exception as e:
        print(f"Lỗi khi xóa embeddings: {str(e)}")

def process_audio(args, model, char_dict, no_color=False):
    global is_running, audio_queue, results, embeddings_database, auto_profile_creation
    global current_speaker, web_results, is_listening_paused

    while is_running or not audio_queue.empty():
        try:
            filename = audio_queue.get(timeout=1)
            if not os.path.exists(filename):
                print(f"File {filename} không tồn tại, bỏ qua...")
                audio_queue.task_done()
                continue
            print(f"Đang xử lý đoạn {os.path.basename(filename)}...")
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
            temp_file.close()
            shutil.copy2(filename, temp_file.name)
            process_filename = temp_file.name

            try:
                # ===== PYANNOTE SPEAKER DIARIZATION =====
                print("🎯 Bắt đầu speaker diarization với pyannote...")

                # Thực hiện speaker diarization
                diarization_segments = perform_speaker_diarization(process_filename)

                if diarization_segments:
                    # Nhận dạng speakers trong từng segment
                    identified_segments = identify_speakers_in_segments(process_filename, diarization_segments)

                    # Nhóm segments liên tiếp theo speaker để gộp văn bản
                    # Lưu ý: merge_same_speaker setting chỉ áp dụng cho "Người lạ"
                    print(f"🔧 merge_same_speaker setting: {'Bật' if merge_same_speaker else 'Tắt'} (chỉ áp dụng cho 'Người lạ')")
                    consecutive_groups = []
                    current_group = None

                    for segment in identified_segments:
                        speaker_name = segment['identified_speaker']

                        # Logic gộp người nói:
                        # - merge_all_speakers: Gộp tất cả người nói cùng tên (không phân biệt liên tiếp hay không)
                        # - merge_same_speaker: Chỉ gộp "Người lạ" liên tiếp

                        if merge_all_speakers:
                            # Tìm nhóm đã có của speaker này để gộp vào
                            existing_group = None
                            for group in consecutive_groups:
                                if group['speaker_name'] == speaker_name:
                                    existing_group = group
                                    break

                            if existing_group:
                                # Gộp vào nhóm đã có
                                existing_group['segments'].append(segment)
                                existing_group['end_time'] = segment['end']
                                continue  # Bỏ qua việc tạo nhóm mới
                            else:
                                # Tạo nhóm mới cho speaker này
                                should_create_new_group = True
                        else:
                            # Logic cũ: chỉ gộp liên tiếp
                            should_create_new_group = (
                                current_group is None or
                                current_group['speaker_name'] != speaker_name or
                                (speaker_name == "Người lạ" and not merge_same_speaker)  # Chỉ áp dụng setting cho "Người lạ"
                            )

                        if should_create_new_group:
                            # Lưu nhóm cũ nếu có
                            if current_group is not None:
                                consecutive_groups.append(current_group)

                            # Log lý do tạo nhóm mới
                            if speaker_name == "Người lạ" and not merge_same_speaker:
                                print(f"📝 Tạo nhóm mới cho {speaker_name} (merge_same_speaker = Tắt cho 'Người lạ')")
                            elif speaker_name == "Người lạ":
                                print(f"📝 Tạo nhóm mới cho {speaker_name} (luôn tách riêng 'Người lạ')")
                            elif current_group is None:
                                print(f"📝 Tạo nhóm đầu tiên cho {speaker_name}")
                            else:
                                print(f"📝 Tạo nhóm mới cho {speaker_name} (khác speaker trước: {current_group['speaker_name']})")

                            # Tạo nhóm mới
                            current_group = {
                                'speaker_name': speaker_name,
                                'segments': [segment],
                                'text_parts': [],
                                'start_time': segment['start'],
                                'end_time': segment['end'],
                                'diarization_speaker': segment['diarization_speaker'],
                                'embedding': segment.get('embedding')
                            }
                        else:
                            # Cùng speaker, thêm vào nhóm hiện tại
                            print(f"📝 Gộp segment {speaker_name} vào nhóm hiện tại")
                            current_group['segments'].append(segment)
                            current_group['end_time'] = segment['end']

                    # Thêm nhóm cuối cùng
                    if current_group is not None:
                        consecutive_groups.append(current_group)

                    # Xử lý từng segment để transcribe và thêm vào nhóm tương ứng
                    for segment in identified_segments:
                        start_time = segment['start']
                        end_time = segment['end']
                        speaker_name = segment['identified_speaker']

                        print(f"📝 Xử lý segment {start_time:.2f}s-{end_time:.2f}s: {speaker_name}")

                        # Create segment audio file
                        segment_audio_file = create_audio_segment(process_filename, start_time, end_time)

                        if segment_audio_file and os.path.exists(segment_audio_file):
                            try:
                                # Transcribe segment
                                segment_result = endless_decode(args, model, char_dict, segment_audio_file, no_color)

                                if segment_result and segment_result.strip():
                                    # Tìm nhóm liên tiếp chứa segment này và thêm văn bản
                                    for group in consecutive_groups:
                                        if segment in group['segments']:
                                            group['text_parts'].append(segment_result.strip())
                                            break

                                    print(f"✅ Segment transcribed: {segment_result.strip()}")

                            except Exception as e:
                                print(f"❌ Lỗi khi transcribe segment {start_time:.2f}s-{end_time:.2f}s: {str(e)}")
                            finally:
                                # Cleanup segment file
                                if os.path.exists(segment_audio_file):
                                    os.unlink(segment_audio_file)

                    # Gộp kết quả theo speaker - chỉ gộp các segments liền kề của cùng speaker
                    print("🔄 Bắt đầu gộp kết quả theo speaker (chỉ gộp segments liền kề)...")
                    current_timestamp = datetime.now().strftime("%H:%M:%S")

                    print(f"📊 Tổng cộng {len(consecutive_groups)} nhóm liền kề để tạo kết quả")

                    # Tạo kết quả cho từng nhóm liền kề (không gộp các nhóm cách xa nhau)
                    for group_info in consecutive_groups:
                        speaker_name = group_info['speaker_name']
                        text_parts = group_info['text_parts']
                        print(f"🔄 Đang tạo kết quả cho {speaker_name}...")
                        try:
                            if text_parts:
                                print(f"📝 Có {len(text_parts)} text parts để gộp")
                                # Gộp tất cả văn bản của speaker này
                                combined_text = ' '.join(text_parts)
                                print(f"📝 Đã gộp text: {len(combined_text)} ký tự")

                                # Tạo kết quả gộp cho speaker này
                                speaker_info = f"[{current_timestamp}] [{speaker_name}] "
                                combined_result = speaker_info + combined_text
                                print(f"📝 Đã tạo combined_result: {len(combined_result)} ký tự")
                                
                                print(f"📝 Đang append vào results...")
                                results.append(combined_result)
                                print(f"📝 Đã append vào results thành công")

                                # Thêm vào web_results với timeout-based approach (không block main thread)
                                print(f"📝 Đang append vào web_results...")
                                web_results_updated = False
                                try:
                                    # Sử dụng timeout-based approach với 0.5 giây timeout
                                    if web_results_lock.acquire(timeout=0.5):
                                        try:
                                            web_results.append(combined_result)
                                            print(f"📝 Đã append vào web_results thành công")
                                            web_results_updated = True
                                        finally:
                                            web_results_lock.release()
                                    else:
                                        print(f"⚠️ web_results_lock timeout (0.5s), kết quả sẽ được sync sau")
                                except Exception as e:
                                    print(f"❌ Lỗi khi append vào web_results: {str(e)}")                                # Nếu không append được vào web_results, lưu vào queue để sync sau
                                if not web_results_updated:
                                    try:
                                        # Chỉ gửi sync signal nếu chưa gửi gần đây (debouncing)
                                        current_time = time.time()
                                        if not hasattr(process_audio, 'last_sync_time') or (current_time - process_audio.last_sync_time) > 5.0:
                                            # Gửi signal để refresh web interface sau
                                            message_queue.publish({
                                                "action": "sync_required",
                                                "new_result": combined_result
                                            }, type='refresh_results')
                                            print(f"📡 Đã gửi sync signal cho web interface")
                                            process_audio.last_sync_time = current_time
                                        else:
                                            print(f"⚠️ Bỏ qua sync signal (debouncing: {current_time - process_audio.last_sync_time:.1f}s)")
                                    except Exception as e:
                                        print(f"⚠️ Không thể gửi sync signal: {str(e)}")

                                # Truncate long text for display
                                display_text = combined_result[:200] + "..." if len(combined_result) > 200 else combined_result
                                print(f"✅ Combined result for {speaker_name}: {display_text}")
                            else:
                                print(f"⚠️ Không có văn bản cho {speaker_name}")
                        except Exception as e:
                            print(f"❌ Lỗi khi tạo kết quả cho {speaker_name}: {str(e)}")
                            import traceback
                            traceback.print_exc()

                    print("✅ Hoàn thành gộp kết quả theo speaker")

                    # Xử lý các speaker chưa xác định - tạo profile hoặc gán "Người lạ"
                    print("🔍 Kiểm tra speakers chưa xác định...")
                    if not is_listening_paused:
                        # Gộp tất cả nhóm của cùng speaker để xử lý
                        speaker_combined_data = {}
                        for group_info in consecutive_groups:
                            speaker_name = group_info['speaker_name']
                            print(f"🔍 Kiểm tra speaker: {speaker_name}")
                            if speaker_name in ["Chờ xác định người nói", "Lỗi nhận dạng"]:
                                print(f"🎯 Tìm thấy speaker chưa xác định: {speaker_name}")
                                if speaker_name not in speaker_combined_data:
                                    speaker_combined_data[speaker_name] = {
                                        'text_parts': [],
                                        'segments': [],
                                        'embedding': group_info['embedding'],
                                        'diarization_speaker': group_info['diarization_speaker'],
                                        'groups': []  # Lưu reference đến các groups để cập nhật tên sau
                                    }
                                speaker_combined_data[speaker_name]['text_parts'].extend(group_info['text_parts'])
                                speaker_combined_data[speaker_name]['segments'].extend(group_info['segments'])
                                speaker_combined_data[speaker_name]['groups'].append(group_info)

                        print(f"📊 Tìm thấy {len(speaker_combined_data)} speakers chưa xác định để xử lý")

                        # Xử lý từng speaker chưa xác định
                        for speaker_name, combined_info in speaker_combined_data.items():
                            print(f"🔄 Đang xử lý speaker chưa xác định: {speaker_name}")
                            # Tổng hợp text từ tất cả text_parts
                            total_text = ' '.join(combined_info['text_parts']).strip()
                            word_count = len(total_text.split()) if total_text else 0
                            print(f"📊 Văn bản: {word_count} từ - '{total_text[:100]}...'")

                            final_speaker_name = "Người lạ"  # Mặc định

                            # Kiểm tra điều kiện tạo profile tự động
                            print(f"🔍 Kiểm tra điều kiện: auto_profile_creation={auto_profile_creation}, word_count={word_count}, embedding={combined_info['embedding'] is not None}")
                            if auto_profile_creation and word_count > 7 and combined_info['embedding'] is not None:
                                print("✅ Đủ điều kiện tạo profile tự động")
                                # Tìm segment dài nhất để làm audio sample
                                longest_segment = max(combined_info['segments'],
                                                    key=lambda s: s['end'] - s['start'])

                                # Tạo audio file cho segment dài nhất
                                segment_audio_file = create_audio_segment(
                                    process_filename,
                                    longest_segment['start'],
                                    longest_segment['end']
                                )

                                if segment_audio_file and os.path.exists(segment_audio_file):
                                    try:
                                        # Copy to unknown_speakers directory
                                        unknown_dir = "unknown_speakers"
                                        os.makedirs(unknown_dir, exist_ok=True)
                                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                        diarization_speaker = combined_info['diarization_speaker']
                                        unknown_file = os.path.join(unknown_dir, f"speaker_{diarization_speaker}_{timestamp}.wav")
                                        shutil.copy(segment_audio_file, unknown_file)

                                        # Tạo profile
                                        new_profile_name = create_auto_profile(
                                            combined_info['embedding'],
                                            total_text,
                                            unknown_file
                                        )

                                        if new_profile_name:
                                            final_speaker_name = new_profile_name
                                            print(f"🆕 Đã tạo profile tự động: {new_profile_name} cho {speaker_name} (từ {len(combined_info['segments'])} segments, {word_count} từ)")
                                        else:
                                            print(f"⚠️ Không thể tạo profile cho {speaker_name}, gán là 'Người lạ'")
                                    except Exception as e:
                                        print(f"❌ Lỗi khi tạo profile cho {speaker_name}: {str(e)}")
                                    finally:
                                        # Cleanup segment file
                                        if os.path.exists(segment_audio_file):
                                            os.unlink(segment_audio_file)
                                else:
                                    print(f"⚠️ Không thể tạo audio file cho {speaker_name}, gán là 'Người lạ'")
                            else:
                                # Thử so sánh embedding với khả năng tự động tạo profile
                                if combined_info.get('embedding') is not None:
                                    print(f"🔍 {speaker_name}: Thử so sánh embedding với profiles hiện có...")

                                    # Tạo audio file tạm để có thể tạo profile nếu cần
                                    segment_audio_file = None
                                    if combined_info['segments']:
                                        longest_segment = max(combined_info['segments'],
                                                            key=lambda s: s['end'] - s['start'])
                                        segment_audio_file = create_audio_segment(
                                            process_filename,
                                            longest_segment['start'],
                                            longest_segment['end']
                                        )

                                    try:
                                        # Sử dụng find_matching_profile với tự động tạo profile (chỉ khi auto_profile_creation=True)
                                        best_match, similarity = find_matching_profile(
                                            combined_info['embedding'],
                                            text=total_text,
                                            audio_file=segment_audio_file,
                                            auto_create_if_no_match=auto_profile_creation  # ← Check setting
                                        )

                                        if best_match and similarity >= SIMILARITY_THRESHOLD:
                                            final_speaker_name = best_match
                                            print(f"✅ {speaker_name}: Nhận dạng thành công → {best_match} (tin cậy: {similarity:.3f})")
                                        else:
                                            final_speaker_name = "Người lạ"
                                            if word_count <= 7:
                                                print(f"⚠️ {speaker_name}: văn bản quá ngắn ({word_count} từ, cần > 7 từ) + similarity thấp ({similarity:.3f} < {SIMILARITY_THRESHOLD}) → gán là 'Người lạ'")
                                            else:
                                                print(f"⚠️ {speaker_name}: similarity thấp ({similarity:.3f} < {SIMILARITY_THRESHOLD}) → gán là 'Người lạ'")
                                    finally:
                                        # Cleanup segment file
                                        if segment_audio_file and os.path.exists(segment_audio_file):
                                            os.unlink(segment_audio_file)
                                else:
                                    final_speaker_name = "Người lạ"
                                    if word_count <= 7:
                                        print(f"⚠️ {speaker_name}: văn bản quá ngắn ({word_count} từ, cần > 7 từ) + không có embedding → gán là 'Người lạ'")
                                    elif not auto_profile_creation:
                                        print(f"⚠️ {speaker_name}: tự động tạo profile bị tắt → gán là 'Người lạ'")
                                    else:
                                        print(f"⚠️ {speaker_name}: không có embedding → gán là 'Người lạ'")

                            # Cập nhật tên speaker trong tất cả groups
                            try:
                                print(f"🔄 Cập nhật tên speaker từ '{speaker_name}' thành '{final_speaker_name}'")
                                for group in combined_info['groups']:
                                    group['speaker_name'] = final_speaker_name

                                # Cập nhật lại kết quả đã tạo với tên speaker mới
                                for i, result in enumerate(results):
                                    if f"[{speaker_name}]" in result:
                                        results[i] = result.replace(f"[{speaker_name}]", f"[{final_speaker_name}]")
                                        print(f"📝 Cập nhật results[{i}]: {speaker_name} → {final_speaker_name}")                                # Thử cập nhật web_results với timeout-based approach
                                web_results_updated = False
                                try:
                                    if web_results_lock.acquire(timeout=0.5):
                                        try:
                                            for i, result in enumerate(web_results):
                                                if f"[{speaker_name}]" in result:
                                                    web_results[i] = result.replace(f"[{speaker_name}]", f"[{final_speaker_name}]")
                                                    print(f"📝 Cập nhật web_results[{i}]: {speaker_name} → {final_speaker_name}")
                                            web_results_updated = True
                                        finally:
                                            web_results_lock.release()
                                    else:
                                        print(f"⚠️ web_results_lock timeout (0.5s), sẽ sync sau cho {speaker_name}")
                                except Exception as e:
                                    print(f"⚠️ Lỗi khi cập nhật web_results: {str(e)}")

                                # Nếu không cập nhật được web_results, gửi signal để sync sau
                                if not web_results_updated:
                                    try:
                                        message_queue.publish({
                                            "action": "speaker_name_update",
                                            "old_name": speaker_name,
                                            "new_name": final_speaker_name
                                        }, type='refresh_results')
                                        print(f"📡 Đã gửi speaker update signal: {speaker_name} → {final_speaker_name}")
                                    except Exception as e:
                                        print(f"⚠️ Không thể gửi speaker update signal: {str(e)}")

                                print(f"✅ Hoàn thành cập nhật tên speaker: {final_speaker_name}")
                            except Exception as e:
                                print(f"⚠️ Lỗi khi cập nhật tên speaker: {str(e)}")
                                import traceback
                                traceback.print_exc()

                    # Lưu kết quả vào file sau khi xử lý xong tất cả (bao gồm cả speakers chưa xác định)
                    print("💾 Lưu kết quả vào file...")
                    display_results_final()

                else:
                    # Fallback: xử lý như cũ nếu diarization thất bại
                    print("⚠️ Speaker diarization thất bại, sử dụng phương pháp cũ...")

                    device = next(model.parameters()).device
                    embedding = extract_speaker_embedding(process_filename, device)
                    speaker_name = "Chờ xác định người nói"

                    if embedding is not None:
                        # Sử dụng find_matching_profile với khả năng tự động tạo profile
                        best_match, similarity = find_matching_profile(
                            embedding,
                            text="",  # Sẽ được cập nhật sau khi có kết quả decode
                            audio_file=process_filename,
                            auto_create_if_no_match=False  # Chưa tạo ngay, đợi có văn bản
                        )
                        if best_match and similarity >= SIMILARITY_THRESHOLD:
                            speaker_name = best_match
                            print(f"Phát hiện giọng nói: {best_match} (độ tin cậy: {similarity:.2f})")

                    result = endless_decode(args, model, char_dict, process_filename, no_color)

                    if result:
                        # Quyết định tên speaker cuối cùng dựa trên số từ
                        word_count = len(result.split()) if result else 0
                        final_speaker_name = speaker_name

                        if speaker_name == "Chờ xác định người nói":
                            # Sử dụng find_matching_profile với khả năng tự động tạo profile
                            if embedding is not None:
                                print(f"🔍 Thử so sánh embedding với profiles hiện có (có văn bản: {word_count} từ)...")

                                # Tạo temp audio file để có thể tạo profile
                                temp_audio_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
                                temp_audio_file.close()
                                shutil.copy2(process_filename, temp_audio_file.name)

                                try:
                                    best_match, similarity = find_matching_profile(
                                        embedding,
                                        text=result,
                                        audio_file=temp_audio_file.name,
                                        auto_create_if_no_match=auto_profile_creation  # ← Check setting
                                    )

                                    if best_match and similarity >= SIMILARITY_THRESHOLD:
                                        final_speaker_name = best_match
                                        print(f"✅ Nhận dạng thành công → {best_match} (tin cậy: {similarity:.3f})")
                                    else:
                                        final_speaker_name = "Người lạ"
                                        if word_count <= 7:
                                            print(f"⚠️ Văn bản quá ngắn ({word_count} từ, cần > 7 từ) + similarity thấp ({similarity:.3f} < {SIMILARITY_THRESHOLD}) → gán là 'Người lạ'")
                                        else:
                                            print(f"⚠️ Similarity thấp ({similarity:.3f} < {SIMILARITY_THRESHOLD}) → gán là 'Người lạ'")
                                finally:
                                    # Cleanup temp file
                                    try:
                                        os.unlink(temp_audio_file.name)
                                    except:
                                        pass
                            else:
                                final_speaker_name = "Người lạ"
                                if word_count <= 7:
                                    print(f"⚠️ Văn bản quá ngắn ({word_count} từ, cần > 7 từ) + không có embedding → gán là 'Người lạ'")
                                elif not auto_profile_creation:
                                    print(f"⚠️ Tự động tạo profile bị tắt → gán là 'Người lạ'")
                                else:
                                    print(f"⚠️ Không có embedding → gán là 'Người lạ'")

                        current_timestamp = datetime.now().strftime("%H:%M:%S")
                        speaker_info = f"[{current_timestamp}] [{final_speaker_name}] "
                        result_with_speaker = speaker_info + result
                        results.append(result_with_speaker)
                        with web_results_lock:
                            web_results.append(result_with_speaker)
                        display_results_final()  # Lưu file sau khi xử lý xong
                    else:
                        print("Không có kết quả nhận dạng từ đoạn âm thanh này.")

            except Exception as e:
                print(f"Lỗi khi xử lý file âm thanh: {str(e)}")
                import traceback
                traceback.print_exc()
            finally:
                try:
                    if process_filename != filename:
                        os.unlink(process_filename)
                except:
                    pass
                clear_embeddings_directory()
                audio_queue.task_done()

        except queue.Empty:
            time.sleep(0.5)
        except Exception as e:
            print(f"Lỗi chung khi xử lý: {str(e)}")
            import traceback
            traceback.print_exc()

def display_results():
    """Hiển thị kết quả trên web interface mà không lưu file"""
    global web_results
    with web_results_lock:
        # Phát sự kiện với tất cả kết quả để đảm bảo hiển thị đầy đủ
        if len(web_results) > 0:
            latest_result = web_results[-1]
            # Gửi cả kết quả mới nhất và tất cả kết quả
            message_queue.publish({
                "result": latest_result,
                "index": len(web_results)-1,
                "all_results": web_results  # Thêm tất cả kết quả
            }, type='new_result')

            # Cũng gửi sự kiện refresh để đảm bảo UI được cập nhật đầy đủ
            message_queue.publish({"action": "refresh_all"}, type='refresh_results')

    print("Kết quả đã được cập nhật trên trình duyệt.")

def display_results_final():
    """Hiển thị kết quả cuối cùng và lưu vào file - Optimized với timeout và fallback"""
    global web_results

    # Thử cập nhật web interface với timeout-based approach
    web_interface_updated = False
    try:
        # Thử acquire lock với timeout 1 giây
        if web_results_lock.acquire(timeout=1.0):
            try:
                # Tạo snapshot nhanh để giảm thời gian hold lock
                results_snapshot = web_results.copy() if web_results else []
                results_count = len(results_snapshot)
            finally:
                web_results_lock.release()
            
            # Xử lý sau khi đã release lock
            if results_count > 0:
                latest_result = results_snapshot[-1]
                # Gửi cả kết quả mới nhất và tất cả kết quả
                message_queue.publish({
                    "result": latest_result,
                    "index": results_count-1,
                    "all_results": results_snapshot
                }, type='new_result')

                # Gửi sự kiện refresh để đảm bảo UI được cập nhật đầy đủ
                message_queue.publish({"action": "refresh_all"}, type='refresh_results')
                print("✅ Kết quả đã được cập nhật trên trình duyệt.")
                web_interface_updated = True
        else:
            print("⚠️ web_results_lock timeout (1s), sử dụng fallback mechanism...")
    except Exception as e:
        print(f"⚠️ Lỗi khi cập nhật web interface: {str(e)}")

    # Fallback: nếu không cập nhật được web interface, vẫn gửi signal refresh
    if not web_interface_updated:
        try:
            # Gửi signal refresh không cần lock
            message_queue.publish({"action": "refresh_all", "fallback": True}, type='refresh_results')
            print("📡 Đã gửi fallback refresh signal cho web interface")
        except Exception as e:
            print(f"⚠️ Fallback refresh cũng thất bại: {str(e)}")

    # Lưu kết quả sau khi xử lý xong tất cả segments với đầy đủ dữ liệu
    print("📁 Đang lưu kết quả vào file...")
    save_results_to_file_safe()

def clear_unknown_speakers_directory():
    unknown_dir = "unknown_speakers"
    if os.path.exists(unknown_dir):
        print(f"Đang xóa tất cả file WAV trong {unknown_dir}...")
        count = 0
        for file in os.listdir(unknown_dir):
            if file.endswith('.wav'):
                file_path = os.path.join(unknown_dir, file)
                try:
                    os.remove(file_path)
                    count += 1
                except Exception as e:
                    print(f"Không thể xóa file {file}: {str(e)}")
        print(f"Đã xóa {count} file WAV trong thư mục {unknown_dir}")
    else:
        os.makedirs(unknown_dir, exist_ok=True)
        print(f"Đã tạo thư mục {unknown_dir}")

def run_flask():
    app.run(host='0.0.0.0', port=5000, debug=False)

def mic_mode(args, model, char_dict):
    global is_running
    
    args.model_obj = model
    args.char_dict = char_dict
    
    os.makedirs(WAVE_OUTPUT_FOLDER, exist_ok=True)
    os.makedirs(EMBEDDINGS_FOLDER, exist_ok=True)
    
    clear_unknown_speakers_directory()
    load_all_profiles()
    load_all_embeddings()
    
    device = next(model.parameters()).device
    init_speaker_encoder(device)
    
    print("\nChương trình đang chạy. Nhấn Ctrl+C để dừng...")
    print("Mở trình duyệt và truy cập http://localhost:5000 để xem kết quả.")
    
    flask_thread = threading.Thread(target=run_flask)
    flask_thread.daemon = True
    flask_thread.start()
    
    record_thread = threading.Thread(target=record_audio, args=(args,))
    record_thread.daemon = True
    record_thread.start()
    
    process_thread = threading.Thread(target=process_audio, args=(args, model, char_dict, args.no_color,))
    process_thread.daemon = True
    process_thread.start()
    
    try:
        while is_running:
            time.sleep(1)
    except KeyboardInterrupt:
        is_running = False
    
    print("Đang dọn dẹp...")
    try:
        if not os.listdir(WAVE_OUTPUT_FOLDER):
            os.rmdir(WAVE_OUTPUT_FOLDER)
    except:
        pass

def main():
    global SIMILARITY_THRESHOLD, AUTO_PROFILE_SIMILARITY_THRESHOLD, EMBEDDING_MERGE_THRESHOLD
    global auto_profile_creation
    
    parser = argparse.ArgumentParser(description="Nhận dạng giọng nói liên tục từ microphone với nhận dạng người nói")
    parser.add_argument("--model_checkpoint", type=str, default="models/chunkformer-large-vie", help="Đường dẫn đến model checkpoint")
    parser.add_argument("--total_batch_duration", type=int, default=14400, help="Thời lượng tối đa âm thanh (giây)")
    parser.add_argument("--chunk_size", type=int, default=64, help="Kích thước đoạn")
    parser.add_argument("--left_context_size", type=int, default=128, help="Kích thước ngữ cảnh trái")
    parser.add_argument("--right_context_size", type=int, default=0, help="Kích thước ngữ cảnh phải")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Thiết bị chạy mô hình")
    parser.add_argument("--autocast_dtype", type=str, choices=["fp32", "bf16", "fp16"], default=None, help="Kiểu dữ liệu cho autocast")
    parser.add_argument("--no_color", action="store_true", help="Tắt đầu ra có màu")
    parser.add_argument("--similarity_threshold", type=float, default=SIMILARITY_THRESHOLD, help=f"Ngưỡng tương đồng để nhận dạng người nói")
    parser.add_argument("--auto_similarity_threshold", type=float, default=AUTO_PROFILE_SIMILARITY_THRESHOLD, help=f"Ngưỡng tương đồng cho profile tự động")
    parser.add_argument("--embedding_merge_threshold", type=float, default=EMBEDDING_MERGE_THRESHOLD, help=f"Ngưỡng xóa/gộp embedding")
    parser.add_argument("--auto_profile_creation", action="store_true", default=True, help="Tự động tạo hồ sơ người nói mới")
    
    args = parser.parse_args()
    
    with settings_lock:
        SIMILARITY_THRESHOLD = args.similarity_threshold
        AUTO_PROFILE_SIMILARITY_THRESHOLD = args.auto_similarity_threshold
        EMBEDDING_MERGE_THRESHOLD = args.embedding_merge_threshold
        auto_profile_creation = args.auto_profile_creation
    
    device = torch.device(args.device)
    dtype = {"fp32": torch.float32, "bf16": torch.bfloat16, "fp16": torch.float16, None: None}[args.autocast_dtype]
    
    signal.signal(signal.SIGINT, signal_handler)
    
    print("=== THIẾT LẬP ===")
    print(f"Model Checkpoint: {args.model_checkpoint}")
    print(f"Thiết bị: {device}")
    print(f"Tổng thời gian trong một Batch: {args.total_batch_duration} giây")
    print(f"Kích thước đoạn: {args.chunk_size}")
    print(f"Kích thước ngữ cảnh trái: {args.left_context_size}")
    print(f"Kích thước ngữ cảnh phải: {args.right_context_size}")
    
    # Tải cài đặt từ file
    load_settings()
    
    print(f"Tự động tạo hồ sơ người nói mới: {auto_profile_creation}")
    print(f"Ngưỡng tương đồng giọng nói: {SIMILARITY_THRESHOLD}")
    print(f"Ngưỡng tương đồng cho profile tự động: {AUTO_PROFILE_SIMILARITY_THRESHOLD}")
    print(f"Ngưỡng xóa/gộp embedding: {EMBEDDING_MERGE_THRESHOLD}")
    print(f"Vietnamese text correction: {vietnamese_correction_enabled}")
    print(f"Sử dụng {MAX_AUDIO_FILES} file luân phiên")
    print("================\n")
    
    print("Đang khởi tạo model...")
    try:
        model, char_dict = init(args.model_checkpoint, device)
    except Exception as e:
        print(f"Lỗi khi khởi tạo model: {str(e)}")
        sys.exit(1)
    
    # Khởi tạo Silero VAD
    print("Đang khởi tạo Silero VAD...")
    vad_init_success = init_silero_vad()
    if vad_init_success:
        print("✓ Silero VAD đã được khởi tạo thành công")
    else:
        print("⚠ Silero VAD không khả dụng, sẽ sử dụng phương pháp RMS cũ")
    

    
    # Khởi tạo Vietnamese Text Corrector
    vietnamese_init_success = init_vietnamese_corrector()
    if vietnamese_init_success:
        print("✓ Vietnamese Text Corrector đã được khởi tạo thành công")
    else:
        print("⚠ Vietnamese Text Corrector không khả dụng, sẽ sử dụng văn bản gốc")

    # Khởi tạo Pyannote Speaker Diarization
    pyannote_init_success = init_pyannote_pipeline()
    if pyannote_init_success:
        print("✅ Pyannote Speaker Diarization đã sẵn sàng")
    else:
        print("⚠ Pyannote Speaker Diarization không khả dụng - sẽ sử dụng phương pháp cũ")

    # Tải kết quả đã lưu
    load_results_from_file()
    
    # Khởi động file watcher để theo dõi thay đổi settings.json
    file_watcher_observer = start_file_watcher()
    
    print("\nSẵn sàng nhận dạng giọng nói! Nói gì đó...")
    
    # Tạo thư mục transcripts và exports trước khi chạy
    os.makedirs("transcripts", exist_ok=True)
    os.makedirs("exports", exist_ok=True)
    
    with torch.autocast(device.type, dtype=dtype) if dtype is not None else nullcontext():
        mic_mode(args, model, char_dict)

if __name__ == "__main__":
    main()