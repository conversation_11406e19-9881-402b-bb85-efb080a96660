@echo off
echo ========================================
echo    ChunkFormer PyInstaller Build Script
echo ========================================
echo.

REM Kiểm tra xem PyInstaller đã được cài đặt chưa
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo PyInstaller chưa được cài đặt. Đang cài đặt...
    pip install pyinstaller
    if errorlevel 1 (
        echo Lỗi: Không thể cài đặt PyInstaller
        pause
        exit /b 1
    )
)

echo Đang kiểm tra các dependencies...
python -c "import torch, torchaudio, transformers, numpy, scipy, sklearn, librosa, soundfile, pyaudio, webrtcvad, RealtimeSTT, flask, openpyxl, pandas, speechbrain" 2>nul
if errorlevel 1 (
    echo Cảnh báo: Một số dependencies có thể chưa được cài đặt
    echo Vui lòng đảm bảo tất cả các thư viện cần thiết đã được cài đặt
    echo.
)

echo Đang xóa các file build cũ...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo.
echo Đang build ứng dụng với PyInstaller...
echo Quá trình này có thể mất vài phút...
echo.

pyinstaller chunkformer.spec --clean --noconfirm

if errorlevel 1 (
    echo.
    echo Lỗi: Build thất bại!
    echo Vui lòng kiểm tra lại các dependencies và thử lại
    pause
    exit /b 1
)

echo.
echo ========================================
echo           BUILD THÀNH CÔNG!
echo ========================================
echo.
echo File .exe đã được tạo tại: dist\ChunkFormer\ChunkFormer.exe
echo.
echo Lưu ý:
echo - File .exe sẽ chạy ẩn (không hiện cửa sổ cmd)
echo - Ứng dụng web sẽ chạy tại http://localhost:5000
echo - Đảm bảo tất cả các file model và dữ liệu cần thiết có trong thư mục dist\ChunkFormer\
echo.

REM Kiểm tra xem file exe có tồn tại không
if exist "dist\ChunkFormer\ChunkFormer.exe" (
    echo File exe đã được tạo thành công!
    echo Kích thước thư mục dist:
    dir "dist\ChunkFormer" /s /-c | find "File(s)"
    echo.
    set /p choice="Bạn có muốn mở thư mục chứa file exe không? (y/n): "
    if /i "%choice%"=="y" (
        explorer "dist\ChunkFormer"
    )
) else (
    echo Lỗi: File exe không được tạo!
)

echo.
pause
