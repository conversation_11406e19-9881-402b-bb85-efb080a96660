/**
 * Thêm CSS toàn cục để ẩn URL khi in
 */
function addPrintStyles() {
    // Tạo style element
    const style = document.createElement('style');
    style.id = 'hide-url-print-style';
    
    // Thêm CSS để ẩn URL và header/footer khi in
    style.innerHTML = `
        @media print {
            @page {
                size: A4;
                margin: 2cm;
            }
            
            @page :first {
                margin-top: 2cm;
            }
            
            @page :left {
                margin-left: 2.5cm;
                margin-right: 2cm;
                @top-left { content: ""; }
                @top-center { content: ""; }
                @top-right { content: ""; }
                @bottom-left { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right { content: ""; }
            }
            
            @page :right {
                margin-left: 2cm;
                margin-right: 2.5cm;
                @top-left { content: ""; }
                @top-center { content: ""; }
                @top-right { content: ""; }
                @bottom-left { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right { content: ""; }
            }
            
            body::after, body::before {
                content: none !important;
            }
            
            html::after, html::before {
                content: none !important;
            }
            
            .toolbar, .status-bar, .speech-editor, .history-panel, #alertsContainer, 
            .alert, .modal, .dropdown-content, .processing-modal {
                display: none !important;
            }
        }
    `;
    
    // Thêm vào head
    document.head.appendChild(style);
    
    // Thêm meta tag để kiểm soát viewport và ngăn hiển thị các header/footer khi in
    if (!document.querySelector('meta[name="viewport"]')) {
        const meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1.0';
        document.head.appendChild(meta);
    }
}/*
 * court-scripts.js - JavaScript riêng cho trang biên bản tòa án
 * Phiên bản: 1.2.0 (đã cập nhật với Undo/Redo, lưu tạm và làm mới)
 */

let activeElement = null;
let speechResults = [];
let editorMinimized = false;

// Biến theo dõi tính năng gộp người nói
let mergeSpeakers = false;

// Mảng lưu trữ lịch sử các thay đổi
let editHistory = [];
// Vị trí hiện tại trong lịch sử
let currentHistoryIndex = -1;
// Kích thước tối đa của lịch sử
const MAX_HISTORY_SIZE = 50;
// Giới hạn cảnh báo storage (MB)
const STORAGE_WARNING_LIMIT = 8; // 8MB
const STORAGE_CRITICAL_LIMIT = 10; // 10MB
// Biến theo dõi việc đang dùng undo/redo để không tạo thêm lịch sử
let isUndoRedoAction = false;
// ID phiên đăng nhập (sử dụng cho lưu tạm)
const sessionId = 'session_' + new Date().getTime();
// Khoảng thời gian tự động lưu (mili giây)
const AUTO_SAVE_INTERVAL = 30000; // 30 giây

// Lấy kết quả nhận dạng giọng nói khi trang được tải
document.addEventListener('DOMContentLoaded', function() {
    fetchSpeechResults();
    setupEditableFields();
    setupExportDropdown();
    
    // Tự động điền ngày giờ hiện tại
    if (document.getElementById('autoFill') && document.getElementById('autoFill').checked) {
        autoFillDateTime();
    }
    
    // Lưu trạng thái ban đầu của biên bản
    saveInitialState();
    
    // Thiết lập định kỳ lưu tạm
    setupAutoSave();
    
    // Thêm phím tắt cho Undo, Redo
    setupShortcuts();
    
    // Khôi phục cài đặt giao diện
    restoreUISettings();
    
    // Thêm CSS toàn cục để ẩn URL khi in
    addPrintStyles();

    // Hiển thị storage usage
    updateStorageUsageDisplay();
});

// Thiết lập dropdown xuất file
function setupExportDropdown() {
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        // Không cần tạo dropdown nếu đã cập nhật HTML mới
        if (exportBtn.classList.contains('dropdown-btn')) {
            return;
        }
        
        const dropdown = document.createElement('div');
        dropdown.className = 'dropdown';
        
        const dropdownBtn = document.createElement('button');
        dropdownBtn.innerHTML = '<i class="fas fa-file-export"></i> Xuất file';
        dropdownBtn.className = 'dropdown-btn';
        
        const dropdownContent = document.createElement('div');
        dropdownContent.className = 'dropdown-content';
        
        // Thêm các tùy chọn xuất file
        dropdownContent.innerHTML = `
            <a href="#" onclick="exportToHTML()"><i class="fas fa-file-code"></i> HTML</a>
            <a href="#" onclick="exportToWord()"><i class="fas fa-file-word"></i> Word (DOCX)</a>
            <a href="#" onclick="exportToExcel()"><i class="fas fa-file-excel"></i> Excel (XLSX)</a>
            <a href="#" onclick="exportToPDF()"><i class="fas fa-file-pdf"></i> PDF</a>
            <a href="#" onclick="exportAll()"><i class="fas fa-file-export"></i> Tất cả định dạng</a>
        `;
        
        dropdown.appendChild(dropdownBtn);
        dropdown.appendChild(dropdownContent);
        
        // Thay thế nút cũ bằng dropdown mới
        exportBtn.parentNode.replaceChild(dropdown, exportBtn);
    }
}

// Thiết lập sự kiện cho các trường có thể chỉnh sửa
function setupEditableFields() {
    const editables = document.querySelectorAll('.editable');
    
    editables.forEach(editable => {
        // Thêm placeholder khi trống
        if (!editable.textContent.trim()) {
            editable.classList.add('editable-placeholder');
            editable.textContent = editable.getAttribute('data-placeholder');
        }
        
        // Xử lý sự kiện focus
        editable.addEventListener('focus', function() {
            if (this.classList.contains('editable-placeholder')) {
                this.classList.remove('editable-placeholder');
                this.textContent = '';
            }
            activeElement = this;
            highlightActiveSection();
        });
        
        // Xử lý sự kiện blur
        editable.addEventListener('blur', function() {
            if (!this.textContent.trim()) {
                this.classList.add('editable-placeholder');
                this.textContent = this.getAttribute('data-placeholder');
            }
            
            // Lưu trạng thái sau khi sửa xong (khi blur)
            saveState('Chỉnh sửa: ' + this.id);
        });
        
        // Xử lý sự kiện click
        editable.addEventListener('click', function() {
            activeElement = this;
            highlightActiveSection();
        });
    });
    
    // Làm cho các tiêu đề phần cố định có thể chỉnh sửa
    makeFixedSectionsEditable();
}

/**
 * Làm cho các phần tiêu đề và đoạn văn cố định có thể chỉnh sửa
 */
function makeFixedSectionsEditable() {
    // Tìm tất cả các tiêu đề phần
    const sectionTitles = document.querySelectorAll('.section-title');
    sectionTitles.forEach(title => {
        convertToEditable(title, 'section_title_' + Date.now() + '_' + Math.floor(Math.random() * 1000));
    });
    
    // Tìm tất cả các mục không có class 'editable'
    const sections = document.querySelectorAll('.section > div:not(.editable):not(.section-title)');
    sections.forEach((section, index) => {
        if (!section.querySelector('.editable')) { // Không chỉ chuyển đổi các div cha
            convertToEditable(section, 'section_content_' + Date.now() + '_' + index);
        }
    });
}

/**
 * Chuyển đổi một phần tử thành có thể chỉnh sửa
 * @param {HTMLElement} element - Phần tử cần chuyển đổi
 * @param {string} id - ID để gán cho phần tử
 */
function convertToEditable(element, id) {
    // Lưu nội dung gốc
    const originalContent = element.innerHTML;
    
    // Thêm thuộc tính cần thiết
    element.setAttribute('contenteditable', 'true');
    element.classList.add('editable');
    
    // Gán ID nếu chưa có
    if (!element.id) {
        element.id = id;
    }
    
    // Lưu nội dung gốc làm placeholder nếu cần
    if (!element.getAttribute('data-placeholder')) {
        element.setAttribute('data-placeholder', originalContent);
    }
    
    // Thêm sự kiện focus
    element.addEventListener('focus', function() {
        activeElement = this;
        highlightActiveSection();
    });
    
    // Thêm sự kiện blur
    element.addEventListener('blur', function() {
        if (!this.textContent.trim()) {
            this.innerHTML = this.getAttribute('data-placeholder');
        }
        saveState('Chỉnh sửa: ' + this.id);
    });
    
    // Thêm sự kiện click
    element.addEventListener('click', function() {
        activeElement = this;
        highlightActiveSection();
    });
}

// Làm nổi bật phần đang được chọn
function highlightActiveSection() {
    document.querySelectorAll('.editable').forEach(el => {
        el.classList.remove('active');
    });
    
    if (activeElement) {
        activeElement.classList.add('active');
    }
}

// Lấy kết quả nhận dạng giọng nói từ server
function fetchSpeechResults() {
    fetch('/results')
        .then(response => response.json())
        .then(data => {
            speechResults = data.results || [];
            updateSpeechResultsDisplay();
        })
        .catch(error => {
            console.error('Lỗi khi lấy kết quả nhận dạng:', error);
        });
}

// Cập nhật hiển thị kết quả nhận dạng
function updateSpeechResultsDisplay() {
    const resultsContainer = document.getElementById('speechResults');
    if (!resultsContainer) return;
    
    resultsContainer.innerHTML = '';
    
    if (!speechResults.length) {
        resultsContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">Chưa có kết quả nhận dạng giọng nói</div>';
        return;
    }
    
    speechResults.forEach((result, index) => {
        const resultCard = document.createElement('div');
        resultCard.className = 'result-card';
        resultCard.dataset.index = index;
        
        // Xử lý kết quả có thông tin người nói
        if (result.includes('[')) {
            // Tách thông tin người nói và văn bản
            const speakerEndPos = result.indexOf(']') + 1;
            const speakerInfo = result.substring(0, speakerEndPos);
            const resultText = result.substring(speakerEndPos);
            
            // Trích xuất tên người nói
            const speakerMatch = speakerInfo.match(/\[(.*?)\]/);
            if (speakerMatch) {
                const speakerName = speakerMatch[1];
                
                resultCard.innerHTML = `
                    <div><span class="speaker-tag">${speakerName}</span></div>
                    <div class="result-text">${resultText.trim()}</div>
                    <div class="timestamp">${new Date().toLocaleTimeString()}</div>
                `;
            } else {
                resultCard.innerHTML = `
                    <div class="result-text">${result}</div>
                    <div class="timestamp">${new Date().toLocaleTimeString()}</div>
                `;
            }
        } else {
            resultCard.innerHTML = `
                <div class="result-text">${result}</div>
                <div class="timestamp">${new Date().toLocaleTimeString()}</div>
            `;
        }
        
        resultCard.onclick = function() {
            this.classList.toggle('selected');
        };
        
        resultsContainer.appendChild(resultCard);
    });
}

// Chèn văn bản vào phần được chỉ định - không còn yêu cầu phải chọn kết quả
function insertSelectedText(sectionId) {
    const targetElement = document.getElementById(sectionId);
    if (!targetElement) return;
    
    // Xóa placeholder nếu có
    if (targetElement.classList.contains('editable-placeholder')) {
        targetElement.classList.remove('editable-placeholder');
        targetElement.textContent = '';
    }
    
    // Lưu trạng thái trước khi thay đổi
    saveState('Trước khi chèn văn bản vào ' + sectionId);
    
    // Lấy tất cả kết quả, không cần phải chọn
    let formattedContent = formatAllResultsHTML();
    
    // Đưa vào mục đích
    targetElement.innerHTML = formattedContent;
    targetElement.focus();
    
    // Lưu trạng thái sau khi thay đổi
    saveState('Chèn văn bản vào ' + sectionId);
    
    // Hiển thị thông báo
    showAlert('success', 'Đã thêm tất cả kết quả nhận dạng vào phần được chọn');
}

// Chèn tất cả văn bản vào phần được chọn
function insertAllText() {
    if (!activeElement) {
        showAlert('error', 'Vui lòng chọn một mục để chèn văn bản');
        return;
    }
    
    // Xóa placeholder nếu có
    if (activeElement.classList.contains('editable-placeholder')) {
        activeElement.classList.remove('editable-placeholder');
        activeElement.textContent = '';
    }
    
    // Lưu trạng thái trước khi thay đổi
    saveState('Trước khi chèn văn bản');
    
    // Lấy tất cả văn bản từ kết quả nhận dạng với định dạng HTML
    let formattedContent = formatAllResultsHTML();
    
    activeElement.innerHTML = formattedContent;
    activeElement.focus();
    
    // Lưu trạng thái sau khi thay đổi
    saveState('Chèn văn bản');
    
    showAlert('success', 'Đã chèn văn bản vào phần được chọn');
}

// Định dạng kết quả thành HTML đơn giản
function formatAllResultsHTML() {
    let formattedHTML = '';
    let mergedTexts = {};
    
    // Nếu không bật chế độ gộp người nói
    if (!mergeSpeakers) {
        // Lấy tất cả kết quả nhận dạng
        speechResults.forEach((result, index) => {
            let textToAdd = '';
            
            // Xử lý kết quả có thông tin người nói - hỗ trợ cả format cũ và mới
            let speakerName = null;
            let timestamp = null;
            let resultText = result;
            
            // Kiểm tra format mới: [HH:MM:SS] [Speaker Name] Content
            const newFormatMatch = result.match(/^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$/);
            if (newFormatMatch) {
                timestamp = newFormatMatch[1];
                speakerName = newFormatMatch[2]; // Tên người nói
                resultText = newFormatMatch[3] ? newFormatMatch[3].trim() : '';
            } else if (result.includes('[') && result.includes(']')) {
                // Format cũ: [Speaker Name] Content
                const speakerEndPos = result.indexOf(']') + 1;
                const speakerInfo = result.substring(0, speakerEndPos);
                resultText = result.substring(speakerEndPos).trim();
                     // Trích xuất tên người nói
            let speakerMatch = speakerInfo.match(/\[(.*?)\]/); // Lấy tên người nói
                
                if (speakerMatch) {
                    speakerName = speakerMatch[1];
                }
            }
            
            if (speakerName) {
                // Hiển thị mỗi người nói trên dòng riêng biệt cho biên bản tòa án với thời gian
                if (timestamp) {
                    textToAdd = `<strong>[${timestamp}] ${speakerName}:</strong><br>${resultText}`;
                } else {
                    textToAdd = `<strong>${speakerName}:</strong><br>${resultText}`;
                }
            } else {
                // Không có thông tin người nói - chỉ hiển thị văn bản
                textToAdd = result;
            }
            
            // Thêm văn bản với xuống dòng
            if (textToAdd) {
                formattedHTML += textToAdd + '<br>';
            }
        });
        
        return formattedHTML;
    }
    
    // CHẾ ĐỘ GỘP NGƯỜI NÓI
    
    // Giai đoạn 1: Gộp tất cả văn bản theo người nói
    speechResults.forEach((result, index) => {
        let speakerName = "Không xác định";
        let timestamp = null;
        let resultText = result;
        
        // Kiểm tra format mới: [HH:MM:SS] [Speaker Name] Content
        const newFormatMatch = result.match(/^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$/);
        if (newFormatMatch) {
            timestamp = newFormatMatch[1];
            speakerName = newFormatMatch[2]; // Tên người nói
            resultText = newFormatMatch[3] ? newFormatMatch[3].trim() : '';
        } else if (result.includes('[') && result.includes(']')) {
            // Format cũ: [Speaker Name] Content
            const speakerEndPos = result.indexOf(']') + 1;
            const speakerInfo = result.substring(0, speakerEndPos);
            resultText = result.substring(speakerEndPos).trim();
            
            // Trích xuất tên người nói
            let speakerMatch = speakerInfo.match(/\[(.*?)\]/); // Lấy tên người nói
            
            if (speakerMatch) {
                speakerName = speakerMatch[1];
            }
        }
        
        // Thêm vào danh sách gộp với thông tin timestamp
        if (!mergedTexts[speakerName]) {
            mergedTexts[speakerName] = {
                texts: [],
                firstTimestamp: null
            };
        }
        
        // Lưu timestamp đầu tiên của người nói
        if (timestamp && !mergedTexts[speakerName].firstTimestamp) {
            mergedTexts[speakerName].firstTimestamp = timestamp;
        }
        
        // Thêm văn bản vào mảng người nói này nếu có nội dung
        if (resultText.trim()) {
            mergedTexts[speakerName].texts.push(resultText);
        }
    });
    
    // Giai đoạn 2: Tạo HTML từ các văn bản đã gộp
    for (let speaker in mergedTexts) {
        if (mergedTexts[speaker].texts.length > 0) {
            // Gộp tất cả văn bản của người nói
            const fullText = mergedTexts[speaker].texts.join(" ");
            
            // Tạo HTML với mỗi người nói trên dòng riêng biệt cho biên bản tòa án
            if (speaker !== "Không xác định") {
                const timestamp = mergedTexts[speaker].firstTimestamp;
                if (timestamp) {
                    // Thêm timestamp khi có, nhưng loại bỏ độ tin cậy
                    formattedHTML += `<strong>[${timestamp}] ${speaker}:</strong><br>${fullText}<br>`;
                } else {
                    formattedHTML += `<strong>${speaker}:</strong><br>${fullText}<br>`;
                }
            } else {
                formattedHTML += `${fullText}<br>`;
            }
        }
    }
    
    return formattedHTML;
}
// Xóa nội dung của phần được chọn
function clearSelectedText() {
    if (!activeElement) {
        showAlert('error', 'Vui lòng chọn một mục để xóa nội dung');
        return;
    }
    
    // Lưu trạng thái trước khi thay đổi
    saveState('Trước khi xóa nội dung');
    
    activeElement.innerHTML = '';
    activeElement.classList.add('editable-placeholder');
    activeElement.textContent = activeElement.getAttribute('data-placeholder');
    
    // Lưu trạng thái sau khi thay đổi
    saveState('Xóa nội dung');
    
    showAlert('success', 'Đã xóa nội dung phần được chọn');
}

// Tự động điền ngày giờ hiện tại
function autoFillDateTime() {
    const now = new Date();
    
    // Điền thời gian bắt đầu phiên tòa
    const hearingTime = document.getElementById('hearing_time');
    const hearingMinute = document.getElementById('hearing_minute');
    const hearingDate = document.getElementById('hearing_date');
    const hearingMonth = document.getElementById('hearing_month');
    const hearingYear = document.getElementById('hearing_year');
    
    if (hearingTime) hearingTime.textContent = now.getHours();
    if (hearingMinute) hearingMinute.textContent = now.getMinutes();
    if (hearingDate) hearingDate.textContent = now.getDate();
    if (hearingMonth) hearingMonth.textContent = now.getMonth() + 1;
    if (hearingYear) hearingYear.textContent = now.getFullYear();
    
    // Điền thời gian kết thúc phiên tòa (giả sử kết thúc sau 2 giờ)
    const endTime = new Date(now.getTime() + 2 * 60 * 60 * 1000);
    const endTimeEl = document.getElementById('end_time');
    const endMinuteEl = document.getElementById('end_minute');
    const endDateEl = document.getElementById('end_date');
    const endMonthEl = document.getElementById('end_month');
    const endYearEl = document.getElementById('end_year');
    
    if (endTimeEl) endTimeEl.textContent = endTime.getHours();
    if (endMinuteEl) endMinuteEl.textContent = endTime.getMinutes();
    if (endDateEl) endDateEl.textContent = endTime.getDate();
    if (endMonthEl) endMonthEl.textContent = endTime.getMonth() + 1;
    if (endYearEl) endYearEl.textContent = endTime.getFullYear();
    
    // Xóa lớp placeholder từ các trường này
    document.querySelectorAll('#hearing_time, #hearing_minute, #hearing_date, #hearing_month, #hearing_year, #end_time, #end_minute, #end_date, #end_month, #end_year').forEach(el => {
        if (el) el.classList.remove('editable-placeholder');
    });
}

// Mở modal cài đặt
function openSettings() {
    const settingsModal = document.getElementById('settingsModal');
    if (settingsModal) settingsModal.style.display = 'block';
}

// Đóng modal
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) modal.style.display = 'none';
}

// Áp dụng cài đặt
function applySettings() {
    const fontFamily = document.getElementById('fontFamily');
    const fontSize = document.getElementById('fontSize');
    const lineHeight = document.getElementById('lineHeight');
    const autoSave = document.getElementById('autoSave');
    const mergeSpeakersCheckbox = document.getElementById('mergeSpeakers');
    
    if (fontFamily && fontSize && lineHeight) {
        document.body.style.fontFamily = fontFamily.value;
        document.body.style.fontSize = fontSize.value;
        document.body.style.lineHeight = lineHeight.value;
        
        // Lưu cài đặt
        localStorage.setItem('fontFamily', fontFamily.value);
        localStorage.setItem('fontSize', fontSize.value);
        localStorage.setItem('lineHeight', lineHeight.value);
    }
    
    if (autoSave) {
        localStorage.setItem('autoSave', autoSave.checked ? 'true' : 'false');
    }
    
    if (mergeSpeakersCheckbox) {
        mergeSpeakers = mergeSpeakersCheckbox.checked;
        localStorage.setItem('mergeSpeakers', mergeSpeakers ? 'true' : 'false');
    }
    
    closeModal('settingsModal');
    
    // Tạo lịch sử cho thay đổi cài đặt
    saveState('Thay đổi cài đặt');
    
    showAlert('success', 'Đã áp dụng cài đặt mới');
}

// Khôi phục cài đặt giao diện
function restoreUISettings() {
    const fontFamily = localStorage.getItem('fontFamily');
    const fontSize = localStorage.getItem('fontSize');
    const lineHeight = localStorage.getItem('lineHeight');
    const savedMergeSpeakers = localStorage.getItem('mergeSpeakers');
    
    if (fontFamily) {
        document.body.style.fontFamily = fontFamily;
        if (document.getElementById('fontFamily')) {
            document.getElementById('fontFamily').value = fontFamily;
        }
    }
    
    if (fontSize) {
        document.body.style.fontSize = fontSize;
        if (document.getElementById('fontSize')) {
            document.getElementById('fontSize').value = fontSize;
        }
    }
    
    if (lineHeight) {
        document.body.style.lineHeight = lineHeight;
        if (document.getElementById('lineHeight')) {
            document.getElementById('lineHeight').value = lineHeight;
        }
    }
    
    // Khôi phục cài đặt gộp người nói
    if (savedMergeSpeakers !== null) {
        mergeSpeakers = savedMergeSpeakers === 'true';
        const mergeSpeakersCheckbox = document.getElementById('mergeSpeakers');
        if (mergeSpeakersCheckbox) {
            mergeSpeakersCheckbox.checked = mergeSpeakers;
        }
    }
}

// Thu gọn/mở rộng editor
function toggleEditor() {
    const editorContent = document.getElementById('editorContent');
    const minimizeBtn = document.getElementById('minimizeEditor');
    
    if (!editorContent || !minimizeBtn) return;
    
    editorMinimized = !editorMinimized;
    
    if (editorMinimized) {
        editorContent.style.display = 'none';
        minimizeBtn.innerHTML = '<i class="fas fa-chevron-up"></i> Mở rộng';
    } else {
        editorContent.style.display = 'flex';
        minimizeBtn.innerHTML = '<i class="fas fa-chevron-down"></i> Thu gọn';
    }
}

// Lưu biên bản dạng HTML
function exportToHTML() {
    saveDocument();
}

// Hàm lưu biên bản dạng HTML
function saveDocument() {
    const documentContainer = document.getElementById('documentContainer');
    if (!documentContainer) return;
    
    // Hiển thị thông báo đang xử lý
    const processingModal = showProcessingModal('Đang chuẩn bị file HTML...');
    
    try {
        // Chuẩn bị cho in/xuất file
        prepareForPrinting();
        
        const documentContent = documentContainer.innerHTML;
        const styles = document.querySelector('style');
        const stylesContent = styles ? styles.textContent : '';
        
        updateProcessingModal(processingModal, 'Đang tạo file HTML...');
        
        // CSS để ẩn URL và header khi in
        const hideUrlStyle = `
            @media print {
                @page {
                    margin: 2cm;
                }
                
                @page :first {
                    margin-top: 2cm;
                }
                
                @page :left, :right {
                    @top-left { content: ""; }
                    @top-center { content: ""; }
                    @top-right { content: ""; }
                    @bottom-left { content: ""; }
                    @bottom-center { content: ""; }
                    @bottom-right { content: ""; }
                }
                
                body::before, body::after,
                html::before, html::after {
                    content: none !important;
                    display: none !important;
                }
                
                /* Hide title in headers */
                title, .title {
                    display: none !important;
                }
            }
            
            /* Hide title in exported HTML */
            title {
                display: none;
            }
        `;
        
        const file = new Blob([
            '<!DOCTYPE html><html><head>' +
            '<meta charset="UTF-8">' +
            '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
            '<title> </title>' + // Để tiêu đề trống
            '<style>' + stylesContent + hideUrlStyle + '</style>' +
            '</head>' +
            '<body class="printable">' + documentContent + '</body></html>'
        ], { type: 'text/html' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(file);
        link.download = 'bien_ban_phien_toa.html';
        
        setTimeout(() => {
            link.click();
            URL.revokeObjectURL(link.href);
            
            // Khôi phục sau khi xuất HTML
            restoreAfterPrinting();
            hideProcessingModal(processingModal);
            
            showAlert('success', 'Đã lưu biên bản thành công!');
        }, 500);
    } catch (error) {
        console.error('Lỗi khi xuất HTML:', error);
        restoreAfterPrinting();
        hideProcessingModal(processingModal);
        showAlert('error', 'Có lỗi xảy ra khi lưu biên bản: ' + error.message);
    }
}
// ===== CHỨC NĂNG XUẤT EXCEL =====

/**
 * Xuất biên bản tòa án ra file Excel
 * Hàm này gửi dữ liệu đến máy chủ để tạo file Excel
 */
function exportToExcel() {
    // Hiện thông báo đang xử lý
    const processingModal = showProcessingModal('Đang chuẩn bị dữ liệu để xuất Excel...');
    
    // Lấy dữ liệu đã cấu trúc từ biên bản
    const data = getImprovedTranscriptTableData();
    
    // Kiểm tra dữ liệu
    if (!data || data.length === 0) {
        hideProcessingModal(processingModal);
        showAlert('error', 'Không tìm thấy dữ liệu biên bản để xuất!');
        return;
    }
    
    // Cập nhật thông báo
    updateProcessingModal(processingModal, 'Đang gửi dữ liệu đến máy chủ...');
    
    // Gửi yêu cầu xuất Excel (sử dụng endpoint tạm thời)
    fetch('/export_excel', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Lỗi khi xuất file Excel: ${response.status}`);
        }
        updateProcessingModal(processingModal, 'Đang tạo file Excel...');
        return response.blob();
    })
    .then(blob => {
        // Xóa thông báo đang xử lý
        hideProcessingModal(processingModal);
        
        // Tạo link để tải xuống file
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'bien_ban_phien_toa.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
        
        showAlert('success', 'Đã xuất biên bản sang định dạng Excel!');
    })
    .catch(error => {
        console.error('Lỗi:', error);
        
        // Xóa thông báo đang xử lý
        hideProcessingModal(processingModal);
        
        // Tạo thông báo lỗi
        showAlert('error', 'Có lỗi xảy ra khi xuất file Excel. Đang thử phương pháp dự phòng...');
        
        // Sử dụng phương án dự phòng
        exportToExcelFallback(data);
    });
}

/**
 * Phương án dự phòng để xuất Excel khi API thất bại
 * Sử dụng CSV nâng cao hơn so với phiên bản cũ
 */
function exportToExcelFallback(data) {
    try {
        // Chuyển đổi dữ liệu thành CSV
        let csvContent = "data:text/csv;charset=utf-8,";
        
        // Thêm BOM (Byte Order Mark) để Excel hiểu encoding UTF-8
        csvContent += "\uFEFF";
        
        // Chuyển đổi mảng thành chuỗi CSV
        data.forEach(row => {
            const processedRow = row.map(cell => {
                // Xử lý các ký tự đặc biệt và đảm bảo định dạng đúng
                if (cell === null || cell === undefined) {
                    return '';
                }
                
                const cellStr = String(cell);
                // Nếu cell chứa dấu phẩy, dấu nháy kép hoặc xuống dòng thì bọc trong dấu nháy kép
                if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
                    // Thay thế dấu nháy kép bằng 2 dấu nháy kép (quy tắc CSV)
                    return '"' + cellStr.replace(/"/g, '""') + '"';
                }
                return cellStr;
            }).join(',');
            csvContent += processedRow + '\r\n';
        });
        
        // Tạo link để tải xuống
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement('a');
        link.setAttribute('href', encodedUri);
        link.setAttribute('download', 'bien_ban_phien_toa.csv');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showAlert('info', 'Đã xuất dữ liệu sang định dạng CSV. Vui lòng mở bằng Excel để xem.');
    } catch (error) {
        console.error('Lỗi khi xuất CSV:', error);
        showAlert('error', 'Không thể xuất file. Vui lòng thử lại sau.');
    }
}

/**
 * Lấy dữ liệu cấu trúc cải tiến từ biên bản để xuất Excel
 * Phiên bản cải tiến với metadata đầy đủ và cấu trúc tốt hơn
 */
function getImprovedTranscriptTableData() {
    const data = [];
    
    // Thêm metadata và header
    data.push(['BIÊN BẢN PHIÊN TÒA HÌNH SỰ SƠ THẨM']);
    data.push(['']);
    
    // Thêm thông tin tòa án
    const courtEl = document.getElementById('header_court');
    if (courtEl && !courtEl.classList.contains('editable-placeholder')) {
        data.push([courtEl.textContent.trim()]);
    } else {
        data.push(['TÒA ÁN NHÂN DÂN...']);
    }
    
    data.push(['CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM']);
    data.push(['Độc lập - Tự do - Hạnh phúc']);
    data.push(['']);
    
    // Lấy thông tin ngày giờ
    const timeInfo = getFormattedDateTime();
    data.push([timeInfo]);
    data.push(['']);
    
    // Thông tin địa điểm và tòa án
    const hearingLocation = getElementTextOrPlaceholder('hearing_location');
    data.push([`Tại: ${hearingLocation}`]);
    
    const courtName = getElementTextOrPlaceholder('court_name');
    data.push([`Tòa án ${courtName}`]);
    
    // Thông tin vụ án và bị cáo
    const defendantName = getElementTextOrPlaceholder('defendant_name');
    data.push([`Mở phiên tòa để xét xử sơ thẩm vụ án hình sự đối với bị cáo ${defendantName}`]);
    
    // Thông tin kiểm sát viên
    const prosecutorOffice = getElementTextOrPlaceholder('prosecutor_office');
    data.push([`Bị Viện kiểm sát ${prosecutorOffice}`]);
    
    // Thông tin tội danh
    const crime = getElementTextOrPlaceholder('crime');
    data.push([`Truy tố về tội (các tội) ${crime}`]);
    data.push(['']);
    
    // Thêm thông tin người tiến hành tố tụng
    data.push(['I. NHỮNG NGƯỜI TIẾN HÀNH TỐ TỤNG:']);
    
    const judgeName = getElementTextOrPlaceholder('judge_name');
    data.push([`Thẩm phán - Chủ tọa phiên tòa: Ông (Bà) ${judgeName}`]);
    
    const secretaryName = getElementTextOrPlaceholder('secretary_name');
    data.push([`Thư ký phiên tòa: Ông (Bà) ${secretaryName}`]);
    
    const prosecutorRep = getElementTextOrPlaceholder('prosecutor_rep');
    const prosecutorName = getElementTextOrPlaceholder('prosecutor_name');
    data.push([`Đại diện Viện kiểm sát ${prosecutorRep} tham gia phiên tòa:`]);
    data.push([`Ông (Bà) ${prosecutorName} Kiểm sát viên.`]);
    data.push(['']);
    
    // Thông tin người tham gia tố tụng
    data.push(['II. NHỮNG NGƯỜI THAM GIA TỐ TỤNG:']);
    
    const defendantFullInfo = getElementTextOrPlaceholder('defendant_full_info');
    data.push([`- Bị cáo: ${defendantFullInfo}`]);
    
    const victim = getElementTextOrPlaceholder('victim');
    data.push([`- Bị hại: ${victim}`]);
    data.push(['']);
    
    // Phần nội dung tranh tụng
    data.push(['III. PHẦN TRANH TỤNG TẠI PHIÊN TÒA']);
    
    // 1. Phần trình bày của KSV
    const prosecutorStatement = document.getElementById('prosecutor_statement');
    if (prosecutorStatement && !prosecutorStatement.classList.contains('editable-placeholder')) {
        data.push(['1. Kiểm sát viên công bố bản cáo trạng:']);
        
        // Xử lý nội dung đặc biệt nếu có .speaker-content
        const speakerContents = prosecutorStatement.querySelectorAll('.speaker-content');
        if (speakerContents.length > 0) {
            speakerContents.forEach(content => {
                const name = content.querySelector('.speaker-name');
                const speechText = content.querySelector('.speaker-text');
                
                if (name && speechText) {
                    data.push([`${name.textContent.trim()}: ${speechText.textContent.trim()}`]);
                }
            });
        } else {
            // Xử lý nội dung dạng văn bản thường
            const paragraphs = prosecutorStatement.textContent.trim().split('\n');
            paragraphs.forEach(paragraph => {
                if (paragraph.trim()) {
                    data.push([paragraph.trim()]);
                }
            });
        }
        
        data.push(['']);
    }
    
    // 2. Phần hỏi và trả lời
    const questioning = document.getElementById('questioning');
    if (questioning && !questioning.classList.contains('editable-placeholder')) {
        data.push(['2. Hỏi và trả lời tại phiên tòa:']);
        
        // Xử lý nội dung đặc biệt nếu có .speaker-content
        const speakerContents = questioning.querySelectorAll('.speaker-content');
        if (speakerContents.length > 0) {
            speakerContents.forEach(content => {
                const name = content.querySelector('.speaker-name');
                const speechText = content.querySelector('.speaker-text');
                
                if (name && speechText) {
                    data.push([`${name.textContent.trim()}: ${speechText.textContent.trim()}`]);
                }
            });
        } else {
            // Xử lý nội dung dạng văn bản thường
            const paragraphs = questioning.textContent.trim().split('\n');
            paragraphs.forEach(paragraph => {
                if (paragraph.trim()) {
                    data.push([paragraph.trim()]);
                }
            });
        }
        
        data.push(['']);
    }
    
    // 3. Phần tranh luận
    const debate = document.getElementById('debate');
    if (debate && !debate.classList.contains('editable-placeholder')) {
        data.push(['3. Tranh luận tại phiên tòa:']);
        
        // Xử lý nội dung đặc biệt nếu có .speaker-content
        const speakerContents = debate.querySelectorAll('.speaker-content');
        if (speakerContents.length > 0) {
            speakerContents.forEach(content => {
                const name = content.querySelector('.speaker-name');
                const speechText = content.querySelector('.speaker-text');
                
                if (name && speechText) {
                    data.push([`${name.textContent.trim()}: ${speechText.textContent.trim()}`]);
                }
            });
        } else {
            // Xử lý nội dung dạng văn bản thường
            const paragraphs = debate.textContent.trim().split('\n');
            paragraphs.forEach(paragraph => {
                if (paragraph.trim()) {
                    data.push([paragraph.trim()]);
                }
            });
        }
        
        data.push(['']);
    }
    
    // 4. Lời nói sau cùng của bị cáo
    const defendantStatement = document.getElementById('defendant_statement');
    if (defendantStatement && !defendantStatement.classList.contains('editable-placeholder')) {
        data.push(['4. Lời nói sau cùng của bị cáo:']);
        
        // Xử lý nội dung đặc biệt nếu có .speaker-content
        const speakerContents = defendantStatement.querySelectorAll('.speaker-content');
        if (speakerContents.length > 0) {
            speakerContents.forEach(content => {
                const name = content.querySelector('.speaker-name');
                const speechText = content.querySelector('.speaker-text');
                
                if (name && speechText) {
                    data.push([`${name.textContent.trim()}: ${speechText.textContent.trim()}`]);
                }
            });
        } else {
            // Xử lý nội dung dạng văn bản thường
            const paragraphs = defendantStatement.textContent.trim().split('\n');
            paragraphs.forEach(paragraph => {
                if (paragraph.trim()) {
                    data.push([paragraph.trim()]);
                }
            });
        }
        
        data.push(['']);
    }
    
    // Thêm thông tin kết thúc
    const endTimeInfo = getFormattedEndDateTime();
    data.push(['Phiên tòa kết thúc ' + endTimeInfo]);
    data.push(['']);
    
    // Thông tin chữ ký
    data.push(['THƯ KÝ GHI BIÊN BẢN PHIÊN TÒA', '', 'THẨM PHÁN - CHỦ TỌA PHIÊN TÒA']);
    
    const secretarySignature = getElementTextOrPlaceholder('secretary_signature');
    const judgeSignature = getElementTextOrPlaceholder('judge_signature');
    data.push([secretarySignature, '', judgeSignature]);
    
    return data;
}

// ===== CHỨC NĂNG XUẤT WORD =====

/**
 * Xuất biên bản ra file Word sử dụng HTML cải tiến
 */
function exportToWord() {
    // Chuẩn bị trang cho việc in/xuất
    prepareForPrinting();
    
    // Tạo ID duy nhất cho tài liệu
    const docId = 'doc_' + new Date().getTime();
    
    // Lấy nội dung biên bản
    const documentContainer = document.getElementById('documentContainer');
    if (!documentContainer) {
        alert('Không thể tìm thấy nội dung biên bản!');
        restoreAfterPrinting();
        return;
    }
    
    // Hiển thị thông báo đang xử lý
    const processingModal = showProcessingModal('Đang chuẩn bị tài liệu Word...');
    
    // Lấy nội dung HTML của biên bản
    const documentContent = documentContainer.innerHTML;
    
    // CSS cải tiến cho Word
    const wordStyles = `
        @page {
            size: 21cm 29.7cm;
            margin: 2cm 2.5cm;
            mso-page-orientation: portrait;
            
            /* Loại bỏ header và footer */
            mso-header-data: "";
            mso-footer-data: "";
            mso-header: "";
            mso-footer: "";
            
            /* Ẩn URL */
            mso-url: "";
            mso-hide: all;
            
            /* Ẩn tiêu đề */
            mso-title-page: no;
            mso-header-margin: 0;
            mso-footer-margin: 0;
        }
        
        /* Phần header tiêu đề */
        div.WordSection1 {
            page-break-before: always;
        }
        
        /* Ẩn tiêu đề */
        h1.NoTopSpacing {
            mso-style-name: "No Top Spacing";
            margin-top: 0;
            line-height: 0;
            font-size: 1pt;
            color: white;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.5;
        }
        
        .document-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header-left, .header-right {
            display: inline-block;
            width: 48%;
            vertical-align: top;
        }
        
        .header-left {
            text-align: left;
        }
        
        .header-right {
            text-align: center;
        }
        
        .document-title {
            font-weight: bold;
            font-size: 14pt;
            text-align: center;
            margin: 20px 0;
            mso-style-name: "Title";
        }
        
        .section {
            margin-bottom: 10px;
        }
        
        .section-title {
            font-weight: bold;
            margin-bottom: 5px;
            mso-style-name: "Heading 1";
        }
        
        /* Định dạng phần người nói */
        .speaker-content {
            margin-bottom: 10px;
        }
        
        .speaker-name {
            font-weight: bold;
            mso-style-name: "Strong";
        }
        
        .speaker-time {
            font-style: italic;
            color: #555;
            font-size: 0.9em;
        }
        
        .speaker-text {
            display: block;
            margin-left: 20px;
            margin-top: 4px;
        }
        
        /* Định dạng cho phần chữ ký */
        .signatures {
            display: table;
            width: 100%;
            margin-top: 30px;
        }
        
        .signature {
            display: table-cell;
            width: 45%;
            text-align: center;
        }
        
        .signature-title {
            font-weight: bold;
            margin-bottom: 50px;
        }
        
        /* Ẩn các phần không cần thiết khi xuất Word */
        .toolbar, .speech-editor, .status-bar, #historyPanel, #alertsContainer, .alert, .modal {
            display: none !important;
        }
        
        /* Loại bỏ viền và màu nền của các trường editable */
        .editable {
            border: none !important;
            background-color: transparent !important;
        }
        
        /* Ẩn placeholder */
        .editable-placeholder {
            display: none !important;
        }
    `;
    
    // Tên file và các thông số
    const fileName = 'bien_ban_phien_toa.doc';
    const title = ''; // Tiêu đề trống để tránh hiển thị
    const subject = 'Phiên Tòa Hình Sự Sơ Thẩm';
    
    try {
        // Tạo tài liệu HTML tương thích Word
        const htmlContent = [
            '<!DOCTYPE html>',
            '<html xmlns:o="urn:schemas-microsoft-com:office:office" ',
            'xmlns:w="urn:schemas-microsoft-com:office:word" ',
            'xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" ',
            'xmlns="http://www.w3.org/TR/REC-html40">',
            '<head>',
            '<meta charset="utf-8">',
            '<meta name="ProgId" content="Word.Document">',
            '<meta name="Generator" content="Microsoft Word 15">',
            '<meta name="Originator" content="Microsoft Word 15">',
            `<title>${title}</title>`,
            `<meta name="Description" content="${subject}">`,
            '<style>',
            wordStyles,
            '</style>',
            '<!--[if gte mso 9]>',
            '<xml>',
            '<w:WordDocument>',
            '<w:View>Print</w:View>',
            '<w:Zoom>100</w:Zoom>',
            '<w:DoNotOptimizeForBrowser/>',
            '<w:TrackMoves>false</w:TrackMoves>',
            '<w:TrackFormatting/>',
            '<w:ValidateAgainstSchemas/>',
            '<w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>',
            '<w:IgnoreMixedContent>false</w:IgnoreMixedContent>',
            '<w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>',
            '<w:DoNotPromoteQF/>',
            '<w:LidThemeOther>VI</w:LidThemeOther>',
            '<w:LidThemeAsian>X-NONE</w:LidThemeAsian>',
            '<w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>',
            '<w:Compatibility>',
            '<w:BreakWrappedTables/>',
            '<w:SnapToGridInCell/>',
            '<w:WrapTextWithPunct/>',
            '<w:UseAsianBreakRules/>',
            '<w:DontGrowAutofit/>',
            '<w:SplitPgBreakAndParaMark/>',
            '<w:EnableOpenTypeKerning/>',
            '<w:DontFlipMirrorIndents/>',
            '<w:OverrideTableStyleHps/>',
            '</w:Compatibility>',
            '<w:DocumentVariables>',
            `<w:VariableName>DocumentID</w:VariableName><w:VariableValue>${docId}</w:VariableValue>`,
            '</w:DocumentVariables>',
            '<w:DoNotHyphenateCaps/>',
            '<w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery>',
            '<w:DisplayVerticalDrawingGridEvery>0</w:DisplayVerticalDrawingGridEvery>',
            '<w:UseMarginsForDrawingGridOrigin/>',
            '<w:ValidateAgainstSchemas/>',
            '<w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>',
            '<w:IgnoreMixedContent>false</w:IgnoreMixedContent>',
            '<w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>',
            '<w:DoNotUnderlineInvalidXML/>',
            '<w:DoNotShadeFormData/>',
            '<w:DoNotPromoteQF/>',
            '<w:LidThemeOther>VI</w:LidThemeOther>',
            '<w:LidThemeAsian>X-NONE</w:LidThemeAsian>',
            '<w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>',
            '</w:WordDocument>',
            '</xml>',
            '<![endif]-->',
            '<!--[if gte mso 9]>',
            '<xml>',
            '<w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="false" DefSemiHidden="false" DefQFormat="false" DefPriority="99" LatentStyleCount="376">',
            '</w:LatentStyles>',
            '</xml>',
            '<![endif]-->',
            // Các thẻ meta để kiểm soát header và footer
            '<meta name="docinfo:title" content=""/>',
            '<meta name="docinfo:author" content=""/>',
            '</head>',
            '<body>',
            // Sử dụng tiêu đề trống
            '<h1 class="NoTopSpacing">&nbsp;</h1>',
            // Tạo phần WordSection1 để kiểm soát tốt hơn 
            '<div class="WordSection1">',
            documentContent,
            '</div>',
            '</body>',
            '</html>'
        ].join('\n');
        
        updateProcessingModal(processingModal, 'Đang tạo file Word...');
        
        // Tạo blob và link để download
        const blob = new Blob([htmlContent], {type: 'application/msword;charset=utf-8'});
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        
        // Thêm link vào document, click và sau đó xóa
        document.body.appendChild(link);
        
        // Timeout nhỏ để đảm bảo Modal hiển thị
        setTimeout(() => {
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
            
            // Đóng modal và khôi phục 
            hideProcessingModal(processingModal);
            restoreAfterPrinting();
            
            showAlert('success', 'Đã xuất biên bản sang định dạng Word!');
        }, 500);
    } catch (error) {
        console.error('Lỗi khi xuất Word:', error);
        hideProcessingModal(processingModal);
        restoreAfterPrinting();
        showAlert('error', 'Có lỗi xảy ra khi xuất file Word: ' + error.message);
    }
}

/**
 * Xuất biên bản ra file PDF
 * Cải tiến từ phiên bản gốc
 */
function exportToPDF() {
    // Hiển thị thông báo
    showAlert('info', 'Đang chuẩn bị xuất file PDF. Khi cửa sổ in xuất hiện, vui lòng chọn "Lưu dưới dạng PDF".');
    
    // Thêm một khoảng thời gian nhỏ để thông báo hiển thị trước khi in
    setTimeout(() => {
        // Chuẩn bị trang cho việc in
        prepareForPrinting();
        
        // Đặt các tùy chọn in để tối ưu cho PDF
        const style = document.createElement('style');
        style.id = "print-pdf-style";
        style.innerHTML = `
            @page {
                size: A4;
                margin: 2cm 2.5cm;
            }
            
            @media print {
                body {
                    font-family: 'Times New Roman', serif;
                    font-size: 12pt;
                    line-height: 1.5;
                }
                
                /* Đảm bảo ẩn tất cả các UI element */
                .toolbar, .status-bar, .speech-editor, #alertsContainer, .alert, .modal, .history-panel {
                    display: none !important;
                }
                
                /* Ẩn URL khi in */
                @page {
                    margin-bottom: 2cm;
                    size: A4;
                }
                
                @page :first {
                    margin-top: 2cm;
                }
                
                @page :left {
                    margin-left: 2.5cm;
                    margin-right: 2cm;
                }
                
                @page :right {
                    margin-left: 2cm;
                    margin-right: 2.5cm;
                }
                
                /* Ẩn header và footer */
                @page {
                    @top-left { content: ""; }
                    @top-center { content: ""; }
                    @top-right { content: ""; }
                    @bottom-left { content: ""; }
                    @bottom-center { content: ""; }
                    @bottom-right { content: ""; }
                }
                
                /* Ẩn nội dung trước và sau body */
                body::before, body::after,
                html::before, html::after {
                    content: none !important;
                    display: none !important;
                }
            }
        `;
        document.head.appendChild(style);
        
        // Thêm meta tag để chặn hiển thị URL và header/footer khi in
        const meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
        document.head.appendChild(meta);
        
        // Lưu tiêu đề gốc và đặt tiêu đề trống
        const originalTitle = document.title;
        document.title = " ";
        
        // Đợi DOM cập nhật
        setTimeout(() => {
            window.print();
            
            // Xóa style tạm và khôi phục sau khi in
            setTimeout(() => {
                document.getElementById("print-pdf-style").remove();
                document.head.removeChild(meta);
                document.title = originalTitle;
                restoreAfterPrinting();
            }, 500);
        }, 200);
    }, 500);
}

// Xuất biên bản tổng hợp (tất cả định dạng)
function exportAll() {
    // Tạo modal xác nhận
    const modal = document.createElement('div');
    modal.classList.add('modal');
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">Xuất biên bản</div>
                <span class="modal-close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div class="modal-body">
                <p>Chọn định dạng bạn muốn xuất:</p>
                <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 15px;">
                    <button onclick="exportToWord(); this.closest('.modal').remove();" class="button">
                        <i class="fas fa-file-word"></i> Word (DOCX)
                    </button>
                    <button onclick="exportToExcel(); this.closest('.modal').remove();" class="button">
                        <i class="fas fa-file-excel"></i> Excel (XLSX)
                    </button>
                    <button onclick="exportToPDF(); this.closest('.modal').remove();" class="button">
                        <i class="fas fa-file-pdf"></i> PDF
                    </button>
                    <button onclick="exportToHTML(); this.closest('.modal').remove();" class="button">
                        <i class="fas fa-file-code"></i> HTML
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// In biên bản
function printDocument() {
    // Hiển thị thông báo
    showAlert('info', 'Đang chuẩn bị trang để in...');
    
    setTimeout(() => {
        // Chuẩn bị trang cho việc in
        prepareForPrinting();
        
        // Thêm style tạm thời cho việc in
        const style = document.createElement('style');
        style.id = "print-style";
        style.innerHTML = `
            @page {
                size: A4;
                margin: 2cm 2.5cm;
            }
            
            @media print {
                body {
                    font-family: 'Times New Roman', serif;
                    font-size: 12pt;
                    line-height: 1.5;
                }
                
                /* Đảm bảo ẩn tất cả các UI element */
                .toolbar, .status-bar, .speech-editor, #historyPanel, #alertsContainer, .alert {
                    display: none !important;
                }
                
                /* Ẩn URL khi in */
                @page {
                    margin-bottom: 2cm;
                    size: A4;
                }
                
                @page :first {
                    margin-top: 2cm;
                }
                
                @page :left {
                    margin-left: 2.5cm;
                    margin-right: 2cm;
                }
                
                @page :right {
                    margin-left: 2cm;
                    margin-right: 2.5cm;
                }
                
                /* Ẩn URL và số trang */
                @page {
                    @bottom-left {
                        content: "";
                    }
                    @bottom-center {
                        content: "";
                    }
                    @bottom-right {
                        content: "";
                    }
                }
            }
        `;
        document.head.appendChild(style);
        
        // Thêm meta tag để chặn hiển thị URL và header/footer khi in
        const meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
        document.head.appendChild(meta);
        
        // Đợi DOM cập nhật
        setTimeout(() => {
            window.print();
            
            // Khôi phục sau khi in
            setTimeout(() => {
                document.getElementById("print-style").remove();
                document.head.removeChild(meta);
                restoreAfterPrinting();
            }, 500);
        }, 200);
    }, 500);
}

// Chuẩn bị trang cho việc in
function prepareForPrinting() {
    const speechEditor = document.getElementById('speechEditor');
    const statusBar = document.querySelector('.status-bar');
    const historyPanel = document.getElementById('historyPanel');
    const alertsContainer = document.getElementById('alertsContainer');
    
    // 1. Ẩn speech editor, thanh trạng thái và các phần tử khác không cần thiết khi in
    if (speechEditor) {
        speechEditor.style.display = 'none';
    }
    
    // Ẩn thanh trạng thái (thông tin đã lưu và hướng dẫn phím tắt)
    if (statusBar) {
        statusBar.style.display = 'none';
    }
    
    // Ẩn bảng lịch sử nếu đang hiển thị
    if (historyPanel) {
        historyPanel.classList.remove('open');
    }
    
    // Ẩn container các thông báo
    if (alertsContainer) {
        alertsContainer.style.display = 'none';
    }
    
    // 2. Xử lý các phần .editable
    document.querySelectorAll('.editable').forEach(el => {
        // Chỉ giữ lại nội dung của các trường đã nhập
        if (el.textContent.trim() && !el.classList.contains('editable-placeholder')) {
            el.classList.add('filled');
            
            // Thêm class inline-edit để đảm bảo hiển thị đúng cho các trường inline
            if (el.id && (
                el.id.includes('time') || 
                el.id.includes('minute') || 
                el.id.includes('date') || 
                el.id.includes('month') || 
                el.id.includes('year')
            )) {
                el.classList.add('inline-edit');
            }
            
            // Loại bỏ các ký tự xuống dòng không cần thiết
            const content = el.innerHTML;
            // Chỉ loại bỏ khoảng trắng dư thừa nếu không phải là phần văn bản lớn
            if (!['prosecutor_statement', 'questioning', 'debate', 'defendant_statement'].includes(el.id)) {
                el.innerHTML = content.replace(/\n\s*/g, ' ').trim();
            }
        } else {
            // Ẩn hoàn toàn các trường trống
            el.style.display = 'none';
        }
    });
    
    // 3. Tối ưu hóa khoảng trắng trong các phần văn bản lớn
    const longTextSections = document.querySelectorAll('#prosecutor_statement, #questioning, #debate, #defendant_statement');
    longTextSections.forEach(section => {
        if (section && section.innerHTML.trim()) {
            // Không xử lý quá mức để giữ định dạng
            const content = section.innerHTML
                .replace(/\s{3,}/g, ' ') // Giảm nhiều khoảng trắng liên tiếp
                .replace(/\n{4,}/g, '\n\n\n'); // Giảm nhiều dòng trống quá mức
            
            section.innerHTML = content;
        }
    });
    
    // 4. Xử lý đặc biệt cho phần ngày tháng năm
    document.querySelectorAll('.date-time-section').forEach(section => {
        section.style.whiteSpace = 'nowrap';
        section.style.pageBreakInside = 'avoid';
        section.style.width = '100%';
        
        // Đảm bảo tất cả các phần tử con đều hiển thị inline
        section.querySelectorAll('.editable').forEach(el => {
            el.style.display = 'inline';
            el.style.whiteSpace = 'nowrap';
        });
    });
    
    // 5. Chuẩn bị các phần cấu trúc biên bản
    document.querySelectorAll('.section').forEach(section => {
        // Loại bỏ margin bottom không cần thiết
        section.style.marginBottom = '8px';
    });
    
    // 6. Đảm bảo các phần speech-content hiển thị đúng
    document.querySelectorAll('.speaker-content').forEach(content => {
        content.style.marginBottom = '6px';
    });
    
    // 7. Ẩn thanh toolbar
    const toolbar = document.querySelector('.toolbar');
    if (toolbar) {
        toolbar.style.display = 'none';
    }
    
    // 8. Đánh dấu trang in đã được chuẩn bị
    document.body.classList.add('print-prepared');
}

// Khôi phục lại trạng thái sau khi in
function restoreAfterPrinting() {
    const speechEditor = document.getElementById('speechEditor');
    const statusBar = document.querySelector('.status-bar');
    const alertsContainer = document.getElementById('alertsContainer');
    
    // 1. Khôi phục speech editor
    if (speechEditor) {
        speechEditor.style.display = '';
    }
    
    // Khôi phục thanh trạng thái
    if (statusBar) {
        statusBar.style.display = '';
    }
    
    // Khôi phục container thông báo
    if (alertsContainer) {
        alertsContainer.style.display = '';
    }
    
    // 2. Khôi phục các trường .editable
    document.querySelectorAll('.editable').forEach(el => {
        el.classList.remove('filled');
        el.classList.remove('inline-edit');
        el.style.display = '';
        el.style.whiteSpace = '';
    });
    
    // 3. Khôi phục margin cho các section
    document.querySelectorAll('.section').forEach(section => {
        section.style.marginBottom = '';
        section.style.whiteSpace = '';
        section.style.pageBreakInside = '';
        section.style.width = '';
    });
    
    // 4. Khôi phục margin cho các speaker-content
    document.querySelectorAll('.speaker-content').forEach(content => {
        content.style.marginBottom = '';
    });
    
    // 5. Khôi phục thanh toolbar
    const toolbar = document.querySelector('.toolbar');
    if (toolbar) {
        toolbar.style.display = '';
    }
    
    // 6. Loại bỏ đánh dấu đã chuẩn bị in
    document.body.classList.remove('print-prepared');
}

// Quay lại trang chính
function goBack() {
    window.location.href = '/';
}

// Đóng tất cả các modal khi click ra ngoài
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
};

/**
 * Hiển thị thông báo với animation
 * @param {string} type - Loại thông báo (success, error, info, warning)
 * @param {string} message - Nội dung thông báo
 */
function showAlert(type, message) {
    // Xóa alert cũ nếu có
    const oldAlerts = document.querySelectorAll('.alert');
    oldAlerts.forEach(oldAlert => {
        oldAlert.parentNode.removeChild(oldAlert);
    });
    
    // Tạo alert mới
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.style.opacity = '0';
    alert.innerHTML = `
        <div class="alert-icon">
            <i class="fas fa-${getAlertIcon(type)}"></i>
        </div>
        <div class="alert-content">${message}</div>
        <div class="alert-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </div>
    `;
    
    // Thêm vào body
    document.body.appendChild(alert);
    
    // Hiển thị alert với hiệu ứng fade in
    setTimeout(() => {
        alert.style.opacity = '1';
    }, 10);
    
    // Thêm hiệu ứng mờ dần trước khi xóa
    setTimeout(() => {
        alert.style.opacity = '0';
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 300);
    }, 4000);
}

/**
 * Lấy icon cho từng loại thông báo
 * @param {string} type - Loại thông báo
 * @returns {string} - Tên icon
 */
function getAlertIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        case 'info': return 'info-circle';
        default: return 'bell';
    }
}

/**
 * Hiển thị modal xử lý
 * @param {string} message - Thông báo hiển thị
 * @returns {HTMLElement} - Phần tử modal
 */
function showProcessingModal(message) {
    const modal = document.createElement('div');
    modal.classList.add('processing-modal');
    modal.innerHTML = `
        <div class="processing-content">
            <div class="processing-spinner"></div>
            <div class="processing-message">${message}</div>
        </div>
    `;
    document.body.appendChild(modal);
    return modal;
}

/**
 * Cập nhật thông báo trong modal xử lý
 * @param {HTMLElement} modal - Modal cần cập nhật
 * @param {string} message - Thông báo mới
 */
function updateProcessingModal(modal, message) {
    if (!modal) return;
    const messageEl = modal.querySelector('.processing-message');
    if (messageEl) {
        messageEl.textContent = message;
    }
}

/**
 * Ẩn modal xử lý
 * @param {HTMLElement} modal - Modal cần ẩn
 */
function hideProcessingModal(modal) {
    if (!modal) return;
    document.body.removeChild(modal);
}

/**
 * Lấy nội dung của phần tử hoặc placeholder nếu trống
 * @param {string} elementId - ID của phần tử
 * @returns {string} - Nội dung hoặc placeholder
 */
function getElementTextOrPlaceholder(elementId) {
    const element = document.getElementById(elementId);
    if (element && !element.classList.contains('editable-placeholder')) {
        return element.textContent.trim();
    }
    return '...';
}

/**
 * Lấy thông tin ngày giờ đã định dạng cho phiên tòa
 * @returns {string} - Chuỗi ngày giờ đã định dạng
 */
function getFormattedDateTime() {
    const hearingTime = getElementTextOrPlaceholder('hearing_time');
    const hearingMinute = getElementTextOrPlaceholder('hearing_minute');
    const hearingDate = getElementTextOrPlaceholder('hearing_date');
    const hearingMonth = getElementTextOrPlaceholder('hearing_month');
    const hearingYear = getElementTextOrPlaceholder('hearing_year');
    
    return `Vào hồi ${hearingTime} giờ ${hearingMinute} phút ngày ${hearingDate} tháng ${hearingMonth} năm ${hearingYear}`;
}

/**
 * Lấy thông tin ngày giờ kết thúc đã định dạng
 * @returns {string} - Chuỗi ngày giờ đã định dạng
 */
function getFormattedEndDateTime() {
    const endTime = getElementTextOrPlaceholder('end_time');
    const endMinute = getElementTextOrPlaceholder('end_minute');
    const endDate = getElementTextOrPlaceholder('end_date');
    const endMonth = getElementTextOrPlaceholder('end_month');
    const endYear = getElementTextOrPlaceholder('end_year');
    
    return `vào hồi ${endTime} giờ ${endMinute} phút ngày ${endDate} tháng ${endMonth} năm ${endYear}`;
}

// ===== CHỨC NĂNG LỊCH SỬ BIÊN TẬP (UNDO/REDO) =====

/**
 * Lưu trạng thái ban đầu của biên bản
 */
function saveInitialState() {
    const documentContainer = document.getElementById('documentContainer');
    if (documentContainer) {
        const initialState = documentContainer.innerHTML;
        
        // Tạo bản ghi lịch sử
        editHistory = [{
            state: initialState,
            timestamp: new Date().toISOString(),
            description: 'Trạng thái ban đầu'
        }];
        currentHistoryIndex = 0;
        
        // Cập nhật trạng thái nút Undo, Redo
        updateUndoRedoButtons();
    }
}

/**
 * Lưu trạng thái hiện tại vào lịch sử
 * @param {string} description - Mô tả hành động
 */
function saveState(description = 'Chỉnh sửa biên bản') {
    // Không lưu nếu đang thực hiện undo/redo
    if (isUndoRedoAction) return;
    
    const documentContainer = document.getElementById('documentContainer');
    if (!documentContainer) return;
    
    const currentState = documentContainer.innerHTML;
    
    // Chỉ lưu nếu có sự thay đổi so với trạng thái gần nhất
    if (currentHistoryIndex >= 0 && 
        editHistory[currentHistoryIndex].state === currentState) {
        return;
    }
    
    // Nếu chúng ta đang ở giữa lịch sử (đã undo), loại bỏ các trạng thái phía sau
    if (currentHistoryIndex < editHistory.length - 1) {
        editHistory = editHistory.slice(0, currentHistoryIndex + 1);
    }
    
    // Thêm trạng thái mới
    editHistory.push({
        state: currentState,
        timestamp: new Date().toISOString(),
        description: description
    });
    
    // Giữ kích thước lịch sử trong giới hạn
    if (editHistory.length > MAX_HISTORY_SIZE) {
        editHistory.shift(); // Loại bỏ phần tử đầu tiên
    }
    
    // Cập nhật chỉ mục hiện tại
    currentHistoryIndex = editHistory.length - 1;
    
    // Cập nhật trạng thái nút Undo, Redo
    updateUndoRedoButtons();
    
    // Lưu tạm thời
    autoSaveDocument();
}

/**
 * Thực hiện Undo - hoàn tác thay đổi
 */
function undo() {
    if (currentHistoryIndex <= 0) {
        showAlert('info', 'Không thể hoàn tác thêm');
        return;
    }
    
    isUndoRedoAction = true;
    currentHistoryIndex--;
    
    // Khôi phục trạng thái
    const documentContainer = document.getElementById('documentContainer');
    documentContainer.innerHTML = editHistory[currentHistoryIndex].state;
    
    // Làm sáng container để chỉ ra thay đổi
    documentContainer.classList.add('undo-redo-flash');
    setTimeout(() => {
        documentContainer.classList.remove('undo-redo-flash');
    }, 700);
    
    // Cập nhật trạng thái nút
    updateUndoRedoButtons();
    
    // Cập nhật bảng lịch sử nếu đang mở
    if (document.getElementById('historyPanel').classList.contains('open')) {
        updateHistoryPanel();
    }
    
    showAlert('info', 'Đã hoàn tác: ' + editHistory[currentHistoryIndex].description);
    
    // Reset trạng thái
    setTimeout(() => {
        isUndoRedoAction = false;
    }, 100);
}

/**
 * Thực hiện Redo - làm lại thay đổi
 */
function redo() {
    if (currentHistoryIndex >= editHistory.length - 1) {
        showAlert('info', 'Không thể làm lại thêm');
        return;
    }
    
    isUndoRedoAction = true;
    currentHistoryIndex++;
    
    // Khôi phục trạng thái
    const documentContainer = document.getElementById('documentContainer');
    documentContainer.innerHTML = editHistory[currentHistoryIndex].state;
    
    // Làm sáng container để chỉ ra thay đổi
    documentContainer.classList.add('undo-redo-flash');
    setTimeout(() => {
        documentContainer.classList.remove('undo-redo-flash');
    }, 700);
    
    // Cập nhật trạng thái nút
    updateUndoRedoButtons();
    
    // Cập nhật bảng lịch sử nếu đang mở
    if (document.getElementById('historyPanel').classList.contains('open')) {
        updateHistoryPanel();
    }
    
    showAlert('info', 'Đã làm lại: ' + editHistory[currentHistoryIndex].description);
    
    // Reset trạng thái
    setTimeout(() => {
        isUndoRedoAction = false;
    }, 100);
}

/**
 * Cập nhật trạng thái các nút Undo, Redo
 */
function updateUndoRedoButtons() {
    const undoBtn = document.getElementById('undoBtn');
    const redoBtn = document.getElementById('redoBtn');
    
    if (undoBtn) {
        if (currentHistoryIndex <= 0) {
            undoBtn.classList.add('disabled');
        } else {
            undoBtn.classList.remove('disabled');
        }
    }
    
    if (redoBtn) {
        if (currentHistoryIndex >= editHistory.length - 1) {
            redoBtn.classList.add('disabled');
        } else {
            redoBtn.classList.remove('disabled');
        }
    }
}

/**
 * Thiết lập phím tắt cho Undo, Redo
 */
function setupShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Undo: Ctrl+Z
        if (e.ctrlKey && e.key === 'z') {
            e.preventDefault();
            undo();
        }
        
        // Redo: Ctrl+Y hoặc Ctrl+Shift+Z
        if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'z')) {
            e.preventDefault();
            redo();
        }
        
        // Lưu: Ctrl+S
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveDocument();
        }
    });
}

// ===== CHỨC NĂNG LƯU TẠM THỜI =====

/**
 * Thiết lập tự động lưu
 */
function setupAutoSave() {
    // Khôi phục cài đặt tự động lưu từ localStorage
    const autoSaveSetting = localStorage.getItem('autoSave');
    const autoSaveEnabled = autoSaveSetting === null ? true : autoSaveSetting === 'true';
    
    if (document.getElementById('autoSave')) {
        document.getElementById('autoSave').checked = autoSaveEnabled;
    }
    
    // Tự động lưu định kỳ nếu được bật
    if (autoSaveEnabled) {
        setInterval(autoSaveDocument, AUTO_SAVE_INTERVAL);
    }
    
    // Lưu khi có sự kiện thay đổi
    document.getElementById('documentContainer').addEventListener('input', function(e) {
        // Chỉ lưu trạng thái nếu là sự thay đổi nội dung editable
        if (e.target.classList.contains('editable')) {
            // Thời gian trễ để tránh lưu quá thường xuyên khi đang gõ
            clearTimeout(this.saveTimeout);
            this.saveTimeout = setTimeout(() => {
                saveState('Chỉnh sửa: ' + (e.target.id || 'không xác định'));
            }, 1000);
        }
    });
    
    // Khôi phục biên bản khi trang tải (nếu có)
    restoreDocument();
}

/**
 * Kiểm tra và dọn dẹp localStorage để tránh vượt quá giới hạn
 */
function cleanupLocalStorage() {
    try {
        // Lấy tất cả keys liên quan đến court transcript
        const keys = Object.keys(localStorage).filter(key => key.startsWith('court_transcript_'));

        // Sắp xếp theo thời gian (từ cũ đến mới)
        const sessions = keys.map(key => {
            try {
                const data = JSON.parse(localStorage.getItem(key));
                return {
                    key: key,
                    timestamp: new Date(data.timestamp || 0).getTime(),
                    size: localStorage.getItem(key).length
                };
            } catch (e) {
                return { key: key, timestamp: 0, size: 0 };
            }
        }).sort((a, b) => a.timestamp - b.timestamp);

        // Xóa các session cũ nếu có quá nhiều (giữ lại 5 session gần nhất)
        if (sessions.length > 5) {
            const toDelete = sessions.slice(0, sessions.length - 5);
            toDelete.forEach(session => {
                localStorage.removeItem(session.key);
                console.log(`🗑️ Đã xóa session cũ: ${session.key}`);
            });
        }

        // Tính tổng kích thước storage đã sử dụng
        let totalSize = 0;
        Object.keys(localStorage).forEach(key => {
            totalSize += localStorage.getItem(key).length;
        });

        const sizeMB = totalSize / 1024 / 1024;
        console.log(`📊 LocalStorage usage: ${sizeMB.toFixed(2)} MB`);

        // Cảnh báo nếu gần đầy
        if (sizeMB > STORAGE_CRITICAL_LIMIT) {
            showAlert('error', `Bộ nhớ trình duyệt gần đầy (${sizeMB.toFixed(1)}MB). Vui lòng dọn dẹp dữ liệu cũ.`);
        } else if (sizeMB > STORAGE_WARNING_LIMIT) {
            showAlert('warning', `Bộ nhớ trình duyệt đang cao (${sizeMB.toFixed(1)}MB). Nên dọn dẹp dữ liệu cũ.`);
        }

        return totalSize;
    } catch (error) {
        console.error('Lỗi khi dọn dẹp localStorage:', error);
        return 0;
    }
}

/**
 * Tự động lưu biên bản hiện tại với quản lý storage
 */
function autoSaveDocument() {
    const autoSaveSetting = localStorage.getItem('autoSave');
    const autoSaveEnabled = autoSaveSetting === null ? true : autoSaveSetting === 'true';

    if (!autoSaveEnabled) return;

    const documentContainer = document.getElementById('documentContainer');
    if (!documentContainer) return;

    try {
        const savingDate = new Date();

        // Hiển thị hiệu ứng lưu
        const statusDot = document.querySelector('.save-status .dot');
        if (statusDot) {
            statusDot.classList.remove('saved');
            statusDot.classList.add('pending');
        }

        // Tạo dữ liệu biên bản với editHistory được giới hạn
        const limitedHistory = editHistory.slice(-10); // Chỉ lưu 10 entry gần nhất thay vì 50

        const documentData = {
            content: documentContainer.innerHTML,
            timestamp: savingDate.toISOString(),
            editHistory: limitedHistory,
            currentHistoryIndex: Math.min(currentHistoryIndex, limitedHistory.length - 1)
        };

        const dataString = JSON.stringify(documentData);
        const dataSize = dataString.length;

        // Kiểm tra kích thước dữ liệu (giới hạn ~2MB cho mỗi session)
        if (dataSize > 2 * 1024 * 1024) {
            console.warn('⚠️ Dữ liệu quá lớn, giảm editHistory...');
            // Giảm xuống còn 5 entry
            documentData.editHistory = editHistory.slice(-5);
            documentData.currentHistoryIndex = Math.min(currentHistoryIndex, 4);
        }

        // Thử lưu vào localStorage
        const storageKey = 'court_transcript_' + sessionId;

        try {
            localStorage.setItem(storageKey, JSON.stringify(documentData));
        } catch (quotaError) {
            if (quotaError.name === 'QuotaExceededError') {
                console.warn('🚨 LocalStorage đầy, đang dọn dẹp...');

                // Dọn dẹp storage
                cleanupLocalStorage();

                // Thử lưu lại với dữ liệu tối giản
                const minimalData = {
                    content: documentContainer.innerHTML,
                    timestamp: savingDate.toISOString(),
                    editHistory: editHistory.slice(-3), // Chỉ 3 entry gần nhất
                    currentHistoryIndex: Math.min(currentHistoryIndex, 2)
                };

                try {
                    localStorage.setItem(storageKey, JSON.stringify(minimalData));
                    showAlert('warning', 'Đã dọn dẹp dữ liệu cũ để tiết kiệm bộ nhớ. Một số lịch sử chỉnh sửa có thể bị mất.');
                } catch (finalError) {
                    console.error('❌ Không thể lưu ngay cả sau khi dọn dẹp:', finalError);
                    showAlert('error', 'Bộ nhớ trình duyệt đầy. Vui lòng xuất biên bản và làm mới trang.');
                    return;
                }
            } else {
                throw quotaError;
            }
        }

        // Ghi thời gian lưu gần nhất
        const lastSavedTime = document.getElementById('lastSavedTime');
        if (lastSavedTime) {
            lastSavedTime.textContent = formatDateTime(savingDate);
        }

        // Hiệu ứng flash khi lưu thành công
        documentContainer.classList.add('autosave-flash');
        setTimeout(() => {
            documentContainer.classList.remove('autosave-flash');
        }, 1000);

        // Cập nhật trạng thái đã lưu
        setTimeout(() => {
            if (statusDot) {
                statusDot.classList.remove('pending');
                statusDot.classList.add('saved');
            }
        }, 500);

        console.log('✅ Đã lưu tạm biên bản', savingDate);

        // Cập nhật storage usage display
        updateStorageUsageDisplay();
    } catch (error) {
        console.error('❌ Lỗi khi lưu tạm biên bản:', error);

        if (error.name === 'QuotaExceededError') {
            showAlert('error', 'Bộ nhớ trình duyệt đầy. Vui lòng xuất biên bản và xóa dữ liệu cũ.');
        } else {
            showAlert('error', 'Có lỗi xảy ra khi lưu tạm biên bản');
        }
    }
}

/**
 * Cập nhật hiển thị storage usage
 */
function updateStorageUsageDisplay() {
    try {
        let totalSize = 0;
        Object.keys(localStorage).forEach(key => {
            totalSize += localStorage.getItem(key).length;
        });

        const sizeMB = totalSize / 1024 / 1024;

        // Tìm element để hiển thị (có thể thêm vào status bar)
        let storageDisplay = document.getElementById('storageUsage');
        if (!storageDisplay) {
            // Tạo element mới nếu chưa có
            storageDisplay = document.createElement('span');
            storageDisplay.id = 'storageUsage';
            storageDisplay.style.cssText = 'font-size: 11px; color: #666; margin-left: 10px;';

            // Thêm vào save status area
            const saveStatus = document.querySelector('.save-status');
            if (saveStatus) {
                saveStatus.appendChild(storageDisplay);
            }
        }

        // Cập nhật text và màu sắc
        storageDisplay.textContent = `Storage: ${sizeMB.toFixed(1)}MB`;

        if (sizeMB > STORAGE_CRITICAL_LIMIT) {
            storageDisplay.style.color = '#dc3545'; // Đỏ
        } else if (sizeMB > STORAGE_WARNING_LIMIT) {
            storageDisplay.style.color = '#ffc107'; // Vàng
        } else {
            storageDisplay.style.color = '#666'; // Xám
        }

        storageDisplay.title = `Bộ nhớ trình duyệt đã sử dụng: ${sizeMB.toFixed(2)}MB`;
    } catch (error) {
        console.error('Lỗi khi cập nhật storage display:', error);
    }
}

/**
 * Hiển thị thông tin sử dụng storage
 */
function showStorageInfo() {
    try {
        let totalSize = 0;
        let courtTranscriptSize = 0;
        const courtSessions = [];

        // Tính toán kích thước storage
        Object.keys(localStorage).forEach(key => {
            const itemSize = localStorage.getItem(key).length;
            totalSize += itemSize;

            if (key.startsWith('court_transcript_')) {
                courtTranscriptSize += itemSize;
                try {
                    const data = JSON.parse(localStorage.getItem(key));
                    courtSessions.push({
                        key: key,
                        timestamp: new Date(data.timestamp || 0),
                        size: itemSize
                    });
                } catch (e) {
                    courtSessions.push({
                        key: key,
                        timestamp: new Date(0),
                        size: itemSize
                    });
                }
            }
        });

        // Sắp xếp sessions theo thời gian
        courtSessions.sort((a, b) => b.timestamp - a.timestamp);

        // Tạo modal hiển thị thông tin
        const modal = document.createElement('div');
        modal.classList.add('modal');
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 600px;">
                <div class="modal-header">
                    <div class="modal-title">Thông tin bộ nhớ trình duyệt</div>
                    <span class="modal-close" onclick="this.closest('.modal').remove()">&times;</span>
                </div>
                <div class="modal-body">
                    <div style="margin-bottom: 20px;">
                        <h4>Tổng quan:</h4>
                        <p><strong>Tổng dung lượng đã sử dụng:</strong> ${(totalSize / 1024 / 1024).toFixed(2)} MB</p>
                        <p><strong>Dung lượng biên bản tòa án:</strong> ${(courtTranscriptSize / 1024 / 1024).toFixed(2)} MB</p>
                        <p><strong>Số phiên biên bản:</strong> ${courtSessions.length}</p>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4>Các phiên biên bản:</h4>
                        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                            ${courtSessions.map(session => `
                                <div style="display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee;">
                                    <span>${session.key}</span>
                                    <span>${formatDateTime(session.timestamp)} (${(session.size / 1024).toFixed(1)} KB)</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px;">
                        <button onclick="cleanupOldSessions(); this.closest('.modal').remove();" class="button button-danger">
                            <i class="fas fa-trash"></i> Dọn dẹp phiên cũ
                        </button>
                        <button onclick="clearAllCourtData(); this.closest('.modal').remove();" class="button button-danger">
                            <i class="fas fa-trash-alt"></i> Xóa tất cả biên bản
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    } catch (error) {
        console.error('Lỗi khi hiển thị thông tin storage:', error);
        showAlert('error', 'Không thể hiển thị thông tin bộ nhớ');
    }
}

/**
 * Dọn dẹp các phiên cũ (manual)
 */
function cleanupOldSessions() {
    try {
        const cleaned = cleanupLocalStorage();
        showAlert('success', 'Đã dọn dẹp các phiên biên bản cũ');
        console.log('🧹 Manual cleanup completed');
    } catch (error) {
        console.error('Lỗi khi dọn dẹp:', error);
        showAlert('error', 'Có lỗi xảy ra khi dọn dẹp');
    }
}

/**
 * Xóa tất cả dữ liệu biên bản tòa án
 */
function clearAllCourtData() {
    if (confirm('Bạn có chắc chắn muốn xóa TẤT CẢ dữ liệu biên bản đã lưu? Hành động này không thể hoàn tác!')) {
        try {
            const keys = Object.keys(localStorage).filter(key => key.startsWith('court_transcript_'));
            keys.forEach(key => localStorage.removeItem(key));

            showAlert('success', `Đã xóa ${keys.length} phiên biên bản`);
            console.log('🗑️ Cleared all court transcript data');
        } catch (error) {
            console.error('Lỗi khi xóa dữ liệu:', error);
            showAlert('error', 'Có lỗi xảy ra khi xóa dữ liệu');
        }
    }
}

/**
 * Khôi phục biên bản từ bản lưu tạm
 */
function restoreDocument() {
    try {
        // Tìm bản lưu gần nhất
        let latestSave = null;
        let latestTimestamp = null;
        
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('court_transcript_')) {
                try {
                    const saveData = JSON.parse(localStorage.getItem(key));
                    
                    if (!latestTimestamp || new Date(saveData.timestamp) > new Date(latestTimestamp)) {
                        latestSave = saveData;
                        latestTimestamp = saveData.timestamp;
                    }
                } catch (e) {
                    console.error('Lỗi khi phân tích bản lưu:', e);
                    // Bỏ qua bản lưu không hợp lệ
                    continue;
                }
            }
        }
        
        // Nếu tìm thấy bản lưu, hỏi người dùng có muốn khôi phục không
        if (latestSave) {
            const saveDate = new Date(latestTimestamp);
            const restoreConfirm = confirm(`Tìm thấy bản lưu tạm từ ${formatDateTime(saveDate)}. Bạn có muốn khôi phục không?`);
            
            if (restoreConfirm) {
                // Khôi phục nội dung
                document.getElementById('documentContainer').innerHTML = latestSave.content;
                
                // Khôi phục lịch sử
                if (latestSave.editHistory && latestSave.editHistory.length > 0) {
                    editHistory = latestSave.editHistory;
                    currentHistoryIndex = latestSave.currentHistoryIndex || (editHistory.length - 1);
                    updateUndoRedoButtons();
                }
                
                showAlert('success', 'Đã khôi phục biên bản từ bản lưu tạm');
            }
        }
    } catch (error) {
        console.error('Lỗi khi khôi phục biên bản:', error);
    }
}

/**
 * Làm mới biên bản (xóa tất cả nội dung và lịch sử)
 */
function resetDocument() {
    // Hiển thị hộp thoại xác nhận
    const confirmReset = confirm('Bạn có chắc chắn muốn làm mới biên bản? Tất cả nội dung sẽ bị xóa và không thể khôi phục.');
    
    if (!confirmReset) return;
    
    try {
        // Xóa tất cả nội dung đã nhập trong các trường editable
        document.querySelectorAll('.editable').forEach(el => {
            el.innerHTML = '';
            el.classList.add('editable-placeholder');
            el.textContent = el.getAttribute('data-placeholder');
        });
        
        // Điền ngày giờ mới
        if (document.getElementById('autoFill') && document.getElementById('autoFill').checked) {
            autoFillDateTime();
        }
        
        // Làm mới lịch sử
        saveInitialState();
        
        // Thông báo
        showAlert('success', 'Đã làm mới biên bản thành công');
    } catch (error) {
        console.error('Lỗi khi làm mới biên bản:', error);
        showAlert('error', 'Có lỗi xảy ra khi làm mới biên bản');
    }
}

/**
 * Định dạng ngày giờ thành chuỗi thân thiện
 * @param {Date} date - Đối tượng Date
 * @returns {string} - Chuỗi ngày giờ đã định dạng
 */
function formatDateTime(date) {
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')} - ${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
}

// ===== BẢNG LỊCH SỬ CHỈNH SỬA =====

/**
 * Hiển thị/ẩn bảng lịch sử chỉnh sửa
 */
function toggleHistoryPanel() {
    const historyPanel = document.getElementById('historyPanel');
    historyPanel.classList.toggle('open');
    
    if (historyPanel.classList.contains('open')) {
        // Cập nhật danh sách lịch sử
        updateHistoryPanel();
    }
}

/**
 * Cập nhật nội dung bảng lịch sử
 */
function updateHistoryPanel() {
    const historyPanelContent = document.getElementById('historyPanelContent');
    historyPanelContent.innerHTML = '';
    
    if (editHistory.length === 0) {
        historyPanelContent.innerHTML = '<div class="empty-state">Chưa có lịch sử chỉnh sửa nào.</div>';
        return;
    }
    
    // Tạo mục lịch sử cho mỗi bản ghi
    editHistory.forEach((historyItem, index) => {
        const historyItemElement = document.createElement('div');
        historyItemElement.className = 'history-item';
        
        if (index === currentHistoryIndex) {
            historyItemElement.classList.add('active');
        }
        
        const timestamp = new Date(historyItem.timestamp);
        const formattedTime = formatDateTime(timestamp);
        
        historyItemElement.innerHTML = `
            <div class="history-item-description">${historyItem.description}</div>
            <div class="history-item-time">${formattedTime}</div>
        `;
        
        // Thêm sự kiện click để khôi phục về trạng thái này
        historyItemElement.addEventListener('click', () => {
            if (index !== currentHistoryIndex) {
                jumpToHistoryState(index);
            }
        });
        
        historyPanelContent.appendChild(historyItemElement);
    });
}

/**
 * Nhảy đến một trạng thái lịch sử cụ thể
 * @param {number} index - Chỉ mục trạng thái lịch sử
 */
function jumpToHistoryState(index) {
    if (index < 0 || index >= editHistory.length) return;
    
    isUndoRedoAction = true;
    
    // Khôi phục trạng thái
    const documentContainer = document.getElementById('documentContainer');
    documentContainer.innerHTML = editHistory[index].state;
    
    // Cập nhật chỉ mục hiện tại
    currentHistoryIndex = index;
    
    // Làm sáng container để chỉ ra thay đổi
    documentContainer.classList.add('undo-redo-flash');
    setTimeout(() => {
        documentContainer.classList.remove('undo-redo-flash');
    }, 700);
    
    // Cập nhật UI
    updateUndoRedoButtons();
    updateHistoryPanel();
    
    showAlert('info', 'Đã khôi phục biên bản về phiên bản: ' + editHistory[index].description);
    
    setTimeout(() => {
        isUndoRedoAction = false;
    }, 100);
}
