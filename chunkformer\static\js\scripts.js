/*
 * scripts.js - JavaScript cho ứng dụng nhận dạng giọng nói
 * Phiên bản: 1.0.4 (S<PERSON>a lỗi lưu kết quả)
 * M<PERSON>u sắc: Đen-Trắng
 */

// Biến để kiểm soát việc cập nhật tự động
let autoUpdateEnabled = true;
let eventSource; // Biến cho SSE

// Biến để lưu trữ các kết quả từ server
let speechResults = [];

// Biến để theo dõi số lượng kết quả trước đó
let prevResultsCount = 0;

// Biến theo dõi dropdown đang mở
let activeDropdownIndex = -1;
let activeDropdownValue = null;

// Biến cho chức năng gộp lời nói
let mergeSameSpeaker = false;
let mergeAllSpeakers = false;

// Audio monitoring variables
let audioContext = null;
let analyser = null;
let microphone = null;
let dataArray = null;
let silenceThreshold = 20; // Default silence threshold (0-100)
let isMonitoringAudio = false;
let audioMonitoringInterval = null;

// Audio device management variables
let availableAudioDevices = [];
let selectedAudioDeviceId = null;

// Biến theo dõi vị trí chuột để kiểm soát auto-scroll
let isMouseOverResults = false;

// Cache và debouncing cho speakers
let speakersCache = null;
let speakersCacheTime = 0;
let fetchSpeakersTimeout = null;
const SPEAKERS_CACHE_DURATION = 10000; // Tăng lên 10 seconds để giảm load

// Tracking cho delete operations
let deletionInProgress = new Set();

// Tracking cho page reload để tránh multiple initializations
let isPageReloading = false;

// Flag để kiểm soát việc reload dropdown - chỉ reload khi thực sự cần thiết
// CHỈ reload dropdown khi:
// 1. Page reload/refresh (làm mới trang)
// 2. Speaker name update (đổi tên người nói ở Hồ Sơ Người Nói)
// KHÔNG reload dropdown khi:
// - Tab switching (chuyển tab)
// - Speaker creation (tạo profile mới)
// - Speaker deletion (xóa profile)
// - Auto profile creation (tạo profile tự động)
let shouldReloadDropdowns = false;

// Lưu trữ speaker selections để restore sau page reload
let savedDropdownSelections = new Map();

// Flag để tạm dừng restore khi user đang tương tác
let userInteracting = false;
let interactionTimeout = null;

// Debouncing cho restore operations
let restoreTimeout = null;

// Speaker selection variables
let selectedSpeakers = new Set();
const SELECTED_SPEAKERS_KEY = 'selectedSpeakers';

document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 Page loaded - DOMContentLoaded event');

    // Prevent multiple initializations during reload
    if (isPageReloading) {
        console.log('🔄 Page reload in progress, skipping duplicate initialization');
        return;
    }
    isPageReloading = true;

    // Detect page reload và clear cache chỉ khi thực sự cần thiết
    // Sử dụng performance.navigation để detect reload chính xác hơn
    const isReload = performance.navigation.type === performance.navigation.TYPE_RELOAD;
    
    if (isReload) {
        console.log('🔄 Page reload detected - clearing selective cache');
        // Chỉ clear speakers cache, không clear tất cả
        speakersCache = null;
        speakersCacheTime = 0;
        // Đặt flag để reload dropdown khi reload trang
        shouldReloadDropdowns = true;
    } else {
        console.log('🆕 Fresh page load - initializing');
        // Đặt flag để reload dropdown khi load trang lần đầu
        shouldReloadDropdowns = true;
    }

    // Các tab
    setupTabs();

    // Thiết lập thanh resize cho container kết quả
    setupResizableContainer();

    // Khởi tạo
    loadInitialData();
    
    // Thiết lập các modal
    setupModals();
    
    // Thiết lập side panel
    setupSidePanel();
    
    // Load cài đặt từ server (settings.json thông qua template)
    loadSettingsFromServer();
    
    // Khởi tạo cài đặt gộp lời nói
    initMergeSpeakerSetting();

    // Khởi tạo speaker selection
    initSpeakerSelection();

    // Khởi tạo audio monitoring
    initAudioMonitoring();

    // Thêm event listener cho Page Visibility API để tự động refresh khi quay lại trang
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            // Trang vừa được hiển thị lại, refresh settings nhẹ nhàng
            console.log('Page became visible, refreshing settings...');
            loadSettingsFromServer();
        }
    });

    // Reset page reloading flag after initialization
    setTimeout(() => {
        isPageReloading = false;
    }, 1000);
    
    // Thêm sự kiện auto-save cho form cài đặt
    const settingsForm = document.getElementById('settingsForm');
    if (settingsForm) {
        // Hàm để gửi cài đặt riêng lẻ lên server
        function updateSetting(name, value) {
            fetch('/update_setting', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: name,
                    value: value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                console.error('Lỗi khi cập nhật cài đặt:', error);
                showAlert('error', 'Lỗi khi cập nhật cài đặt');
            });
        }

        // Thêm event listeners cho tất cả input number
        const numberInputs = settingsForm.querySelectorAll('input[type="number"]');
        numberInputs.forEach(input => {
            input.addEventListener('change', function() {
                const value = parseFloat(this.value);
                updateSetting(this.name, value);

                // Special handling for similarity threshold
                if (this.name === 'similarity_threshold') {
                    onSimilarityThresholdChange(value);
                }

                // Cài đặt sẽ được load từ settings.json, không cần lưu localStorage
            });
        });

        // Thêm event listeners cho tất cả checkbox
        const checkboxInputs = settingsForm.querySelectorAll('input[type="checkbox"]');
        checkboxInputs.forEach(input => {
            input.addEventListener('change', function() {
                const value = this.checked;
                updateSetting(this.name, value);

                // Special handling for auto profile creation
                if (this.name === 'auto_profile_creation') {
                    onAutoProfileCreationChange(value);
                }

                // Cài đặt sẽ được load từ settings.json, không cần lưu localStorage

                // Xử lý đặc biệt cho merge_same_speaker và merge_all_speakers
                if (this.name === 'merge_same_speaker') {
                    mergeSameSpeaker = value;
                    // Cập nhật hiển thị kết quả khi thay đổi cài đặt
                    const resultsInner = document.querySelector('.results-inner');
                    if (resultsInner) {
                        resultsInner.innerHTML = '';
                        displayResults(speechResults, resultsInner);
                    }
                } else if (this.name === 'merge_all_speakers') {
                    mergeAllSpeakers = value;
                    // Cập nhật hiển thị kết quả khi thay đổi cài đặt
                    const resultsInner = document.querySelector('.results-inner');
                    if (resultsInner) {
                        resultsInner.innerHTML = '';
                        displayResults(speechResults, resultsInner);
                    }
                }
            });
        });

        // Ngăn chặn form submit thông thường
        settingsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            showAlert('info', 'Cài đặt được lưu tự động khi có thay đổi');
        });
    }
});

// Thiết lập tabs
function setupTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    function setActiveTab(tab) {
        // Lưu tab đang mở vào localStorage
        localStorage.setItem("activeTab", tab.dataset.tab);
        
        // Loại bỏ active class từ tất cả buttons và contents
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));
        
        // Thêm active class cho tab được chọn
        tab.classList.add('active');
        document.getElementById(tab.dataset.tab).classList.add('active');
        
        // Tải dữ liệu nếu cần
        if (tab.dataset.tab === 'speakersTab') {
            fetchSpeakers(); // Use cache for tab switching - KHÔNG reload dropdown
        }
    }
    
    // Thêm event listener cho các tab
    tabButtons.forEach(tab => {
        tab.addEventListener('click', () => setActiveTab(tab));
    });
    
    // Kiểm tra tab đã lưu hoặc mặc định là resultsTab
    const savedTab = localStorage.getItem("activeTab") || "resultsTab";
    
    // Đảm bảo chỉ chọn một trong hai tab có sẵn
    let defaultTab;
    if (savedTab === "resultsTab" || savedTab === "speakersTab") {
        defaultTab = document.querySelector(`.tab-button[data-tab="${savedTab}"]`);
    } else {
        defaultTab = document.querySelector('.tab-button[data-tab="resultsTab"]');
    }
    
    if (defaultTab) {
        setActiveTab(defaultTab);
    }
    
    // Tải danh sách người nói ngay khi khởi động, bất kể tab nào đang active
    fetchSpeakers(true); // Force refresh on startup
}

// Thiết lập container kết quả
function setupResizableContainer() {
    const container = document.querySelector('.results-container');
    
    // Kiểm tra xem có chiều cao đã lưu không
    const savedHeight = localStorage.getItem('resultsContainerHeight');
    if (savedHeight) {
        container.style.height = savedHeight;
    }
    
    // Thêm event listeners để theo dõi vị trí chuột để kiểm soát auto-scroll
    if (container) {
        container.addEventListener('mouseenter', function() {
            isMouseOverResults = true;
        });
        
        container.addEventListener('mouseleave', function() {
            isMouseOverResults = false;
        });
    }
}

// Lấy dữ liệu ban đầu - optimized để tránh reload treo
function loadInitialData() {
    console.log('🚀 Loading initial data...');

    // Kiểm tra nếu đang loading để tránh duplicate calls
    if (window.isLoadingInitialData) {
        console.log('⚠️ Initial data loading already in progress, skipping...');
        return;
    }
    window.isLoadingInitialData = true;

    // Sử dụng cache intelligently
    const now = Date.now();
    const shouldUseCache = speakersCache && (now - speakersCacheTime) < SPEAKERS_CACHE_DURATION;
    
    console.log('🔄 Loading data with optimized strategy...');

    // Load results first (most important)
    fetchResults('initial_load')
        .then(() => {
            // console.log('✅ Results loaded successfully');

            // Load speakers sau (less critical)
            return fetchSpeakers(!shouldUseCache); // Only force refresh if cache expired
        })
        .then(() => {
            // console.log('✅ All initial data loaded successfully');
        })
        .catch(error => {
            console.error('❌ Error loading initial data:', error);
            showAlert('warning', 'Có lỗi khi tải dữ liệu ban đầu. Một số tính năng có thể không hoạt động.');
        })
        .finally(() => {
            window.isLoadingInitialData = false;

            // Luôn refresh speaker names khi reload trang để đảm bảo đồng bộ
            if (speechResults.length > 0) {
                console.log('🔄 Refreshing speaker names after page load...');
                setTimeout(() => {
                    refreshAllSpeakerNamesInResults()
                        .then(() => {
                            // console.log('✅ Speaker names refreshed, reloading results...');
                            // Reload results sau khi refresh speaker names
                            return fetchResults();
                        })
                        .then(() => {
                            // Reset flag sau khi load trang hoàn tất
                            shouldReloadDropdowns = false;
                            // console.log('✅ Page load completed, dropdown reload flag reset');
                        })
                        .catch(error => {
                            console.error('Warning: Could not refresh speaker names:', error);
                            // Reset flag ngay cả khi có lỗi
                            shouldReloadDropdowns = false;
                        });
                }, 1000); // Đợi 1 giây để đảm bảo speakers đã load xong
            }
        });

    // Thiết lập SSE
    setupSSE();
}

// Thiết lập kết nối SSE
function setupSSE() {
    // Đóng kết nối cũ nếu có
    if (eventSource) {
        eventSource.close();
    }
    
    // Khởi tạo kết nối SSE
    eventSource = new EventSource('/stream');
    
    // Lắng nghe sự kiện new_result
    eventSource.addEventListener('new_result', function(event) {
        if (!autoUpdateEnabled) return;

        const data = JSON.parse(event.data);
        const result = data.result;
        const index = data.index;

        // Nếu có all_results, cập nhật toàn bộ danh sách
        if (data.all_results && Array.isArray(data.all_results)) {
            console.log('📋 Updating all results from new_result event');
            updateResultsDisplay(data.all_results);
            return;
        }
        
        // Kiểm tra xem đã có kết quả này trong mảng chưa
        if (index >= speechResults.length) {
            speechResults.push(result);
            
            // Kiểm tra xem có đang chỉnh sửa không
            const isEditingText = document.querySelector('.result-text:focus') !== null;
            
            if (!isEditingText) {
                const resultsContainer = document.getElementById('resultsContainer');
                const resultsInner = document.querySelector('.results-inner');
                
                // Xóa trạng thái trống nếu có
                const emptyState = resultsInner.querySelector('.empty-state');
                if (emptyState) {
                    resultsInner.innerHTML = '';
                }
                
                if (mergeSameSpeaker) {
                    // Cập nhật hiển thị với chế độ gộp lời nói
                    resultsInner.innerHTML = '';
                    displayResults(speechResults, resultsInner);

                    // Highlight all results from last 2 seconds
                    setTimeout(() => highlightRecentResults(), 100);
                } else {
                    // Thêm kết quả mới
                    renderResultCard(resultsInner, result, index, false);

                    // Highlight all results from last 2 seconds
                    setTimeout(() => highlightRecentResults(), 100);
                }
                
                // Cuộn xuống cuối chỉ khi chuột không hover trên vùng kết quả
                if (!isMouseOverResults) {
                    resultsContainer.scrollTop = resultsContainer.scrollHeight;
                }
            }
            
            prevResultsCount = speechResults.length;
            
            // QUAN TRỌNG: Lưu kết quả mới lên server
            saveResults()
                .then(() => {
                    console.log('Đã lưu kết quả mới từ SSE');
                })
                .catch(error => {
                    console.error('Lỗi khi lưu kết quả từ SSE:', error);
                });
            
            // Chỉ cập nhật cache speakers khi có người nói mới được tạo profile tự động
            // KHÔNG reload dropdown vì chỉ là tạo profile mới, không đổi tên
            if (result.includes('Đã tạo profile tự động:') || result.includes('profile tự động')) {
                console.log('🆕 New speaker profile detected, refreshing speakers cache only...');
                setTimeout(() => fetchSpeakers(true), 500); // Force refresh cache only
            }
        }
    });
    
    // Lắng nghe sự kiện refresh_results
    eventSource.addEventListener('refresh_results', function(event) {
        // Only refresh if auto-update is enabled
        if (!autoUpdateEnabled) {
            console.log('Auto-update is disabled. Ignoring refresh_results event.');
            return;
        }
        
        try {
            // Parse data từ event để xem action cụ thể
            const eventData = JSON.parse(event.data);
            console.log('Received refresh_results event:', eventData);
            
            if (eventData.action === 'refresh_all') {
                // Debounce refresh_all để tránh quá nhiều calls
                const now = Date.now();
                if (!window.lastRefreshAll || (now - window.lastRefreshAll) > 2000) {
                    console.log('Processing refresh_all action for speaker name update');
                    window.lastRefreshAll = now;

                    // Đầu tiên cập nhật danh sách người nói
                    fetchSpeakers(true).then(() => { // Force refresh
                        // Sau đó cập nhật kết quả
                        return fetchResults();
                    }).then(() => {
                        // CHỈ reload dropdown nếu flag được đặt (từ speaker name update)
                        if (shouldReloadDropdowns) {
                            console.log('🔄 Reloading dropdowns due to speaker name update...');
                            return refreshAllDropdowns();
                        } else {
                            console.log('ℹ️ Skipping dropdown reload - not needed for this refresh_all');
                            return Promise.resolve();
                        }
                    }).then(() => {
                        console.log('Speaker name update completed successfully');
                        shouldReloadDropdowns = false; // Reset flag
                    }).catch(error => {
                        console.error('Error during speaker name update process:', error);
                        shouldReloadDropdowns = false; // Reset flag on error
                    });
                } else {
                    console.log('⚠️ Skipping refresh_all - too recent (debouncing)');
                }
            } else if (eventData.action === 'sync_required') {
                // Xử lý sync khi web_results_lock bị timeout - WITH DEBOUNCING
                const now = Date.now();
                if (!window.lastSyncRequired || (now - window.lastSyncRequired) > 3000) {
                    console.log('Processing sync_required action - refreshing results');
                    window.lastSyncRequired = now;
                    fetchResults().catch(error => {
                        console.error('Error during sync_required refresh:', error);
                    });
                } else {
                    console.log('⚠️ Skipping sync_required - too recent (debouncing)');
                }
            } else if (eventData.action === 'speaker_name_update') {
                // Xử lý cập nhật tên speaker khi lock timeout
                console.log('Processing speaker_name_update:', eventData.old_name, '->', eventData.new_name);
                
                // Cập nhật local results nếu có
                if (speechResults && speechResults.length > 0) {
                    let updated = false;
                    for (let i = 0; i < speechResults.length; i++) {
                        if (speechResults[i].includes(`[${eventData.old_name}]`)) {
                            speechResults[i] = speechResults[i].replace(`[${eventData.old_name}]`, `[${eventData.new_name}]`);
                            updated = true;
                        }
                    }
                    
                    if (updated) {
                        // Cập nhật hiển thị
                        const resultsInner = document.querySelector('.results-inner');
                        if (resultsInner) {
                            resultsInner.innerHTML = '';
                            displayResults(speechResults, resultsInner);
                        }
                        // console.log('✅ Updated speaker names in local results');
                    }
                }
                
                // Refresh speakers cache
                fetchSpeakers(true).catch(error => {
                    console.error('Error refreshing speakers after name update:', error);
                });
            } else {
                // Chỉ xử lý các action khác nếu thực sự cần thiết
                // Tránh refresh không cần thiết cho các sự kiện thường
                console.log('Received non-refresh_all action, skipping general refresh');
            }
        } catch (error) {
            console.log('Error parsing refresh_results event data, limiting fallback refresh:', error);
            // Chỉ refresh results, không cần refresh speakers trừ khi thực sự cần thiết
            fetchResults();
        }
    });
    
    // Lắng nghe sự kiện speaker_update
    eventSource.addEventListener('speaker_update', function(event) {
        try {
            const eventData = JSON.parse(event.data);
            console.log('Received speaker_update event:', eventData);
            
            // Tự động tải lại danh sách người nói khi có thay đổi
            switch(eventData.action) {
                case 'speaker_created':
                    console.log('New speaker created:', eventData.speaker_name);

                    // Tự động chọn speaker mới được tạo
                    autoSelectSpeaker(eventData.speaker_name);

                    // Chỉ cập nhật cache speakers, KHÔNG reload dropdown
                    fetchSpeakers(true).then(() => { // Force refresh for new speaker
                        showAlert('success', `Đã tạo hồ sơ người nói mới: ${eventData.speaker_name} (đã tự động chọn)`);
                        fetchResults();
                    }).catch(error => {
                        console.error('Error fetching speakers after creation:', error);
                    });
                    break;

                case 'speaker_updated':
                    console.log('Speaker updated:', eventData.old_name, '->', eventData.new_name);

                    // Lưu mapping tên cũ -> tên mới để preserve dropdown selections
                    speakerNameMapping.set(eventData.old_name, eventData.new_name);
                    console.log(`💾 Saved speaker name mapping: "${eventData.old_name}" -> "${eventData.new_name}"`);

                    // Cập nhật selectedSpeakers Set để preserve speaker selection
                    if (selectedSpeakers.has(eventData.old_name)) {
                        selectedSpeakers.delete(eventData.old_name);
                        selectedSpeakers.add(eventData.new_name);
                        console.log(`🔄 Updated selectedSpeakers: "${eventData.old_name}" -> "${eventData.new_name}"`);

                        // Lưu trạng thái mới
                        saveSpeakerSelection();

                        // Cập nhật server với danh sách mới
                        updateServerSpeakerSelection();
                    }

                    // CHỈ TRƯỜNG HỢP NÀY mới reload dropdown vì đổi tên người nói
                    shouldReloadDropdowns = true;
                    fetchSpeakers(true).then(() => { // Force refresh for updated speaker
                        showAlert('success', `Đã cập nhật tên từ "${eventData.old_name}" thành "${eventData.new_name}"`);
                        // Reload dropdown vì tên đã thay đổi - mapping sẽ được sử dụng trong refreshAllDropdowns()
                        return refreshAllDropdowns();
                    }).then(() => {
                        console.log('✅ Dropdown reloaded after speaker name update with preserved selections');

                        // Cập nhật UI để reflect speaker selection changes
                        updateSpeakerSelectionUI();

                        shouldReloadDropdowns = false; // Reset flag
                    }).catch(error => {
                        console.error('Error fetching speakers after update:', error);
                        shouldReloadDropdowns = false; // Reset flag on error
                        speakerNameMapping.clear(); // Clear mapping on error
                    });
                    break;

                case 'speaker_deleted':
                    console.log('Speaker deleted:', eventData.speaker_name);
                    // Chỉ cập nhật cache speakers, KHÔNG reload dropdown
                    fetchSpeakers(true).then(() => { // Force refresh for deleted speaker
                        showAlert('info', `Đã xóa hồ sơ người nói: ${eventData.speaker_name}`);
                    }).catch(error => {
                        console.error('Error fetching speakers after deletion:', error);
                    });
                    break;

                case 'all_speakers_deleted':
                    console.log('All speakers deleted');
                    // Chỉ cập nhật cache speakers, KHÔNG reload dropdown
                    fetchSpeakers(true).then(() => { // Force refresh for all deleted
                        showAlert('info', 'Đã xóa tất cả hồ sơ người nói');
                    }).catch(error => {
                        console.error('Error fetching speakers after bulk deletion:', error);
                    });
                    break;

                default:
                    console.log('Unknown speaker_update action:', eventData.action);
                    // Chỉ cập nhật cache speakers, KHÔNG reload dropdown
                    fetchSpeakers(true).catch(error => { // Force refresh for unknown action
                        console.error('Error fetching speakers for unknown action:', error);
                    });
            }
        } catch (error) {
            console.error('Error parsing speaker_update event data:', error);
            // Trong trường hợp lỗi, vẫn cố gắng tải lại danh sách
            fetchSpeakers(true).catch(fetchError => { // Force refresh on error
                console.error('Error fetching speakers after parse error:', fetchError);
            });
        }
    });
    
    // Lắng nghe sự kiện settings_reloaded
    eventSource.addEventListener('message', function(event) {
        try {
            const eventData = JSON.parse(event.data);
            
            if (eventData.action === 'settings_reloaded') {
                console.log('Settings reloaded from file:', eventData.settings);
                
                // Cập nhật UI với cài đặt mới
                updateSettingsUI(eventData.settings);
                
                // Hiển thị thông báo
                showAlert('info', '⚡ Cài đặt đã được tự động tải lại từ file settings.json');
            }
        } catch (error) {
            console.error('Error parsing settings_reloaded event:', error);
        }
    });
    
    // Xử lý lỗi kết nối
    eventSource.onerror = function(error) {
        console.error('SSE Error:', error);
        // Thử kết nối lại sau 5 giây
        setTimeout(setupSSE, 5000);
    };
}

// Hàm bật/tắt cập nhật tự động
function toggleAutoUpdate() {
    // Gọi API để toggle trạng thái pause
    fetch('/toggle_pause', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Toggle trạng thái local sau khi server xác nhận
            autoUpdateEnabled = !autoUpdateEnabled;
            const btn = document.getElementById('toggleUpdateBtn');
            
            if (autoUpdateEnabled) {
                btn.innerHTML = '<i class="fas fa-pause"></i> Tạm dừng lắng nghe';
                showAlert('success', 'Đã tiếp tục lắng nghe');
            } else {
                btn.innerHTML = '<i class="fas fa-play"></i> Tiếp tục lắng nghe';
                showAlert('info', 'Đã tạm dừng lắng nghe. Không tạo hồ sơ người nói mới.');
            }
        } else {
            showAlert('error', 'Lỗi khi thay đổi trạng thái: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Lỗi kết nối khi thay đổi trạng thái');
    });
}

// Thiết lập Modal
function setupModals() {
    // Sửa đổi: Không còn đóng modal khi click ra ngoài cho "editModal"
    window.addEventListener('click', (event) => {
        const modals = document.querySelectorAll('.modal:not(#editModal)');
        modals.forEach(modal => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
    
    // Đóng modal khi click nút đóng
    document.querySelectorAll('.modal-close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });
}

function closeAllModals() {
    document.querySelectorAll('.modal').forEach(modal => {
        modal.style.display = 'none';
        
        // Dừng audio nếu có
        const audioPlayer = modal.querySelector('audio');
        if (audioPlayer) {
            audioPlayer.pause();
        }
    });
}

function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
}

// Lấy kết quả nhận dạng giọng nói
function fetchResults() {
    // Save active dropdown if any
    const activeDropdown = document.querySelector('.speaker-dropdown:focus');
    if (activeDropdown) {
        activeDropdownIndex = parseInt(activeDropdown.getAttribute('data-index'));
        activeDropdownValue = activeDropdown.value;
    } else {
        activeDropdownIndex = -1;
        activeDropdownValue = null;
    }
    
    return fetch('/results')
        .then(response => response.json())
        .then(data => {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsInner = document.querySelector('.results-inner');
            
            // Update results storage
            speechResults = data.results || [];
            
            if (speechResults.length === 0) {
                resultsInner.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-microphone-slash"></i>
                        <div class="empty-state-text">Chưa có kết quả nhận dạng nào</div>
                        <div>Hãy nói gì đó để bắt đầu nhận dạng giọng nói</div>
                    </div>
                `;
                return data;
            }
            
            // Save current scroll position and check if at bottom
            const isScrolledToBottom = resultsContainer.scrollHeight - resultsContainer.clientHeight <= resultsContainer.scrollTop + 5;
            
            // Check if any element is currently being edited
            const isEditingText = document.querySelector('.result-text:focus') !== null;
            
            // If editing text, only add new results
            if (isEditingText && !mergeSameSpeaker) {
                // Only handle this case for non-merged view
                const newResults = speechResults.slice(prevResultsCount);
                if (newResults.length > 0) {
                    if (mergeSameSpeaker) {
                        // Refresh toàn bộ hiển thị
                        resultsInner.innerHTML = '';
                        displayResults(speechResults, resultsInner);

                        // Highlight all results from last 2 seconds
                        setTimeout(() => highlightRecentResults(), 100);
                    } else {
                        newResults.forEach((result, index) => {
                            renderResultCard(resultsInner, result, prevResultsCount + index, false);
                        });

                        // Highlight all results from last 2 seconds
                        setTimeout(() => highlightRecentResults(), 100);
                    }
                }
            } else {
                // Display all results
                resultsInner.innerHTML = '';
                displayResults(speechResults, resultsInner);
                
                // Restore dropdown focus if needed
                if (activeDropdownIndex >= 0) {
                    setTimeout(() => {
                        const resultCards = document.querySelectorAll('.result-card');
                        if (activeDropdownIndex < resultCards.length) {
                            const dropdown = resultCards[activeDropdownIndex].querySelector('.speaker-dropdown');
                            if (dropdown) {
                                dropdown.focus();
                                dropdown.value = activeDropdownValue;
                            }
                        }
                    }, 50);
                }
            }
            
            // Scroll to bottom if previously at bottom or new results added AND mouse is not over results
            if ((isScrolledToBottom || prevResultsCount < speechResults.length) && !isMouseOverResults) {
                resultsContainer.scrollTop = resultsContainer.scrollHeight;
            }
            
            // Update previous results count
            prevResultsCount = speechResults.length;
            
            return data; // Trả về data để có thể chain promises
        })
        .catch(error => {
            console.error('Lỗi khi tải kết quả:', error);
            throw error; // Re-throw để promise chain có thể xử lý lỗi
        });
}

// Hàm cập nhật hiển thị kết quả từ dữ liệu đã có
function updateResultsDisplay(results) {
    console.log('📋 Updating results display with', results.length, 'results');

    const resultsContainer = document.getElementById('resultsContainer');
    const resultsInner = document.querySelector('.results-inner');

    // Update results storage
    speechResults = results || [];

    if (speechResults.length === 0) {
        resultsInner.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-microphone-slash"></i>
                <div class="empty-state-text">Chưa có kết quả nhận dạng nào</div>
                <div>Hãy nói gì đó để bắt đầu nhận dạng giọng nói</div>
            </div>
        `;
        return;
    }

    // Save current scroll position and check if at bottom
    const isScrolledToBottom = resultsContainer.scrollHeight - resultsContainer.clientHeight <= resultsContainer.scrollTop + 5;

    // Display all results
    resultsInner.innerHTML = '';
    displayResults(speechResults, resultsInner);

    // Scroll to bottom if previously at bottom AND mouse is not over results
    if (isScrolledToBottom && !isMouseOverResults) {
        resultsContainer.scrollTop = resultsContainer.scrollHeight;
    }

    // Update previous results count
    prevResultsCount = speechResults.length;
}

// Hàm render kết quả thành card
function renderResultCard(container, result, index, isLatest = false) {
    // const debugText = typeof result === 'string' ? result : (result.text || 'Unknown');
    // console.log(`🎨 renderResultCard: index=${index}, isLatest=${isLatest}, result="${debugText.substring(0, 50)}..."`);

    const resultCard = document.createElement('div');
    resultCard.className = 'result-card';
    resultCard.dataset.index = index;
    resultCard.dataset.timestamp = Date.now(); // Add timestamp for recent highlighting

    // Add latest class if this is the newest result
    if (isLatest) {
        // console.log('✅ Adding "latest" class to result card');
        resultCard.classList.add('latest');
    } else {
        // console.log('⚪ Not adding "latest" class (isLatest=false)');
    }
    
    // Xử lý kết quả có thông tin người nói và timestamp
    let speakerName = "Người lạ";
    let resultText = result;
    let timestamp = new Date().toLocaleTimeString(); // Mặc định
    
    // Kiểm tra format mới với timestamp: [HH:MM:SS] [Tên người nói] Content
    const newFormatMatch = result.match(/^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$/);
    if (newFormatMatch) {
        timestamp = newFormatMatch[1];
        speakerName = newFormatMatch[2];
        resultText = newFormatMatch[3];
    } else if (result.includes('[') && result.includes(']')) {
        // Format cũ: [Tên người nói] Content
        const speakerEndPos = result.indexOf(']') + 1;
        const speakerInfo = result.substring(0, speakerEndPos);
        resultText = result.substring(speakerEndPos);
        
        // Trích xuất tên người nói
        const speakerMatch = speakerInfo.match(/\[(.*?)\]/);
        if (speakerMatch) {
            speakerName = speakerMatch[1];
        }
    }
    
    resultCard.innerHTML = `
        <div class="result-header">
            <div class="result-controls">
                <select class="speaker-dropdown" data-index="${index}">
                    <!-- Các tùy chọn người nói sẽ được điền bằng JS -->
                </select>
                <button class="correct-text-btn" data-index="${index}" title="Sửa văn bản tiếng Việt">
                    <i class="fas fa-spell-check"></i>
                </button>
                <button class="merge-text-btn" data-index="${index}" title="Gộp với người nói trước đó">
                    <i class="fas fa-compress-arrows-alt"></i>
                </button>
                <button class="delete-result-btn" data-index="${index}" title="Xóa đoạn văn này">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <div class="result-text" contenteditable="true">${resultText.trim()}</div>
        <div class="timestamp">${timestamp}</div>
    `;
    
    container.appendChild(resultCard);
    
    // Lấy dropdown vừa thêm vào và gán sự kiện thay đổi
    const dropdown = resultCard.querySelector('.speaker-dropdown');

    // Mark user interaction on dropdown events
    dropdown.addEventListener('focus', function() {
        markUserInteraction();
        activeDropdownIndex = index;
        activeDropdownValue = this.value;
    });

    dropdown.addEventListener('mousedown', function() {
        markUserInteraction();
    });

    dropdown.addEventListener('change', function() {
        markUserInteraction();
        changeSpeaker(this, index);
        // Save dropdown selection after change
        setTimeout(() => saveAllDropdownSelections(), 100);
    });

    // Bắt sự kiện chỉnh sửa văn bản
    const textElement = resultCard.querySelector('.result-text');

    // Mark user interaction when editing text
    textElement.addEventListener('focus', function() {
        markUserInteraction();
    });

    textElement.addEventListener('input', function() {
        markUserInteraction();
    });

    textElement.addEventListener('blur', function() {
        markUserInteraction(); // Mark interaction when finishing edit
        updateResultText(index, this.textContent.trim());
    });

    // Xử lý sự kiện keydown để ngăn chặn Enter tạo dòng mới
    textElement.addEventListener('keydown', function(e) {
        // Mark interaction for any key press (user is actively editing)
        markUserInteraction();

        if (e.key === 'Enter') {
            if (e.shiftKey) {
                // Shift+Enter: Tách văn bản tại con trỏ thành 2 kết quả riêng biệt
                e.preventDefault();
                splitTextAtCursor(this, index);
            } else {
                // Enter thường: Chỉ kết thúc chỉnh sửa
                e.preventDefault(); // Ngăn chặn tạo dòng mới
                this.blur(); // Kết thúc chỉnh sửa và lưu thay đổi
            }
        }
    });

    // Thêm sự kiện click để chọn/bỏ chọn card
    resultCard.addEventListener('click', function(e) {
        // Chỉ xử lý click vào card, không phải vào các phần tử con có xử lý riêng
        if (e.target === resultCard || e.target === textElement) {
            this.classList.toggle('selected');
            // Mark interaction when user clicks on card/text (might be preparing to edit)
            markUserInteraction();
        }
    });

    // Thêm sự kiện double-click để tạo dòng mới ngay dưới dòng hiện tại
    resultCard.addEventListener('dblclick', function(e) {
        // Chỉ xử lý double-click vào card hoặc text element
        if (e.target === resultCard || e.target === textElement) {
            markUserInteraction();
            createNewResultLineBelow(index);
        }
    });

    // Thêm event listeners cho các buttons
    const correctBtn = resultCard.querySelector('.correct-text-btn');
    const mergeBtn = resultCard.querySelector('.merge-text-btn');
    const deleteBtn = resultCard.querySelector('.delete-result-btn');

    correctBtn.addEventListener('click', function() {
        markUserInteraction();
        correctText(index);
    });

    mergeBtn.addEventListener('click', function() {
        markUserInteraction();
        mergeWithPrevious(index);
    });

    deleteBtn.addEventListener('click', function() {
        markUserInteraction();
        console.log('🗑️ Delete button clicked for index:', index);
        deleteResult(index);
    });

    // Làm cho card có thể focus để nhận sự kiện keydown
    resultCard.setAttribute('tabindex', '0');

    // Cập nhật dropdown với danh sách người nói
    updateSpeakerDropdown(dropdown, speakerName);

    container.appendChild(resultCard);
}

// Function to remove latest highlight from all result cards
function removeLatestHighlight() {
    const latestCards = document.querySelectorAll('.result-card.latest');
    // console.log(`🧹 removeLatestHighlight: Found ${latestCards.length} cards with "latest" class`);
    latestCards.forEach((card) => {
        // console.log(`   Removing "latest" from card ${index}`);
        card.classList.remove('latest');
    });
}

// Function to parse timestamp from result card (check multiple sources)
function parseTimestampFromCard(card) {
    // Method 1: Check .result-text for [HH:MM:SS] format
    const textElement = card.querySelector('.result-text');
    if (textElement) {
        const text = textElement.textContent || textElement.innerText;
        const timestampMatch = text.match(/^\[(\d{2}:\d{2}:\d{2})\]/);
        if (timestampMatch) {
            const timeStr = timestampMatch[1];
            const [hours, minutes, seconds] = timeStr.split(':').map(Number);
            return hours * 3600 + minutes * 60 + seconds;
        }
    }

    // Method 2: Check .timestamp div for HH:MM:SS format
    const timestampElement = card.querySelector('.timestamp');
    if (timestampElement) {
        const timestampText = timestampElement.textContent || timestampElement.innerText;
        const timeMatch = timestampText.match(/(\d{1,2}):(\d{2}):(\d{2})/);
        if (timeMatch) {
            const [, hours, minutes, seconds] = timeMatch;
            return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds);
        }
    }

    // Method 3: Check card's data-timestamp attribute
    const dataTimestamp = card.dataset.timestamp;
    if (dataTimestamp) {
        const timestamp = parseInt(dataTimestamp);
        if (!isNaN(timestamp)) {
            // Convert milliseconds to seconds and extract time of day
            const date = new Date(timestamp);
            const hours = date.getHours();
            const minutes = date.getMinutes();
            const seconds = date.getSeconds();
            return hours * 3600 + minutes * 60 + seconds;
        }
    }

    return null;
}

// Legacy function for backward compatibility
function parseTimestampFromText(text) {
    const timestampMatch = text.match(/^\[(\d{2}:\d{2}:\d{2})\]/);
    if (timestampMatch) {
        const timeStr = timestampMatch[1];
        const [hours, minutes, seconds] = timeStr.split(':').map(Number);
        return hours * 3600 + minutes * 60 + seconds;
    }
    return null;
}

// Function to highlight result cards with timestamps within the last 2 seconds
function highlightRecentResults() {
    // Remove all existing highlights first
    removeLatestHighlight();

    const allCards = document.querySelectorAll('.result-card');
    if (allCards.length === 0) return;

    let latestTimestamp = 0;
    let highlightedCount = 0;

    // First pass: find the latest timestamp
    allCards.forEach((card) => {
        const timestamp = parseTimestampFromCard(card);
        // const textElement = card.querySelector('.result-text');
        // const text = textElement ? (textElement.textContent || textElement.innerText) : 'No text';
        // console.log(`🔍 Debug: Card ${index}: "${text.substring(0, 50)}..." → Timestamp: ${timestamp}`);
        if (timestamp && timestamp > latestTimestamp) {
            latestTimestamp = timestamp;
        }
    });

    // console.log(`🔍 Debug: Latest timestamp found: ${latestTimestamp}`);

    if (latestTimestamp === 0) {
        // No timestamps found, fallback to highlighting last few cards only
        console.log(`⚠️ No timestamps found, highlighting last 3 cards as fallback`);
        for (let i = Math.max(0, allCards.length - 3); i < allCards.length; i++) {
            allCards[i].classList.add('latest');
            highlightedCount++;
        }
    } else {
        // Second pass: highlight cards within 2 seconds of latest timestamp
        const cutoffTime = latestTimestamp - 2; // 2 seconds before latest
        // console.log(`🔍 Debug: Cutoff time: ${cutoffTime} (highlighting cards >= this time)`);

        allCards.forEach((card) => {
            const timestamp = parseTimestampFromCard(card);

            if (timestamp !== null && timestamp >= cutoffTime) {
                card.classList.add('latest');
                highlightedCount++;
                // console.log(`✅ Highlighted card ${index}: timestamp ${timestamp} >= cutoff ${cutoffTime}`);
            } else if (timestamp !== null) {
                // console.log(`⚪ Skipped card ${index}: timestamp ${timestamp} < cutoff ${cutoffTime}`);
            } else {
                // console.log(`⚪ Skipped card ${index}: no timestamp found`);
            }
        });

        // If no cards were highlighted despite having timestamps, highlight all with latest timestamp
        if (highlightedCount === 0 && latestTimestamp > 0) {
            // console.log(`🔄 No cards highlighted with cutoff, highlighting all cards with latest timestamp ${latestTimestamp}`);
            allCards.forEach((card) => {
                const timestamp = parseTimestampFromCard(card);

                if (timestamp === latestTimestamp) {
                    card.classList.add('latest');
                    highlightedCount++;
                    // console.log(`✅ Highlighted card ${index} with latest timestamp: ${timestamp}`);
                }
            });
        }
    }

    // console.log(`✅ Highlighted ${highlightedCount} result cards within 2 seconds of latest timestamp (${latestTimestamp})`);

    // Note: Highlights will only be removed when new results appear
}

// Test function to manually add latest class to last result card
function testLatestHighlight() {
    highlightRecentResults();
    console.log('🧪 Test: Triggered recent results highlight');
}

// Test function to simulate recent results with content timestamps
function testRecentHighlight() {
    const allCards = document.querySelectorAll('.result-card');

    if (allCards.length >= 1) {
        // Simulate multiple timestamps within 2 seconds: 06:48:23, 06:48:24, 06:48:25, 06:48:26, etc.
        const baseHour = 6;
        const baseMinute = 48;
        const baseSecond = 23;

        // Create timestamps for all cards (or up to 10 recent ones)
        const numCardsToUpdate = Math.min(allCards.length, 10);

        for (let i = 0; i < numCardsToUpdate; i++) {
            const cardIndex = allCards.length - numCardsToUpdate + i;
            const card = allCards[cardIndex];
            const textElement = card.querySelector('.result-text');

            if (textElement) {
                const currentText = textElement.textContent || textElement.innerText;
                const timestampSecond = baseSecond + i;
                const timestamp = `${baseHour.toString().padStart(2, '0')}:${baseMinute.toString().padStart(2, '0')}:${timestampSecond.toString().padStart(2, '0')}`;

                // Replace or add timestamp
                const newText = currentText.replace(/\[\d{2}:\d{2}:\d{2}\]/, `[${timestamp}]`);
                if (!newText.includes('[')) {
                    textElement.textContent = `[${timestamp}] ${currentText}`;
                } else {
                    textElement.textContent = newText;
                }
            }
        }
    }

    highlightRecentResults();
    console.log(`🧪 Test: Added sequential timestamps to last ${Math.min(allCards.length, 10)} cards (all within 2s of latest)`);
}

// Test function to highlight last N cards (simulate recent additions)
function testHighlightLastN(n = 10) {
    const allCards = document.querySelectorAll('.result-card');
    removeLatestHighlight();

    // Highlight last N cards (or all if less than N)
    const startIndex = Math.max(0, allCards.length - n);
    for (let i = startIndex; i < allCards.length; i++) {
        allCards[i].classList.add('latest');
    }

    console.log(`🧪 Test: Highlighted last ${Math.min(n, allCards.length)} cards`);
    console.log(`Note: Highlights will remain until new results appear`);
}

// Test function to highlight ALL cards (simulate all recent)
function testHighlightAll() {
    const allCards = document.querySelectorAll('.result-card');
    removeLatestHighlight();

    // Highlight ALL cards
    allCards.forEach(card => {
        card.classList.add('latest');
    });

    console.log(`🧪 Test: Highlighted ALL ${allCards.length} cards`);
    console.log(`Note: Highlights will remain until new results appear`);
}

// Test function with real timestamp scenario
function testTimestampHighlight() {
    console.log('🧪 Testing timestamp-based highlighting...');

    const allCards = document.querySelectorAll('.result-card');
    allCards.forEach((card, index) => {
        const textElement = card.querySelector('.result-text');
        if (textElement) {
            const text = textElement.textContent || textElement.innerText;
            const timestamp = parseTimestampFromText(text);
            console.log(`Card ${index}: "${text.substring(0, 50)}..." → Timestamp: ${timestamp}`);
        }
    });

    highlightRecentResults();
}

// Manual function to clear all highlights (for testing)
function clearAllHighlights() {
    removeLatestHighlight();
    // console.log('🧹 Manually cleared all highlights');
}

// Debug function to check everything step by step
function debugHighlightIssue() {
    console.log('🔍 === DEBUGGING HIGHLIGHT ISSUE ===');

    const allCards = document.querySelectorAll('.result-card');
    console.log(`📊 Total cards found: ${allCards.length}`);

    if (allCards.length === 0) {
        console.log('❌ No cards found! Make sure you have result cards on the page.');
        return;
    }

    // Step 1: Check each card's text content
    console.log('\n📝 Step 1: Checking card text content...');
    allCards.forEach((card, index) => {
        const textElement = card.querySelector('.result-text');
        if (textElement) {
            const text = textElement.textContent || textElement.innerText;
            console.log(`Card ${index}: "${text}"`);
        } else {
            console.log(`Card ${index}: NO .result-text element found!`);
        }
    });

    // Step 2: Test timestamp parsing
    console.log('\n⏰ Step 2: Testing timestamp parsing...');
    allCards.forEach((card, index) => {
        const textElement = card.querySelector('.result-text');
        const timestampElement = card.querySelector('.timestamp');
        const dataTimestamp = card.dataset.timestamp;

        const text = textElement ? (textElement.textContent || textElement.innerText) : 'No text';
        const timestampText = timestampElement ? (timestampElement.textContent || timestampElement.innerText) : 'No timestamp div';
        const timestamp = parseTimestampFromCard(card);

        console.log(`Card ${index}:`);
        console.log(`  Text: "${text.substring(0, 30)}..."`);
        console.log(`  Timestamp div: "${timestampText}"`);
        console.log(`  Data timestamp: ${dataTimestamp}`);
        console.log(`  Parsed timestamp: ${timestamp}`);
    });

    // Step 3: Test manual highlight
    console.log('\n🎨 Step 3: Testing manual highlight...');
    removeLatestHighlight();

    // Manually add latest class to all cards
    allCards.forEach((card, index) => {
        card.classList.add('latest');
        console.log(`✅ Added 'latest' class to card ${index}`);
    });

    // Check if CSS is applied
    setTimeout(() => {
        console.log('\n🎨 Step 4: Checking CSS application...');
        allCards.forEach((card, index) => {
            const hasLatestClass = card.classList.contains('latest');
            const computedStyle = window.getComputedStyle(card);
            const backgroundColor = computedStyle.backgroundColor;
            console.log(`Card ${index}: hasLatest=${hasLatestClass}, backgroundColor="${backgroundColor}"`);
        });
    }, 100);

    console.log('\n🔍 === DEBUG COMPLETE ===');
}

// Hàm hiển thị kết quả với tùy chọn gộp lời nói
function displayResults(results, container) {
    if (!results || results.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-microphone-slash"></i>
                <div class="empty-state-text">Chưa có kết quả nhận dạng nào</div>
                <div>Hãy nói gì đó để bắt đầu nhận dạng giọng nói</div>
            </div>
        `;
        return;
    }

    // Nếu không cần gộp lời nói, hiển thị bình thường
    if (!mergeSameSpeaker && !mergeAllSpeakers) {
        results.forEach((result, index) => {
            // Don't highlight when displaying all results (refresh)
            renderResultCard(container, result, index, false);
        });

        // Highlight all results from last 2 seconds after rendering
        if (results.length > 0) {
            setTimeout(() => highlightRecentResults(), 100);
        }
        return;
    }

    // Xử lý trường hợp gộp lời nói
    let mergedResults = [];

    if (mergeAllSpeakers) {
        // Gộp tất cả người nói cùng tên (không phân biệt liên tiếp hay không)
        let speakerGroups = {};

        results.forEach((result, index) => {
            let speakerName = "Người lạ";
            let resultText = result;
            let timestamp = new Date().toLocaleTimeString(); // Mặc định

            // Kiểm tra format mới với timestamp: [HH:MM:SS] [Tên người nói] Content
            const newFormatMatch = result.match(/^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$/);
            if (newFormatMatch) {
                timestamp = newFormatMatch[1];
                speakerName = newFormatMatch[2];
                resultText = newFormatMatch[3].trim();
            } else if (result.includes('[') && result.includes(']')) {
                // Format cũ: [Tên người nói] Content
                const speakerEndPos = result.indexOf(']') + 1;
                const speakerInfo = result.substring(0, speakerEndPos);
                resultText = result.substring(speakerEndPos).trim();

                // Lấy tên người nói
                const speakerMatch = speakerInfo.match(/\[(.*?)\]/);
                if (speakerMatch) {
                    speakerName = speakerMatch[1];
                }
            }

            // Gộp vào nhóm của speaker
            if (!speakerGroups[speakerName]) {
                speakerGroups[speakerName] = {
                    texts: [],
                    indices: [],
                    firstTimestamp: timestamp
                };
            }

            if (resultText.trim() !== "") {
                speakerGroups[speakerName].texts.push(resultText.trim());
            }
            speakerGroups[speakerName].indices.push(index);
        });

        // Tạo kết quả đã gộp theo thứ tự xuất hiện đầu tiên của mỗi speaker
        let speakerOrder = [];
        results.forEach((result) => {
            let speakerName = "Người lạ";
            const newFormatMatch = result.match(/^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]/);
            if (newFormatMatch) {
                speakerName = newFormatMatch[2];
            } else if (result.includes('[') && result.includes(']')) {
                const speakerMatch = result.match(/\[(.*?)\]/);
                if (speakerMatch) {
                    speakerName = speakerMatch[1];
                }
            }

            if (!speakerOrder.includes(speakerName)) {
                speakerOrder.push(speakerName);
            }
        });

        speakerOrder.forEach(speakerName => {
            const group = speakerGroups[speakerName];
            const combinedText = group.texts.join(' ');
            mergedResults.push(`[${group.firstTimestamp}] [${speakerName}] ${combinedText}`);
        });

    } else if (mergeSameSpeaker) {
        // Logic cũ: chỉ gộp liên tiếp
        let currentSpeaker = null;
        let currentText = "";

        results.forEach((result) => {
            let speakerName = "Người lạ";
            let resultText = result;
            let timestamp = new Date().toLocaleTimeString(); // Mặc định

            // Kiểm tra format mới với timestamp: [HH:MM:SS] [Tên người nói] Content
            const newFormatMatch = result.match(/^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$/);
            if (newFormatMatch) {
                timestamp = newFormatMatch[1];
                speakerName = newFormatMatch[2];
                resultText = newFormatMatch[3].trim();
            } else if (result.includes('[') && result.includes(']')) {
                // Format cũ: [Tên người nói] Content
                const speakerEndPos = result.indexOf(']') + 1;
                const speakerInfo = result.substring(0, speakerEndPos);
                resultText = result.substring(speakerEndPos).trim();

                // Lấy tên người nói
                const speakerMatch = speakerInfo.match(/\[(.*?)\]/);
                if (speakerMatch) {
                    speakerName = speakerMatch[1];
                }
            }

            // Nếu cùng người nói, gộp văn bản
            // KHÔNG gộp "Người lạ" vì có thể là nhiều người khác nhau
            if (currentSpeaker === speakerName && speakerName !== "Người lạ") {
                // Chỉ gộp nếu cả hai đều có nội dung
                if (currentText.trim() !== "" && resultText.trim() !== "") {
                    currentText += " " + resultText;

                    // Cập nhật kết quả đã gộp cuối cùng với timestamp từ câu mới nhất
                    if (mergedResults.length > 0) {
                        mergedResults[mergedResults.length - 1] = `[${timestamp}] [${currentSpeaker}] ${currentText}`;
                    }
                } else {
                    // Nếu một trong hai là rỗng, giữ nguyên cả hai
                    if (resultText.trim() !== "") {
                        mergedResults.push(`[${timestamp}] [${speakerName}] ${resultText}`);
                    } else if (currentText.trim() === "" && resultText.trim() === "") {
                        // Nếu cả hai đều rỗng, giữ một kết quả rỗng
                        mergedResults.push(`[${timestamp}] [${speakerName}] `);
                    }
                }
            } else {
                // Người nói mới, tạo kết quả mới
                currentSpeaker = speakerName;
                currentText = resultText;
                mergedResults.push(`[${timestamp}] [${currentSpeaker}] ${currentText}`);
            }
        });
    }

    // Hiển thị kết quả đã gộp
    // Create a mapping to track original indices for each merged result
    let originalIndicesMap = [];

    if (mergeAllSpeakers) {
        // Tạo mapping cho mergeAllSpeakers
        let speakerToIndices = {};
        let speakerOrder = [];

        for (let i = 0; i < results.length; i++) {
            let speakerName = "Người lạ";

            // Extract speaker name from result
            const newFormatMatch = results[i].match(/^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]/);
            if (newFormatMatch) {
                speakerName = newFormatMatch[2];
            } else if (results[i].includes('[') && results[i].includes(']')) {
                const nameMatch = results[i].match(/\[(.*?)\]/);
                if (nameMatch) {
                    speakerName = nameMatch[1];
                }
            }

            if (!speakerToIndices[speakerName]) {
                speakerToIndices[speakerName] = [];
                speakerOrder.push(speakerName);
            }
            speakerToIndices[speakerName].push(i);
        }

        // Tạo originalIndicesMap theo thứ tự xuất hiện
        speakerOrder.forEach((speakerName, displayIndex) => {
            originalIndicesMap.push({
                displayIndex: displayIndex,
                originalIndices: speakerToIndices[speakerName],
                speaker: speakerName
            });
        });

    } else if (mergeSameSpeaker) {
        // Logic cũ cho mergeSameSpeaker
        let currentIndex = 0;

        for (let i = 0; i < results.length; i++) {
            let speakerName = "Người lạ";

            // Extract speaker name from result (hỗ trợ cả format mới và cũ)
            const newFormatMatch = results[i].match(/^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]/);
            if (newFormatMatch) {
                speakerName = newFormatMatch[2];
            } else if (results[i].includes('[') && results[i].includes(']')) {
                const nameMatch = results[i].match(/\[(.*?)\]/);
                if (nameMatch) {
                    speakerName = nameMatch[1];
                }
            }

            // Tạo mapping giữa display index và original indices
            // Lưu ý: "Người lạ" sẽ KHÔNG được gộp vì có thể là nhiều người khác nhau
            if (i === 0 || originalIndicesMap[currentIndex-1].speaker !== speakerName || speakerName === "Người lạ") {
                // New speaker, first result, or "Người lạ" (never merge "Người lạ")
                originalIndicesMap.push({
                    displayIndex: currentIndex,
                    originalIndices: [i],
                    speaker: speakerName
                });
                currentIndex++;
            } else {
                // Same speaker as previous (and not "Người lạ"), add to existing entry
                originalIndicesMap[currentIndex-1].originalIndices.push(i);
            }
        }
    }
    
    mergedResults.forEach((result, displayIndex) => {
        const resultCard = document.createElement('div');
        resultCard.className = 'result-card';
        resultCard.dataset.index = displayIndex;
        resultCard.dataset.originalIndices = JSON.stringify(originalIndicesMap[displayIndex].originalIndices);
        resultCard.dataset.timestamp = Date.now(); // Add timestamp for recent highlighting

        // Don't highlight during initial render - will be added later
        
        // Extract speaker info, text và timestamp
        let speakerName = "Người lạ";
        let resultText = result;
        let timestamp = new Date().toLocaleTimeString(); // Mặc định
        
        // Kiểm tra format mới với timestamp: [HH:MM:SS] [Tên người nói] Nội dung
        const newFormatMatch = result.match(/^\[(\d{2}:\d{2}:\d{2})\]\s*\[(.*?)\]\s*(.*)$/);
        if (newFormatMatch) {
            timestamp = newFormatMatch[1];
            speakerName = newFormatMatch[2];
            resultText = newFormatMatch[3];
        } else if (result.includes('[') && result.includes(']')) {
            // Format cũ: [Tên người nói] Nội dung
            const speakerEndPos = result.indexOf(']') + 1;
            const speakerInfo = result.substring(0, speakerEndPos);
            resultText = result.substring(speakerEndPos);
            
            // Extract speaker name
            const speakerMatch = speakerInfo.match(/\[(.*?)\]/);
            if (speakerMatch) {
                speakerName = speakerMatch[1];
            }
        }
        
        resultCard.innerHTML = `
            <div class="result-header">
                <div class="result-controls">
                    <select class="speaker-dropdown" data-index="${displayIndex}">
                        <!-- Speaker options will be filled by JS -->
                    </select>
                    <button class="correct-text-btn" onclick="correctText(${displayIndex})" title="Sửa văn bản tiếng Việt">
                        <i class="fas fa-spell-check"></i>
                    </button>
                    <button class="merge-text-btn" onclick="mergeWithPrevious(${displayIndex})" title="Gộp với người nói trước đó">
                        <i class="fas fa-compress-arrows-alt"></i>
                    </button>
                    <button class="delete-result-btn" onclick="console.log('🗑️ Delete button clicked for displayIndex:', ${displayIndex}); deleteResult(${displayIndex})" title="Xóa đoạn văn này">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="result-text" contenteditable="true">${resultText.trim()}</div>
            <div class="timestamp">${timestamp}</div>
        `;
        
        container.appendChild(resultCard);
        
        // Get the dropdown that was just added and set up the change event
        const dropdown = resultCard.querySelector('.speaker-dropdown');
        dropdown.addEventListener('change', function() {
            changeSpeaker(this, displayIndex);
        });
        
        // Track dropdown focus/blur events
        dropdown.addEventListener('focus', function() {
            activeDropdownIndex = displayIndex;
            activeDropdownValue = this.value;
        });
        
        // Set up text editing events
        const textElement = resultCard.querySelector('.result-text');
        textElement.addEventListener('blur', function() {
            updateResultText(displayIndex, this.textContent.trim());
        });
        
        // Xử lý sự kiện keydown để ngăn chặn Enter tạo dòng mới
        textElement.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                if (e.shiftKey) {
                    // Shift+Enter: Tách văn bản tại con trỏ thành 2 kết quả riêng biệt
                    e.preventDefault();
                    splitTextAtCursor(this, displayIndex);
                } else {
                    // Enter thường: Chỉ kết thúc chỉnh sửa
                    e.preventDefault(); // Ngăn chặn tạo dòng mới
                    this.blur(); // Kết thúc chỉnh sửa và lưu thay đổi
                }
            }
        });
        
        // Add click event to select/deselect the card
        resultCard.addEventListener('click', function(e) {
            // Only handle clicks on the card itself, not on child elements with their own handlers
            if (e.target === resultCard || e.target === textElement) {
                this.classList.toggle('selected');
            }
        });
        
        // Thêm sự kiện double-click để tạo dòng mới ngay dưới dòng hiện tại
        resultCard.addEventListener('dblclick', function(e) {
            // Chỉ xử lý double-click vào card hoặc text element
            if (e.target === resultCard || e.target === textElement) {
                createNewResultLineBelow(displayIndex);
            }
        });
        
        // Làm cho card có thể focus để nhận sự kiện keydown
        resultCard.setAttribute('tabindex', '0');
        
        // Update the dropdown with the speaker list
        updateSpeakerDropdown(dropdown, speakerName);
    });

    // Highlight all merged results from last 2 seconds after rendering
    if (mergedResults.length > 0) {
        setTimeout(() => highlightRecentResults(), 100);
    }
}

// Hàm khởi tạo cài đặt gộp lời nói
function initMergeSpeakerSetting() {
    const mergeSpeakerCheckbox = document.getElementById('merge_same_speaker');
    
    if (mergeSpeakerCheckbox) {
        // Khôi phục giá trị từ localStorage nếu có
        const savedValue = localStorage.getItem('mergeSameSpeaker');
        if (savedValue !== null) {
            mergeSameSpeaker = savedValue === 'true';
            mergeSpeakerCheckbox.checked = mergeSameSpeaker;
        } else {
            // Nếu không có giá trị đã lưu, sử dụng trạng thái hiện tại của checkbox
            mergeSameSpeaker = mergeSpeakerCheckbox.checked;
        }
        
        // Event listener sẽ được thêm trong phần auto-save, không cần thêm ở đây nữa
    }
}

// Cập nhật nội dung văn bản của kết quả
function updateResultText(displayIndex, newText) {
    // Get the result card to check for original indices mapping
    const resultCard = document.querySelector(`.result-card[data-index="${displayIndex}"]`);
    
    // Check if we're in merged mode and have original indices stored
    if ((mergeSameSpeaker || mergeAllSpeakers) && resultCard && resultCard.dataset.originalIndices) {
        try {
            // Get original indices from the data attribute
            const originalIndices = JSON.parse(resultCard.dataset.originalIndices);
            
            // If there are multiple merged results, we need special handling
            if (originalIndices.length > 1) {
                // Get the timestamp and speaker info from the first result
                const firstResult = speechResults[originalIndices[0]];
                let timestampAndSpeakerInfo = '';

                // Handle format: [timestamp] [speaker] content
                const newFormatMatch = firstResult.match(/^(\[(\d{2}:\d{2}:\d{2})\]\s*\[.*?\])/);
                if (newFormatMatch) {
                    timestampAndSpeakerInfo = newFormatMatch[1];
                } else if (firstResult.includes('[') && firstResult.includes(']')) {
                    // Fallback to old format: [speaker] content
                    const speakerEndPos = firstResult.indexOf(']') + 1;
                    timestampAndSpeakerInfo = firstResult.substring(0, speakerEndPos);
                }

                // For merged results, update only the first entry with all the text
                // and empty out the other entries
                originalIndices.forEach((originalIndex, i) => {
                    if (i === 0) {
                        // Update first entry with full text, preserving timestamp and speaker
                        speechResults[originalIndex] = timestampAndSpeakerInfo ? `${timestampAndSpeakerInfo} ${newText}` : newText;
                    } else {
                        // For other entries, keep just the timestamp and speaker info
                        const currResult = speechResults[originalIndex];
                        const currNewFormatMatch = currResult.match(/^(\[(\d{2}:\d{2}:\d{2})\]\s*\[.*?\])/);
                        if (currNewFormatMatch) {
                            speechResults[originalIndex] = `${currNewFormatMatch[1]} `;  // Empty text
                        } else if (currResult.includes('[') && currResult.includes(']')) {
                            const speakerEndPos = currResult.indexOf(']') + 1;
                            const currSpeakerInfo = currResult.substring(0, speakerEndPos);
                            speechResults[originalIndex] = `${currSpeakerInfo} `;  // Empty text
                        } else {
                            speechResults[originalIndex] = '';  // Empty if no speaker info
                        }
                    }
                });
                
                console.log(`Updated merged result containing ${originalIndices.length} entries`);
            } else if (originalIndices.length === 1) {
                // Single result case (same as original)
                const originalIndex = originalIndices[0];
                if (originalIndex < speechResults.length) {
                    const result = speechResults[originalIndex];

                    // Handle format: [timestamp] [speaker] content
                    const newFormatMatch = result.match(/^(\[(\d{2}:\d{2}:\d{2})\]\s*\[.*?\])/);
                    if (newFormatMatch) {
                        const timestampAndSpeakerInfo = newFormatMatch[1];
                        speechResults[originalIndex] = `${timestampAndSpeakerInfo} ${newText}`;
                    } else if (result.includes('[') && result.includes(']')) {
                        // Fallback to old format: [speaker] content
                        const speakerEndPos = result.indexOf(']') + 1;
                        const speakerInfo = result.substring(0, speakerEndPos);
                        speechResults[originalIndex] = `${speakerInfo} ${newText}`;
                    } else {
                        // No speaker info, update entire text
                        speechResults[originalIndex] = newText;
                    }
                }
            }
            
            // Save changes to server
            saveResults()
                .then(() => {
                    showAlert('success', 'Đã lưu nội dung chỉnh sửa');
                    // Không cần fetchResults() - chỉ update local UI đã đủ
                    // fetchResults(); // REMOVED to avoid triggering speaker fetch
                })
                .catch(error => {
                    console.error('Error saving edited text:', error);
                    showAlert('error', 'Lỗi khi lưu nội dung chỉnh sửa');
                });
        } catch (error) {
            console.error('Error updating merged result text:', error);
            showAlert('error', 'Lỗi khi cập nhật nội dung');
        }
    } else {
        // Original behavior for non-merged mode
        if (displayIndex < speechResults.length) {
            // Keep the timestamp and speaker info, just update the text
            const result = speechResults[displayIndex];

            // Handle format: [timestamp] [speaker] content
            const newFormatMatch = result.match(/^(\[(\d{2}:\d{2}:\d{2})\]\s*\[.*?\])/);
            if (newFormatMatch) {
                const timestampAndSpeakerInfo = newFormatMatch[1];
                speechResults[displayIndex] = `${timestampAndSpeakerInfo} ${newText}`;
            } else if (result.includes('[') && result.includes(']')) {
                // Fallback to old format: [speaker] content
                const speakerEndPos = result.indexOf(']') + 1;
                const speakerInfo = result.substring(0, speakerEndPos);
                speechResults[displayIndex] = `${speakerInfo} ${newText}`;
            } else {
                // No speaker info, update entire text
                speechResults[displayIndex] = newText;
            }
            
            // Save changes to server
            saveResults()
                .then(() => {
                    showAlert('success', 'Đã lưu nội dung chỉnh sửa');
                    // Không cần fetchResults() - chỉ update local UI đã đủ
                    // fetchResults(); // REMOVED to avoid triggering speaker fetch
                })
                .catch(error => {
                    console.error('Error saving edited text:', error);
                    showAlert('error', 'Lỗi khi lưu nội dung chỉnh sửa');
                });
        }
    }
}

// Hàm để cập nhật dropdown với danh sách người nói
function updateSpeakerDropdown(dropdown, selectedSpeaker) {
    // Xóa tất cả tùy chọn hiện tại
    dropdown.innerHTML = '';

    // Tùy chọn mặc định - "Người lạ"
    const defaultOption = document.createElement('option');
    defaultOption.value = 'Người lạ';
    defaultOption.textContent = 'Người lạ';
    if (selectedSpeaker === 'Người lạ') {
        defaultOption.selected = true;
    }
    dropdown.appendChild(defaultOption);

    // Sử dụng cache thay vì fetch riêng lẻ
    // CHỈ force refresh nếu flag được đặt (page reload hoặc speaker name update)
    const forceRefresh = shouldReloadDropdowns;

    // Chỉ fetch speakers nếu cache chưa có hoặc cần force refresh
    if (!speakersCache || forceRefresh) {
        fetchSpeakersWithCache(forceRefresh)
            .then(data => {
                if (data.speakers && data.speakers.length > 0) {
                    // Thêm tùy chọn cho mỗi người nói
                    data.speakers.forEach(speaker => {
                        const option = document.createElement('option');
                        option.value = speaker.name;
                        option.textContent = speaker.name;
                        if (speaker.name === selectedSpeaker) {
                            option.selected = true;
                        }
                        dropdown.appendChild(option);
                    });
                }
            })
            .catch(error => console.error('Lỗi khi lấy danh sách người nói:', error));
    } else {
        // Sử dụng cache có sẵn
        console.log('📋 Using cached speakers for dropdown');
        if (speakersCache.speakers && speakersCache.speakers.length > 0) {
            speakersCache.speakers.forEach(speaker => {
                const option = document.createElement('option');
                option.value = speaker.name;
                option.textContent = speaker.name;
                if (speaker.name === selectedSpeaker) {
                    option.selected = true;
                }
                dropdown.appendChild(option);
            });
        }
    }
}

// Hàm thay đổi người nói cho một kết quả
function changeSpeaker(dropdown, displayIndex) {
    const newSpeaker = dropdown.value;
    const resultCard = dropdown.closest('.result-card');
    const resultText = resultCard.querySelector('.result-text').textContent.trim();
    
    // Check if we're in merged mode and have original indices stored
    if ((mergeSameSpeaker || mergeAllSpeakers) && resultCard && resultCard.dataset.originalIndices) {
        try {
            // Get original indices from the data attribute
            const originalIndices = JSON.parse(resultCard.dataset.originalIndices);
            
            // Update speaker for all merged entries
            originalIndices.forEach(originalIndex => {
                if (originalIndex < speechResults.length) {
                    const result = speechResults[originalIndex];
                    
                    if (result.includes('[') && result.includes(']')) {
                        // Get the text part (keep it intact)
                        const speakerEndPos = result.indexOf(']') + 1;
                        const currentText = result.substring(speakerEndPos).trim();
                        
                        // Only update the first entry with all text
                        if (originalIndex === originalIndices[0]) {
                            speechResults[originalIndex] = `[${newSpeaker}] ${resultText}`;
                        } else {
                            // Keep other entries with just speaker info
                            speechResults[originalIndex] = `[${newSpeaker}] `;
                        }
                    } else {
                        // No speaker info, add it
                        speechResults[originalIndex] = `[${newSpeaker}] ${result}`;
                    }
                }
            });
            
            console.log(`Updated speaker for merged result containing ${originalIndices.length} entries`);
        } catch (error) {
            console.error('Error changing speaker for merged result:', error);
            showAlert('error', 'Lỗi khi thay đổi người nói. Vui lòng thử lại.');
            return;
        }
    } else {
        // Original behavior for non-merged mode
        if (displayIndex < speechResults.length) {
            const currentResult = speechResults[displayIndex];
            
            if (currentResult.includes('[') && currentResult.includes(']')) {
                const speakerEndPos = currentResult.indexOf(']') + 1;
                
                // Update result with new speaker
                speechResults[displayIndex] = `[${newSpeaker}] ${resultText}`;
            } else {
                // No speaker info, add it
                speechResults[displayIndex] = `[${newSpeaker}] ${currentResult}`;
            }
        }
    }
    
    // Save changes to server
    saveResults()
        .then(() => {
            // Show notification
            showAlert('success', `Đã thay đổi người nói thành "${newSpeaker}"`);
            
            // Chỉ cập nhật speakers nếu thực sự đổi sang "Người lạ"
            // (có thể là người nói mới cần profile)
            if (newSpeaker === 'Người lạ') {
                console.log('🔄 Speaker changed to "Người lạ", refreshing cache...');
                setTimeout(() => fetchSpeakers(true), 500); // Force refresh cache
            }
        })
        .catch(error => {
            console.error('Error saving after speaker change:', error);
            showAlert('error', 'Lỗi khi lưu thay đổi người nói');
        });
}

// Hàm xóa một kết quả - optimized để tránh treo
function deleteResult(displayIndex) {
    console.log('🗑️ deleteResult called with displayIndex:', displayIndex);

    // Kiểm tra xem deletion cho index này đã đang diễn ra chưa
    if (deletionInProgress.has(displayIndex)) {
        console.log('⚠️ Deletion already in progress for index:', displayIndex);
        return;
    }

    // Đánh dấu deletion đang diễn ra
    deletionInProgress.add(displayIndex);
    // console.log('✅ Added to deletionInProgress:', displayIndex);

    // Cập nhật UI ngay lập tức trước khi lưu server
    const resultCard = document.querySelector(`.result-card[data-index="${displayIndex}"]`);
    if (resultCard) {
        resultCard.style.opacity = '0.5';
        resultCard.style.pointerEvents = 'none';
        
        // Thêm loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'loading-indicator';
        loadingIndicator.innerHTML = '<span>🗑️ Đang xóa...</span>';
        loadingIndicator.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        `;
        resultCard.style.position = 'relative';
        resultCard.appendChild(loadingIndicator);
    }

    // Timeout cleanup để tránh stuck forever (giảm từ 10s xuống 5s)
    const timeoutId = setTimeout(() => {
        console.log('⏰ Timeout cleanup for index:', displayIndex);
        deletionInProgress.delete(displayIndex);
        
        // Restore UI nếu có lỗi
        if (resultCard) {
            resultCard.style.opacity = '1';
            resultCard.style.pointerEvents = 'auto';
            const loadingIndicator = resultCard.querySelector('.loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
        }
        showAlert('error', 'Xóa câu thất bại do timeout. Vui lòng thử lại.');
    }, 5000); // Giảm timeout xuống 5 giây

    // Function để cleanup
    const cleanup = () => {
        // console.log('🧹 Cleanup for index:', displayIndex);
        clearTimeout(timeoutId);
        deletionInProgress.delete(displayIndex);
    };

    // Xử lý xóa data
    let deletionSuccessful = false;

    // console.log('🔍 Found result card:', resultCard ? 'Yes' : 'No');

    // Check if we're in merged mode and have original indices stored
    if ((mergeSameSpeaker || mergeAllSpeakers) && resultCard && resultCard.dataset.originalIndices) {
        console.log('📊 Processing merged mode deletion');
        try {
            // Get original indices from the data attribute
            const originalIndices = JSON.parse(resultCard.dataset.originalIndices);
            
            // Delete all entries in reverse order to avoid shifting issues
            originalIndices.sort((a, b) => b - a).forEach(originalIndex => {
                if (originalIndex < speechResults.length) {
                    speechResults.splice(originalIndex, 1);
                }
            });
            
            console.log(`Deleted merged result containing ${originalIndices.length} entries`);
            deletionSuccessful = true;
        } catch (error) {
            console.error('Error deleting merged result:', error);
            showAlert('error', 'Lỗi khi xóa câu gộp. Vui lòng thử lại.');
            cleanup();
            return;
        }
    } else {
        // Normal deletion for non-merged mode
        console.log('📊 Processing normal mode deletion');
        console.log('📊 speechResults.length:', speechResults.length, 'displayIndex:', displayIndex);
        if (displayIndex < speechResults.length) {
            speechResults.splice(displayIndex, 1);
            // console.log('✅ Deleted from speechResults, new length:', speechResults.length);
            deletionSuccessful = true;
        } else {
            console.log('⚠️ displayIndex out of bounds');
            showAlert('error', 'Không thể xóa: Index không hợp lệ');
            cleanup();
            return;
        }
    }

    // Cập nhật UI ngay lập tức nếu xóa thành công
    if (deletionSuccessful) {
        // Update display immediately
        const resultsInner = document.querySelector('.results-inner');
        resultsInner.innerHTML = '';
        displayResults(speechResults, resultsInner);

        // Update previous results count
        prevResultsCount = speechResults.length;

        // Show success message immediately
        showAlert('success', 'Đã xóa câu thành công');
    }

    // Lưu thay đổi lên server (async, không block UI)
    console.log('💾 Starting saveResults...');
    saveResults()
        .then(() => {
            // console.log('✅ saveResults successful');
            cleanup();
        })
        .catch(error => {
            console.error('❌ saveResults failed:', error);
            
            // Vẫn giữ UI đã updated vì đã xóa thành công ở local
            showAlert('warning', 'Đã xóa câu khỏi giao diện. Lưu file có thể thất bại do mất kết nối server.');
            cleanup();
        });
}

// Hàm lưu thay đổi lên server - optimized với timeout và retry
function saveResults() {
    return new Promise((resolve, reject) => {
        const payload = {
            results: speechResults
        };
        
        // Tạo AbortController để có thể cancel request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // Giảm xuống 3 giây timeout
        
        fetch('/save_results', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                resolve(data);
            } else {
                console.error('Lỗi khi lưu kết quả:', data.message);
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                console.error('Save request timeout after 3 seconds');
                reject(new Error('Timeout khi lưu kết quả'));
            } else {
                console.error('Lỗi khi gửi yêu cầu lưu kết quả:', error);
                reject(error);
            }
        });
    });
}

// THÊM MỚI: Hàm thêm người nói vào kết quả nhận dạng
function addSpeakerToResults(speakerName) {
    // Tạo kết quả mới với tên người nói (không kèm độ tin cậy)
    const newResult = `[${speakerName}] `;
    
    // Thêm vào mảng kết quả
    speechResults.push(newResult);
    
    // Cập nhật hiển thị
    const resultsContainer = document.getElementById('resultsContainer');
    const resultsInner = document.querySelector('.results-inner');
    
    // Xóa trạng thái trống nếu có
    const emptyState = resultsInner.querySelector('.empty-state');
    if (emptyState) {
        resultsInner.innerHTML = '';
    }
    
    if (mergeSameSpeaker) {
        // Cập nhật hiển thị với chế độ gộp lời nói
        resultsInner.innerHTML = '';
        displayResults(speechResults, resultsInner);
    } else {
        if (mergeSameSpeaker) {
            // Refresh toàn bộ hiển thị
            resultsInner.innerHTML = '';
            displayResults(speechResults, resultsInner);

            // Highlight all results from last 2 seconds
            setTimeout(() => highlightRecentResults(), 100);
        } else {
            // Thêm kết quả mới
            renderResultCard(resultsInner, newResult, speechResults.length - 1, false);

            // Highlight all results from last 2 seconds
            setTimeout(() => highlightRecentResults(), 100);
        }
    }
    
    // Cuộn xuống cuối chỉ khi chuột không hover trên vùng kết quả
    if (!isMouseOverResults) {
        resultsContainer.scrollTop = resultsContainer.scrollHeight;
    }
    
    // Cập nhật biến theo dõi số lượng kết quả trước đó
    prevResultsCount = speechResults.length;
    
    // Lưu thay đổi lên server
    saveResults();
    
    // Hiển thị thông báo
    showAlert('info', `Đã thêm người nói "${speakerName}" vào kết quả nhận dạng`);
    
    // Chuyển đến tab kết quả nhận dạng
    const resultsTabButton = document.querySelector('.tab-button[data-tab="resultsTab"]');
    if (resultsTabButton) {
        resultsTabButton.click();
    }
}

// Tracking để tránh multiple concurrent requests
let fetchSpeakersInProgress = false;

// Hàm lấy speakers với cache, timeout và better error handling
function fetchSpeakersWithCache(forceRefresh = false) {
    const now = Date.now();

    // Kiểm tra cache nếu không force refresh
    if (!forceRefresh && speakersCache && (now - speakersCacheTime) < SPEAKERS_CACHE_DURATION) {
        console.log('📋 Using cached speakers data');
        return Promise.resolve(speakersCache);
    }

    // Tránh multiple concurrent requests
    if (fetchSpeakersInProgress) {
        console.log('⚠️ Speakers fetch already in progress, waiting...');
        return new Promise((resolve, reject) => {
            // Đợi request hiện tại hoàn thành
            const checkInterval = setInterval(() => {
                if (!fetchSpeakersInProgress) {
                    clearInterval(checkInterval);
                    // Retry với cache
                    if (speakersCache) {
                        resolve(speakersCache);
                    } else {
                        reject(new Error('Previous fetch failed'));
                    }
                }
            }, 100);

            // Timeout sau 5 giây
            setTimeout(() => {
                clearInterval(checkInterval);
                reject(new Error('Timeout waiting for speakers fetch'));
            }, 5000);
        });
    }

    // Log khi force refresh
    if (forceRefresh) {
        console.log('🔄 Force refreshing speakers - bypassing cache');
        speakersCache = null;
        speakersCacheTime = 0;
    }

    // Đánh dấu đang fetch
    fetchSpeakersInProgress = true;

    // Clear debounce timeout nếu có
    if (fetchSpeakersTimeout) {
        clearTimeout(fetchSpeakersTimeout);
        fetchSpeakersTimeout = null;
    }

    console.log(`📡 Fetching speakers from server... ${forceRefresh ? '(FORCE REFRESH)' : ''}`);

    // Tạo AbortController để có thể cancel request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
        controller.abort();
        console.error('❌ Speakers fetch timeout after 10 seconds');
    }, 10000); // 10 giây timeout

    return fetch('/speakers', {
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        // Cập nhật cache
        speakersCache = data;
        speakersCacheTime = now;
        console.log(`✅ ${forceRefresh ? 'Force refreshed' : 'Fetched'} ${data.speakers?.length || 0} speakers`);
        return data;
    })
    .catch(error => {
        clearTimeout(timeoutId);
        console.error('❌ Error fetching speakers:', error);

        // Fallback to cache nếu có
        if (speakersCache && !forceRefresh) {
            console.log('📋 Falling back to cached speakers data');
            return speakersCache;
        }

        // Fallback to empty data
        const fallbackData = { speakers: [] };
        console.log('📋 Using fallback empty speakers data');
        return fallbackData;
    })
    .finally(() => {
        // Reset flag
        fetchSpeakersInProgress = false;
    });
}

// SỬA ĐỔI: Lấy danh sách người nói và thêm hỗ trợ nhấp đúp
function fetchSpeakers(forceRefresh = false) {
    return fetchSpeakersWithCache(forceRefresh).then(data => {
            const speakersContainer = document.getElementById('speakersTableBody');
            
            if (!speakersContainer) {
                return;
            }
            
            // Always show table and hide empty state since we always have "Người mới"
            document.getElementById('speakersTable').style.display = 'table';
            document.getElementById('speakersEmptyState').style.display = 'none';

            speakersContainer.innerHTML = '';

            // Remove existing event listeners by clearing and re-adding event delegation
            speakersContainer.removeEventListener('click', handleSpeakerClick);
            speakersContainer.removeEventListener('dblclick', handleSpeakerDblClick);
            speakersContainer.addEventListener('click', handleSpeakerClick);
            speakersContainer.addEventListener('dblclick', handleSpeakerDblClick);

            // Always add "Người mới" as first row (special speaker) - regardless of other speakers
            const newSpeakerRow = document.createElement('tr');
            const isNewSpeakerSelected = selectedSpeakers.has('Người mới');
            newSpeakerRow.innerHTML = `
                <td class="speaker-name-cell special-speaker" colspan="2" data-speaker-name="Người mới">
                    <i class="fas fa-user-plus"></i> Người mới
                    <span class="special-speaker-label">Có người nới mới ấn vào đây</span>
                </td>
            `;

            // Add selected class if "Người mới" is selected
            if (isNewSpeakerSelected) {
                newSpeakerRow.classList.add('selected');
            }

            // Event delegation will handle clicks, no need for individual listeners

            speakersContainer.appendChild(newSpeakerRow);

            // Add other speakers if any exist
            if (data.speakers && data.speakers.length > 0) {
                data.speakers.forEach(speaker => {
                const row = document.createElement('tr');
                const isSelected = selectedSpeakers.has(speaker.name);

                row.innerHTML = `
                    <td class="speaker-name-cell" data-speaker-name="${speaker.name}">${speaker.name}</td>
                    <td class="speaker-actions">
                        <button class="action-button edit-button" onclick="openEditModal('${speaker.id}', '${speaker.name}')">
                            <i class="fas fa-edit"></i> Sửa
                        </button>
                        <button class="action-button delete-button" onclick="deleteSpeaker('${speaker.id}')">
                            <i class="fas fa-trash"></i> Xóa
                        </button>
                    </td>
                `;

                // Thêm class selected nếu speaker được chọn
                if (isSelected) {
                    row.classList.add('selected');
                }

                // Event delegation will handle clicks, no need for individual listeners

                    speakersContainer.appendChild(row);
                });
            }

            // Cập nhật UI hiển thị trạng thái chọn
            updateSpeakerSelectionUI();

            // Validate speaker selection after loading speakers
            if (selectedSpeakers.size > 0) {
                validateSpeakerSelection();
            }

            return data; // Trả về data để có thể chain promises
        })
        .catch(error => {
            console.error('Lỗi khi tải danh sách người nói:', error);
            // Hiển thị thông báo lỗi cho người dùng
            showAlert('error', 'Không thể tải danh sách người nói. Vui lòng thử lại sau.');
            throw error; // Re-throw để promise chain có thể xử lý lỗi
        });
}

function openEditModal(speakerId, speakerName) {
    document.getElementById('speakerId').value = speakerId;
    document.getElementById('speakerName').value = speakerName;
    
    // Thiết lập xử lý sự kiện Enter key cho input
    const nameInput = document.getElementById('speakerName');
    nameInput.addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault(); // Ngăn chặn hành động mặc định
            updateSpeakerName(); // Gọi hàm cập nhật khi nhấn Enter
        }
    });
    
    openModal('editModal');
}

function updateSpeakerName() {
    const speakerId = document.getElementById('speakerId').value;
    const newName = document.getElementById('speakerName').value;
    
    if (!newName.trim()) {
        alert('Tên không được để trống!');
        return;
    }
    
    fetch('/update_speaker', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            id: speakerId,
            name: newName
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Cập nhật tên thành công!');
            closeAllModals();
            
            // Không cần manual refresh, SSE sẽ xử lý việc cập nhật tự động
            // Server sẽ gửi sự kiện 'refresh_results' với action 'refresh_all'
        } else {
            showAlert('error', 'Lỗi: ' + data.message);
        }
    })
    .catch((error) => {
        console.error('Lỗi:', error);
        showAlert('error', 'Có lỗi xảy ra khi cập nhật tên.');
    });
}

function deleteSpeaker(speakerId) {
    if (confirm('Bạn có chắc chắn muốn xóa người nói này?')) {
        fetch('/delete_speaker', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: speakerId
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Xóa người nói thành công!');
                fetchSpeakers(true); // Force refresh after delete
            } else {
                showAlert('error', 'Lỗi: ' + data.message);
            }
        })
        .catch((error) => {
            console.error('Lỗi:', error);
            showAlert('error', 'Có lỗi xảy ra khi xóa người nói.');
        });
    }
}

function deleteAllSpeakers() {
    if (confirm('Bạn có chắc chắn muốn xóa tất cả hồ sơ người nói? Hành động này không thể hoàn tác.')) {
        fetch('/delete_all_speakers', {
            method: 'POST',
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Đã xóa tất cả hồ sơ người nói!');
                fetchSpeakers(true); // Force refresh after delete all
            } else {
                showAlert('error', 'Lỗi: ' + data.message);
            }
        })
        .catch((error) => {
            console.error('Lỗi:', error);
            showAlert('error', 'Có lỗi xảy ra khi xóa tất cả hồ sơ người nói.');
        });
    }
}

// Hàm xóa tất cả kết quả nhận dạng
function deleteAllResults() {
    if (speechResults.length === 0) {
        showAlert('info', 'Không có kết quả nào để xóa.');
        return;
    }
    
    if (confirm('Bạn có chắc chắn muốn xóa tất cả kết quả nhận dạng? Hành động này không thể hoàn tác.')) {
        fetch('/delete_all_results', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                speechResults = [];
                prevResultsCount = 0;
                
                // Cập nhật hiển thị
                const resultsInner = document.querySelector('.results-inner');
                resultsInner.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-microphone-slash"></i>
                        <div class="empty-state-text">Chưa có kết quả nhận dạng nào</div>
                        <div>Hãy nói gì đó để bắt đầu nhận dạng giọng nói</div>
                    </div>
                `;
                
                showAlert('success', 'Đã xóa tất cả kết quả nhận dạng!');
            } else {
                showAlert('error', 'Lỗi: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Lỗi khi xóa kết quả:', error);
            showAlert('error', 'Có lỗi xảy ra khi xóa kết quả.');
        });
    }
}

// Side Panel
function setupSidePanel() {
    const settingsToggle = document.getElementById('settingsToggle');
    const settingsPanel = document.querySelector('.settings-panel');
    const overlay = document.getElementById('overlay');
    
    if (!settingsToggle || !settingsPanel || !overlay) {
        return; // Tránh lỗi nếu các phần tử không tồn tại
    }
    
    // Hàm mở side panel
    function openSettingsPanel() {
        settingsPanel.classList.add('open');
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
    
    // Hàm đóng side panel
    function closeSettingsPanel() {
        settingsPanel.classList.remove('open');
        overlay.classList.remove('show');
        document.body.style.overflow = '';
    }
    
    // Thêm sự kiện click
    settingsToggle.addEventListener('click', openSettingsPanel);
    overlay.addEventListener('click', closeSettingsPanel);
}

// Hàm để load cài đặt từ server (settings.json)
function loadSettingsFromServer() {
    fetch('/get_settings')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Loaded settings from server:', data.settings);
                updateSettingsUI(data.settings);
            } else {
                console.error('Failed to load settings:', data.message);
                // Fallback to template rendering
                console.log('Using template-rendered settings as fallback');
            }
        })
        .catch(error => {
            console.error('Error loading settings from server:', error);
            // Fallback to template rendering
            console.log('Using template-rendered settings as fallback');
        });
}

// Hàm để cập nhật UI với cài đặt mới
function updateSettingsUI(settings) {
    try {
        // Cập nhật các input number
        const numberInputs = {
            'similarity_threshold': settings.similarity_threshold,
            'auto_similarity_threshold': settings.auto_similarity_threshold, 
            'embedding_merge_threshold': settings.embedding_merge_threshold,
            'vad_threshold': settings.vad_threshold,
            'vad_min_speech_duration': settings.vad_min_speech_duration,
            'vad_min_silence_duration': settings.vad_min_silence_duration
        };
        
        Object.entries(numberInputs).forEach(([name, value]) => {
            const input = document.querySelector(`input[name="${name}"]`);
            if (input && value !== undefined) {
                input.value = value;
            }
        });
        
        // Cập nhật các checkbox
        const checkboxInputs = {
            'auto_profile_creation': settings.auto_profile_creation,
            'merge_same_speaker': settings.merge_same_speaker,
            'merge_all_speakers': settings.merge_all_speakers,
            'vietnamese_correction_enabled': settings.vietnamese_correction_enabled,
            'realtime_word_mode': settings.realtime_word_mode
        };
        
        Object.entries(checkboxInputs).forEach(([name, value]) => {
            const input = document.querySelector(`input[name="${name}"]`);
            if (input && value !== undefined) {
                input.checked = value;
            }
        });
        
        // Cập nhật biến merge_same_speaker và merge_all_speakers toàn cục
        if (settings.merge_same_speaker !== undefined) {
            mergeSameSpeaker = settings.merge_same_speaker;
        }
        if (settings.merge_all_speakers !== undefined) {
            mergeAllSpeakers = settings.merge_all_speakers;
        }
        
        console.log('Settings UI updated successfully');
    } catch (error) {
        console.error('Error updating settings UI:', error);
    }
}

function showAlert(type, message) {
    const alertsContainer = document.getElementById('alertsContainer');
    if (!alertsContainer) return;
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    
    let icon = type === 'success' ? 'check-circle' : (type === 'info' ? 'info-circle' : 'exclamation-circle');
    
    alert.innerHTML = `
        <i class="fas fa-${icon}"></i>
        ${message}
    `;
    
    alertsContainer.appendChild(alert);
    
    // Thêm hiệu ứng mờ dần trước khi xóa
    setTimeout(() => {
        alert.style.opacity = '0';
        setTimeout(() => {
            alert.remove();
        }, 300);
    }, 2700);
}

// Hàm tách văn bản tại vị trí con trỏ thành kết quả mới
function splitTextAtCursor(textElement, displayIndex) {
    // TẠM DỪNG AUTO-UPDATE để tránh ghi đè
    const wasAutoUpdateEnabled = autoUpdateEnabled;
    autoUpdateEnabled = false;
    
    // Lưu lại vị trí con trỏ trước khi selection có thể bị mất
    let cursorPosition = 0;
    const selection = window.getSelection();
    
    if (selection.rangeCount > 0 && selection.focusNode) {
        const range = selection.getRangeAt(0);
        // Kiểm tra xem selection có nằm trong textElement không
        if (textElement.contains(selection.focusNode)) {
            cursorPosition = range.startOffset;
            // Nếu focusNode là text node con, cần tính offset từ đầu textElement
            if (selection.focusNode !== textElement) {
                const walker = document.createTreeWalker(
                    textElement,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                let totalOffset = 0;
                let currentNode;
                while (currentNode = walker.nextNode()) {
                    if (currentNode === selection.focusNode) {
                        cursorPosition = totalOffset + range.startOffset;
                        break;
                    }
                    totalOffset += currentNode.textContent.length;
                }
            }
        } else {
            // Nếu không có selection hợp lệ, đặt cursor ở giữa text
            cursorPosition = Math.floor(textElement.textContent.length / 2);
        }
    } else {
        // Không có selection, đặt cursor ở giữa text
        cursorPosition = Math.floor(textElement.textContent.length / 2);
    }
    
    const textContent = textElement.textContent;
    
    // Tách văn bản
    const textBefore = textContent.substring(0, cursorPosition).trim();
    const textAfter = textContent.substring(cursorPosition).trim();
    
    if (!textAfter) {
        // Không có văn bản sau con trỏ, chỉ kết thúc chỉnh sửa
        textElement.blur();
        return;
    }
    
    if (!textBefore) {
        // Không có văn bản trước con trỏ, không làm gì cả
        showAlert('warning', 'Không có văn bản trước vị trí con trỏ để giữ lại');
        return;
    }
    
    // Lấy thông tin người nói hiện tại từ result card
    const resultCard = textElement.closest('.result-card');
    let currentSpeakerInfo = '[Người lạ]';
    
    // Cập nhật văn bản hiện tại và tạo kết quả mới cho phần sau con trỏ
    if ((mergeSameSpeaker || mergeAllSpeakers) && resultCard && resultCard.dataset.originalIndices) {
        try {
            // Xử lý chế độ gộp lời nói
            const originalIndices = JSON.parse(resultCard.dataset.originalIndices);
            
            if (originalIndices.length > 0) {
                // Lấy thông tin người nói từ kết quả đầu tiên
                const firstResult = speechResults[originalIndices[0]];
                if (firstResult.includes('[') && firstResult.includes(']')) {
                    const speakerEndPos = firstResult.indexOf(']') + 1;
                    currentSpeakerInfo = firstResult.substring(0, speakerEndPos);
                }
                
                // Cập nhật kết quả đầu tiên với văn bản "trước con trỏ"
                speechResults[originalIndices[0]] = `${currentSpeakerInfo} ${textBefore}`;
                
                // Tạo kết quả mới cho phần sau con trỏ với cùng người nói
                const newResult = `${currentSpeakerInfo} ${textAfter}`;
                
                // Chèn kết quả mới ngay sau kết quả cuối cùng của nhóm gộp
                const lastOriginalIndex = Math.max(...originalIndices);
                speechResults.splice(lastOriginalIndex + 1, 0, newResult);
            }
            
        } catch (error) {
            console.error('Error in merged mode split:', error);
        }
    } else {
        // Chế độ bình thường
        if (displayIndex < speechResults.length) {
            // Lấy thông tin người nói hiện tại
            const currentResult = speechResults[displayIndex];
            if (currentResult.includes('[') && currentResult.includes(']')) {
                const speakerEndPos = currentResult.indexOf(']') + 1;
                currentSpeakerInfo = currentResult.substring(0, speakerEndPos);
            }
            
            // Cập nhật kết quả hiện tại với văn bản "trước con trỏ"
            speechResults[displayIndex] = `${currentSpeakerInfo} ${textBefore}`;
            
            // Tạo kết quả mới cho phần sau con trỏ với cùng người nói
            const newResult = `${currentSpeakerInfo} ${textAfter}`;
            
            // Chèn kết quả mới ngay sau kết quả hiện tại
            speechResults.splice(displayIndex + 1, 0, newResult);
        }
    }
    
    // Cập nhật ngay nội dung DOM element để tránh blur event ghi đè
    textElement.textContent = textBefore;
    
    // Lưu thay đổi và cập nhật hiển thị
    saveResults()
        .then(() => {
            // Cập nhật hiển thị
            const resultsInner = document.querySelector('.results-inner');
            resultsInner.innerHTML = '';
            displayResults(speechResults, resultsInner);
            
            // Cập nhật số lượng kết quả
            prevResultsCount = speechResults.length;
            
            showAlert('info', 'Đã tách văn bản thành 2 kết quả riêng biệt với cùng người nói');
        })
        .catch(error => {
            console.error('Error saving after text split:', error);
            showAlert('error', 'Lỗi khi lưu sau khi tách văn bản');
        })
        .finally(() => {
            // Khôi phục trạng thái auto-update sau một delay ngắn để tránh race condition
            setTimeout(() => {
                autoUpdateEnabled = wasAutoUpdateEnabled;
            }, 200); // 200ms delay để đảm bảo server đã xử lý xong
        });
}

// Hàm tạo dòng kết quả mới ngay dưới dòng được double-click
function createNewResultLineBelow(displayIndex) {
    // TẠM DỪNG AUTO-UPDATE để tránh ghi đè
    const wasAutoUpdateEnabled = autoUpdateEnabled;
    autoUpdateEnabled = false;
    
    // Lấy thông tin người nói hiện tại từ result card
    const resultCard = document.querySelector(`.result-card[data-index="${displayIndex}"]`);
    let currentSpeakerInfo = '[Người lạ]';
    
    // Xử lý chế độ gộp lời nói và chế độ bình thường
    if ((mergeSameSpeaker || mergeAllSpeakers) && resultCard && resultCard.dataset.originalIndices) {
        try {
            // Xử lý chế độ gộp lời nói
            const originalIndices = JSON.parse(resultCard.dataset.originalIndices);
            
            if (originalIndices.length > 0) {
                // Lấy thông tin người nói từ kết quả đầu tiên
                const firstResult = speechResults[originalIndices[0]];
                if (firstResult.includes('[') && firstResult.includes(']')) {
                    const speakerEndPos = firstResult.indexOf(']') + 1;
                    currentSpeakerInfo = firstResult.substring(0, speakerEndPos);
                }
                
                // Tạo kết quả mới với cùng người nói
                const newResult = `${currentSpeakerInfo} `;
                
                // Chèn kết quả mới ngay sau kết quả cuối cùng của nhóm gộp
                const lastOriginalIndex = Math.max(...originalIndices);
                speechResults.splice(lastOriginalIndex + 1, 0, newResult);
            }
            
        } catch (error) {
            console.error('Error in merged mode create new line:', error);
        }
    } else {
        // Chế độ bình thường
        if (displayIndex < speechResults.length) {
            // Lấy thông tin người nói hiện tại
            const currentResult = speechResults[displayIndex];
            if (currentResult.includes('[') && currentResult.includes(']')) {
                const speakerEndPos = currentResult.indexOf(']') + 1;
                currentSpeakerInfo = currentResult.substring(0, speakerEndPos);
            }
            
            // Tạo kết quả mới với cùng người nói
            const newResult = `${currentSpeakerInfo} `;
            
            // Chèn kết quả mới ngay sau kết quả hiện tại
            speechResults.splice(displayIndex + 1, 0, newResult);
        }
    }
    
    // Lưu thay đổi và cập nhật hiển thị
    saveResults()
        .then(() => {
            // Cập nhật hiển thị
            const resultsInner = document.querySelector('.results-inner');
            resultsInner.innerHTML = '';
            displayResults(speechResults, resultsInner);
            
            // Cập nhật số lượng kết quả
            prevResultsCount = speechResults.length;
            
            // Focus vào dòng mới và đặt cursor ở cuối
            setTimeout(() => {
                const newResultIndex = displayIndex + 1;
                const newResultCard = document.querySelector(`.result-card[data-index="${newResultIndex}"]`);
                if (newResultCard) {
                    const textElement = newResultCard.querySelector('.result-text');
                    if (textElement) {
                        textElement.focus();
                        // Đặt cursor ở cuối text
                        const range = document.createRange();
                        const selection = window.getSelection();
                        range.selectNodeContents(textElement);
                        range.collapse(false);
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                }
            }, 100);
            
            showAlert('info', 'Đã tạo dòng mới với cùng người nói');
        })
        .catch(error => {
            console.error('Error saving after creating new line:', error);
            showAlert('error', 'Lỗi khi lưu sau khi tạo dòng mới');
        })
        .finally(() => {
            // Khôi phục trạng thái auto-update sau một delay ngắn để tránh race condition
            setTimeout(() => {
                autoUpdateEnabled = wasAutoUpdateEnabled;
            }, 200); // 200ms delay để đảm bảo server đã xử lý xong
        });
}

// Biến để lưu trữ mapping tên cũ -> tên mới khi đổi tên speaker
let speakerNameMapping = new Map();

// Debug function để kiểm tra speaker name mapping
function debugSpeakerNameMapping() {
    console.log('🔍 === SPEAKER NAME MAPPING DEBUG ===');
    console.log('Current mapping size:', speakerNameMapping.size);
    if (speakerNameMapping.size > 0) {
        console.log('Mappings:');
        speakerNameMapping.forEach((newName, oldName) => {
            console.log(`  "${oldName}" -> "${newName}"`);
        });
    } else {
        console.log('No mappings currently stored');
    }
    console.log('=================================');
}

// Hàm refresh tất cả dropdown hiện tại với preservation của selections
function refreshAllDropdowns() {
    console.log('🔄 Refreshing all dropdowns...');
    return fetchSpeakersWithCache()
        .then(data => {
            const speakers = data.speakers || [];
            const dropdowns = document.querySelectorAll('.speaker-dropdown');

            dropdowns.forEach((dropdown, index) => {
                const currentValue = dropdown.value;
                console.log(`🔍 Processing dropdown ${index} with current value: "${currentValue}"`);

                // Xóa tất cả tùy chọn hiện tại
                dropdown.innerHTML = '';

                // Tùy chọn mặc định - "Người lạ"
                const defaultOption = document.createElement('option');
                defaultOption.value = 'Người lạ';
                defaultOption.textContent = 'Người lạ';
                dropdown.appendChild(defaultOption);

                // Thêm tùy chọn cho mỗi người nói
                speakers.forEach(speaker => {
                    const option = document.createElement('option');
                    option.value = speaker.name;
                    option.textContent = speaker.name;
                    dropdown.appendChild(option);
                });

                // Khôi phục giá trị được chọn với logic cải thiện
                let valueToSelect = currentValue;

                // Kiểm tra xem có mapping tên cũ -> tên mới không
                if (speakerNameMapping.has(currentValue)) {
                    valueToSelect = speakerNameMapping.get(currentValue);
                    console.log(`🔄 Mapping old name "${currentValue}" to new name "${valueToSelect}"`);
                }

                // Kiểm tra xem giá trị có tồn tại trong danh sách mới không
                const optionExists = Array.from(dropdown.options).some(option => option.value === valueToSelect);
                if (optionExists) {
                    dropdown.value = valueToSelect;
                    console.log(`✅ Restored dropdown ${index} to: "${valueToSelect}"`);
                } else {
                    // Nếu giá trị không tồn tại, chọn "Người lạ"
                    dropdown.value = 'Người lạ';
                    console.log(`⚠️ Could not restore dropdown ${index} value "${valueToSelect}" - setting to "Người lạ"`);
                }
            });

            // Clear mapping sau khi sử dụng
            speakerNameMapping.clear();

            console.log(`✅ Refreshed ${dropdowns.length} dropdowns with enhanced state preservation`);
            return speakers;
        })
        .catch(error => {
            console.error('Error refreshing dropdowns:', error);
            throw error;
        });
}

// Hàm gộp văn bản với người nói trước đó
function mergeWithPrevious(index) {
    // Kiểm tra xem có đoạn văn bản trước đó không
    if (index <= 0) {
        showAlert('warning', 'Không có đoạn văn bản trước đó để gộp');
        return;
    }
    
    // Lấy văn bản hiện tại
    const currentResultCard = document.querySelector(`.result-card[data-index="${index}"]`);
    if (!currentResultCard) {
        showAlert('error', 'Không tìm thấy đoạn văn bản hiện tại');
        return;
    }
    
    const currentTextElement = currentResultCard.querySelector('.result-text');
    if (!currentTextElement) {
        showAlert('error', 'Không tìm thấy nội dung văn bản hiện tại');
        return;
    }
    
    const currentText = currentTextElement.textContent.trim();
    
    // Tạm dừng auto-update để tránh ghi đè
    const wasAutoUpdateEnabled = autoUpdateEnabled;
    autoUpdateEnabled = false;
    
    try {
        // Xử lý chế độ gộp lời nói và chế độ bình thường
        if ((mergeSameSpeaker || mergeAllSpeakers) && currentResultCard.dataset.originalIndices) {
            // Chế độ gộp lời nói - phức tạp hơn
            const originalIndices = JSON.parse(currentResultCard.dataset.originalIndices);
            
            if (originalIndices.length === 0) {
                showAlert('error', 'Không có dữ liệu gốc để xử lý');
                return;
            }
            
            // Tìm đoạn văn bản trước đó trong speechResults
            const firstOriginalIndex = Math.min(...originalIndices);
            if (firstOriginalIndex <= 0) {
                showAlert('warning', 'Không có đoạn văn bản trước đó để gộp');
                return;
            }
            
            // Lấy thông tin của đoạn văn bản trước đó
            const previousResult = speechResults[firstOriginalIndex - 1];
            if (!previousResult) {
                showAlert('warning', 'Không tìm thấy đoạn văn bản trước đó');
                return;
            }
            
            // Trích xuất thông tin người nói từ đoạn văn bản trước đó
            let previousSpeakerInfo = '[Người lạ]';
            let previousText = '';
            
            if (previousResult.includes('[') && previousResult.includes(']')) {
                const speakerEndPos = previousResult.indexOf(']') + 1;
                previousSpeakerInfo = previousResult.substring(0, speakerEndPos);
                previousText = previousResult.substring(speakerEndPos).trim();
            } else {
                previousText = previousResult.trim();
            }
            
            // Gộp văn bản
            const mergedText = `${previousText} ${currentText}`.trim();
            
            // Cập nhật đoạn văn bản trước đó với văn bản đã gộp
            speechResults[firstOriginalIndex - 1] = `${previousSpeakerInfo} ${mergedText}`;
            
            // Xóa tất cả các đoạn văn bản hiện tại (theo thứ tự ngược để tránh thay đổi index)
            originalIndices.sort((a, b) => b - a).forEach(originalIndex => {
                if (originalIndex < speechResults.length) {
                    speechResults.splice(originalIndex, 1);
                }
            });
            
        } else {
            // Chế độ bình thường
            if (index >= speechResults.length) {
                showAlert('error', 'Index không hợp lệ');
                return;
            }
            
            // Lấy thông tin đoạn văn bản trước đó
            const previousResult = speechResults[index - 1];
            const currentResult = speechResults[index];
            
            // Trích xuất thông tin người nói và văn bản từ đoạn trước đó
            let previousSpeakerInfo = '[Người lạ]';
            let previousText = '';
            
            if (previousResult.includes('[') && previousResult.includes(']')) {
                const speakerEndPos = previousResult.indexOf(']') + 1;
                previousSpeakerInfo = previousResult.substring(0, speakerEndPos);
                previousText = previousResult.substring(speakerEndPos).trim();
            } else {
                previousText = previousResult.trim();
            }
            
            // Gộp văn bản
            const mergedText = `${previousText} ${currentText}`.trim();
            
            // Cập nhật đoạn văn bản trước đó với văn bản đã gộp
            speechResults[index - 1] = `${previousSpeakerInfo} ${mergedText}`;
            
            // Xóa đoạn văn bản hiện tại
            speechResults.splice(index, 1);
        }
        
        // Lưu thay đổi lên server
        saveResults()
            .then(() => {
                // Cập nhật hiển thị
                const resultsInner = document.querySelector('.results-inner');
                resultsInner.innerHTML = '';
                displayResults(speechResults, resultsInner);
                
                // Cập nhật số lượng kết quả
                prevResultsCount = speechResults.length;
                
                showAlert('success', 'Đã gộp văn bản với người nói trước đó thành công');
            })
            .catch(error => {
                console.error('Error saving after merge:', error);
                showAlert('error', 'Lỗi khi lưu sau khi gộp văn bản');
            })
            .finally(() => {
                // Khôi phục trạng thái auto-update
                setTimeout(() => {
                    autoUpdateEnabled = wasAutoUpdateEnabled;
                }, 200);
            });
            
    } catch (error) {
        console.error('Error in mergeWithPrevious:', error);
        showAlert('error', 'Có lỗi xảy ra khi gộp văn bản');
        // Khôi phục trạng thái auto-update
        autoUpdateEnabled = wasAutoUpdateEnabled;
    }
}

// Hàm sửa văn bản tiếng Việt cho một đoạn cụ thể
async function correctText(index) {
    try {
        // Vô hiệu hóa tạm thời auto-update để tránh xung đột
        const wasAutoUpdateEnabled = autoUpdateEnabled;
        autoUpdateEnabled = false;
        
        let textToCorrect = '';
        
        // Lấy văn bản cần sửa từ speechResults
        if (mergeSameSpeaker) {
            // Trong chế độ gộp lời nói, cần lấy text từ DOM element
            const resultCard = document.querySelector(`.result-card[data-index="${index}"]`);
            if (resultCard) {
                const textElement = resultCard.querySelector('.result-text');
                if (textElement) {
                    textToCorrect = textElement.textContent.trim();
                }
            }
        } else {
            // Trong chế độ hiển thị bình thường
            if (index >= 0 && index < speechResults.length) {
                const result = speechResults[index];
                // Trích xuất văn bản từ format: [timestamp] [speaker] text
                const textMatch = result.match(/\] \[.*?\] (.+)$/);
                textToCorrect = textMatch ? textMatch[1] : result;
            }
        }
        
        if (!textToCorrect || textToCorrect.trim() === '') {
            showAlert('warning', 'Không có văn bản để sửa');
            autoUpdateEnabled = wasAutoUpdateEnabled;
            return;
        }
        
        // Hiển thị loading
       
        showAlert('info', 'Đang sửa văn bản tiếng Việt...');
        
        // Gửi yêu cầu sửa văn bản đến server
        const response = await fetch('/correct_text', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: textToCorrect.trim()
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Cập nhật văn bản đã sửa vào kết quả
            if (mergeSameSpeaker) {
                // Cập nhật trong chế độ gộp lời nói
                const resultCard = document.querySelector(`.result-card[data-index="${index}"]`);
                if (resultCard && resultCard.dataset.originalIndices) {
                    const originalIndices = JSON.parse(resultCard.dataset.originalIndices);
                    // Cập nhật tất cả các kết quả gốc thuộc về nhóm này
                    originalIndices.forEach(originalIndex => {
                        if (originalIndex < speechResults.length) {
                            const originalResult = speechResults[originalIndex];
                            const speakerMatch = originalResult.match(/^(\[.*?\] \[.*?\] )/);
                            if (speakerMatch) {
                                speechResults[originalIndex] = speakerMatch[1] + data.corrected_text;
                            }
                        }
                    });
                }
            } else {
                // Cập nhật trong chế độ hiển thị bình thường
                if (index >= 0 && index < speechResults.length) {
                    const result = speechResults[index];
                    const speakerMatch = result.match(/^(\[.*?\] \[.*?\] )/);
                    if (speakerMatch) {
                        speechResults[index] = speakerMatch[1] + data.corrected_text;
                    }
                }
            }
            
            // Lưu kết quả và cập nhật hiển thị
            saveResults().then(() => {
                // Cập nhật hiển thị
                const resultsInner = document.querySelector('.results-inner');
                if (resultsInner) {
                    resultsInner.innerHTML = '';
                    displayResults(speechResults, resultsInner);
                }
                
                // Cập nhật số lượng kết quả
                prevResultsCount = speechResults.length;
            });
            
            showAlert('success', `Đã sửa văn bản thành công: "${data.original_text}" → "${data.corrected_text}"`);
        } else {
            showAlert('error', data.message || 'Không thể sửa văn bản này');
        }
        
        // Khôi phục trạng thái auto-update sau một chút
        setTimeout(() => {
            autoUpdateEnabled = wasAutoUpdateEnabled;
        }, 200);
        
    } catch (error) {
        console.error('Error in correctText:', error);
        showAlert('error', 'Có lỗi xảy ra khi sửa văn bản tiếng Việt');
        // Khôi phục trạng thái auto-update
        setTimeout(() => {
            autoUpdateEnabled = wasAutoUpdateEnabled;
        }, 200);
    }
}









// Hàm refresh tất cả speaker names trong saved_results.json
function refreshAllSpeakerNamesInResults() {
    console.log('🔄 Refreshing all speaker names in saved results...');

    return fetch('/refresh_all_speaker_names', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // console.log(`✅ Refreshed ${data.changes_count} speaker names in ${data.total_results} results`);
            if (data.changes_count > 0) {
                showAlert('success', `Đã cập nhật ${data.changes_count} tên người nói trong kết quả đã lưu`);
            } else {
                console.log('ℹ️ No speaker name changes needed in saved results');
            }
            return data;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    })
    .catch(error => {
        console.error('❌ Failed to refresh speaker names in results:', error);
        showAlert('error', 'Lỗi khi cập nhật tên người nói trong kết quả: ' + error.message);
        throw error;
    });
}

// Hàm manual refresh - clear cache và reload tất cả dữ liệu
function manualRefreshAll() {
    console.log('🔄 Manual refresh all data...');

    // Clear cache hoàn toàn
    speakersCache = null;
    speakersCacheTime = 0;

    // Đặt flag để reload dropdown vì đây là manual refresh
    shouldReloadDropdowns = true;

    // Show loading indicator
    showAlert('info', 'Đang refresh tất cả dữ liệu...');

    // Force refresh tất cả với logging chi tiết
    console.log('🔄 Force refreshing results, speakers, and speaker names in saved results...');
    Promise.all([
        fetchResults('manual_refresh'),
        fetchSpeakers(true), // Force refresh speakers
        refreshAllSpeakerNamesInResults() // Refresh speaker names in saved results
    ]).then(() => {
        // Reload dropdown sau khi refresh xong
        return refreshAllDropdowns();
    }).then(() => {
        showAlert('success', 'Đã refresh tất cả dữ liệu và tên người nói thành công!');
        // console.log('✅ Manual refresh completed - all data and speaker names refreshed');
        shouldReloadDropdowns = false; // Reset flag
    }).catch(error => {
        console.error('❌ Manual refresh failed:', error);
        showAlert('error', 'Lỗi khi refresh dữ liệu');
        shouldReloadDropdowns = false; // Reset flag on error
    });
}

// Expose function globally for button onclick
window.manualRefreshAll = manualRefreshAll;

// Event listener để detect F5 hoặc Ctrl+R - optimized
document.addEventListener('keydown', function(event) {
    // F5 key - chỉ set flag khi thực sự cần thiết
    if (event.key === 'F5') {
        console.log('🔄 F5 detected - selective refresh on next load');
        // Chỉ refresh khi có speakers data
        if (speakersCache && speakersCache.speakers && speakersCache.speakers.length > 0) {
            sessionStorage.setItem('forceRefreshSpeakers', 'true');
        }
    }

    // Ctrl+R - tương tự
    if (event.ctrlKey && event.key === 'r') {
        console.log('🔄 Ctrl+R detected - selective refresh on next load');
        if (speakersCache && speakersCache.speakers && speakersCache.speakers.length > 0) {
            sessionStorage.setItem('forceRefreshSpeakers', 'true');
        }
    }
});

// Event listener để detect beforeunload - optimized để tránh excessive flags
window.addEventListener('beforeunload', function() {
    // Chỉ set flag nếu thực sự cần thiết
    const now = Date.now();
    const cacheAge = now - speakersCacheTime;

    // Chỉ force refresh nếu cache đã cũ (> 30 giây) hoặc có nhiều speakers
    if (cacheAge > 30000 || (speakersCache && speakersCache.speakers && speakersCache.speakers.length > 5)) {
        console.log('🔄 Page unloading - setting selective refresh flags');
        sessionStorage.setItem('forceRefreshSpeakers', 'true');
    }

    // Lưu trạng thái speakers được chọn
    saveSpeakerSelection();
});

// ===== SPEAKER SELECTION FUNCTIONS =====

// Event delegation handlers for speaker clicks
function handleSpeakerClick(e) {
    const nameCell = e.target.closest('.speaker-name-cell');
    if (!nameCell) return;

    e.stopPropagation();
    const speakerName = nameCell.dataset.speakerName;

    if (speakerName === 'Người mới') {
        toggleNewSpeakerProfile();
    } else {
        toggleSpeakerSelection(speakerName);
    }
}

function handleSpeakerDblClick(e) {
    const nameCell = e.target.closest('.speaker-name-cell');
    if (!nameCell) return;

    e.stopPropagation();
    const speakerName = nameCell.dataset.speakerName;

    if (speakerName !== 'Người mới') {
        addSpeakerToResults(speakerName);
    }
}

// Khởi tạo speaker selection
function initSpeakerSelection() {
    console.log('🎯 Initializing speaker selection...');
    loadSpeakerSelection();

    // Cập nhật indicator sau khi load
    setTimeout(() => {
        updateSpeakerSelectionIndicator();
    }, 500);
}

// Lưu trạng thái speakers được chọn vào localStorage
function saveSpeakerSelection() {
    try {
        localStorage.setItem(SELECTED_SPEAKERS_KEY, JSON.stringify(Array.from(selectedSpeakers)));
        console.log('💾 Saved speaker selection:', Array.from(selectedSpeakers));
    } catch (error) {
        console.error('Error saving speaker selection:', error);
    }
}

// Tải trạng thái speakers được chọn từ localStorage
function loadSpeakerSelection() {
    try {
        const saved = localStorage.getItem(SELECTED_SPEAKERS_KEY);
        if (saved) {
            const speakers = JSON.parse(saved);
            selectedSpeakers = new Set(speakers);
            console.log('📂 Loaded speaker selection from localStorage:', Array.from(selectedSpeakers));
            console.log('📊 Speaker selection count:', selectedSpeakers.size);

            // Kiểm tra nếu có quá nhiều speakers được chọn (có thể là bug)
            if (selectedSpeakers.size > 5) {
                console.warn('⚠️ Too many speakers selected, this might be a bug. Will validate against server.');
            }

            // Cập nhật indicator ngay sau khi load
            updateSpeakerSelectionIndicator();

            // Validate speakers against server data sau khi load speakers
            setTimeout(() => validateSpeakerSelection(), 1000);
        } else {
            console.log('📂 No saved speaker selection found, starting fresh');
            // Đảm bảo indicator được cập nhật ngay cả khi không có selection
            updateSpeakerSelectionIndicator();
        }
    } catch (error) {
        console.error('Error loading speaker selection:', error);
        selectedSpeakers = new Set();
        updateSpeakerSelectionIndicator();
    }
}

// Cập nhật server với danh sách speakers được chọn
function updateServerSpeakerSelection() {
    const speakers = Array.from(selectedSpeakers);

    fetch('/selected_speakers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            selected_speakers: speakers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // console.log('✅ Updated server speaker selection:', speakers);
        } else {
            console.error('❌ Failed to update server speaker selection:', data.message);
        }
    })
    .catch(error => {
        console.error('❌ Error updating server speaker selection:', error);
    });
}

// Reset speaker selection (for debugging)
function resetSpeakerSelection() {
    console.log('🔄 Resetting speaker selection...');
    selectedSpeakers.clear();
    localStorage.removeItem(SELECTED_SPEAKERS_KEY);
    updateSpeakerSelectionUI();
    updateServerSpeakerSelection();
    console.log('✅ Speaker selection reset completed');
}

// Debug function to check current speaker selection state
function debugSpeakerSelection() {
    console.log('🔍 === SPEAKER SELECTION DEBUG ===');
    console.log('Current selectedSpeakers Set:', selectedSpeakers);
    console.log('Current selectedSpeakers Array:', Array.from(selectedSpeakers));
    console.log('selectedSpeakers.size:', selectedSpeakers.size);

    const saved = localStorage.getItem(SELECTED_SPEAKERS_KEY);
    console.log('localStorage raw value:', saved);
    if (saved) {
        try {
            const parsed = JSON.parse(saved);
            console.log('localStorage parsed:', parsed);
            console.log('localStorage length:', parsed.length);
        } catch (e) {
            console.log('localStorage parse error:', e);
        }
    }

    // Check speaker name mapping
    if (speakerNameMapping.size > 0) {
        console.log('Active speaker name mappings:');
        speakerNameMapping.forEach((newName, oldName) => {
            console.log(`  "${oldName}" -> "${newName}"`);
        });
    } else {
        console.log('No active speaker name mappings');
    }

    const selectedCountElement = document.getElementById('selectedCount');
    if (selectedCountElement) {
        console.log('UI selectedCount text:', selectedCountElement.textContent);
    }

    console.log('🔍 === DEBUG COMPLETE ===');
}

// Validate speaker selection against server data
function validateSpeakerSelection() {
    console.log('🔍 Validating speaker selection against server...');

    fetchSpeakersWithCache(true) // Force refresh to get latest data
        .then(data => {
            const serverSpeakers = data.speakers || [];
            const validSpeakerNames = new Set(serverSpeakers.map(s => s.name));

            // Add special speakers that are always valid
            validSpeakerNames.add('Người mới');

            console.log('📋 Valid speakers from server:', Array.from(validSpeakerNames));
            console.log('📋 Current selected speakers:', Array.from(selectedSpeakers));

            // Apply speaker name mapping before validation
            const mappedSelectedSpeakers = new Set();
            selectedSpeakers.forEach(speaker => {
                let mappedName = speaker;
                if (speakerNameMapping.has(speaker)) {
                    mappedName = speakerNameMapping.get(speaker);
                    console.log(`🔄 Mapping speaker "${speaker}" to "${mappedName}" during validation`);
                }
                mappedSelectedSpeakers.add(mappedName);
            });

            // Filter out invalid speakers
            const validSelectedSpeakers = new Set();
            const invalidSpeakers = [];

            mappedSelectedSpeakers.forEach(speaker => {
                if (validSpeakerNames.has(speaker)) {
                    validSelectedSpeakers.add(speaker);
                } else {
                    invalidSpeakers.push(speaker);
                }
            });

            if (invalidSpeakers.length > 0) {
                console.warn('⚠️ Found invalid speakers after mapping, removing:', invalidSpeakers);
                selectedSpeakers = validSelectedSpeakers;

                // Save cleaned selection
                saveSpeakerSelection();
                updateServerSpeakerSelection();
                updateSpeakerSelectionUI();

                console.log('✅ Cleaned speaker selection:', Array.from(selectedSpeakers));
            } else {
                console.log('✅ All selected speakers are valid after mapping');

                // Update selectedSpeakers with mapped names if any mapping occurred
                if (mappedSelectedSpeakers.size > 0 && !areSetsEqual(selectedSpeakers, mappedSelectedSpeakers)) {
                    selectedSpeakers = mappedSelectedSpeakers;
                    saveSpeakerSelection();
                    updateSpeakerSelectionUI();
                    console.log('✅ Updated selectedSpeakers with mapped names:', Array.from(selectedSpeakers));
                }

                // Still update server to ensure sync
                updateServerSpeakerSelection();
            }
        })
        .catch(error => {
            console.error('❌ Error validating speaker selection:', error);
        });
}

// Helper function to compare two Sets
function areSetsEqual(set1, set2) {
    if (set1.size !== set2.size) return false;
    for (let item of set1) {
        if (!set2.has(item)) return false;
    }
    return true;
}

// Debug function to check fetch status
function debugFetchStatus() {
    console.log('🔍 === FETCH STATUS DEBUG ===');
    console.log('fetchSpeakersInProgress:', fetchSpeakersInProgress);
    console.log('fetchSpeakersTimeout:', fetchSpeakersTimeout);
    console.log('speakersCache:', speakersCache ? 'Available' : 'Null');
    console.log('speakersCacheTime:', speakersCacheTime);
    console.log('Cache age (ms):', Date.now() - speakersCacheTime);
    console.log('Cache valid:', speakersCache && (Date.now() - speakersCacheTime) < SPEAKERS_CACHE_DURATION);
    console.log('🔍 === DEBUG COMPLETE ===');
}

// Force clear fetch status (emergency reset)
function forceClearFetchStatus() {
    console.log('🚨 Force clearing fetch status...');
    fetchSpeakersInProgress = false;
    if (fetchSpeakersTimeout) {
        clearTimeout(fetchSpeakersTimeout);
        fetchSpeakersTimeout = null;
    }
    console.log('✅ Fetch status cleared');
}

// Expose functions globally for debugging
window.resetSpeakerSelection = resetSpeakerSelection;
window.debugSpeakerSelection = debugSpeakerSelection;
window.validateSpeakerSelection = validateSpeakerSelection;
window.debugFetchStatus = debugFetchStatus;
window.forceClearFetchStatus = forceClearFetchStatus;

// Debouncing cho toggle operations
let toggleTimeout = null;

// Tự động chọn một speaker (dùng cho speaker mới được tạo)
function autoSelectSpeaker(speakerName) {
    if (!speakerName || selectedSpeakers.has(speakerName)) {
        return; // Đã được chọn rồi hoặc tên không hợp lệ
    }

    selectedSpeakers.add(speakerName);
    console.log(`🎯 Auto-selected speaker "${speakerName}"`);

    // Lưu trạng thái
    saveSpeakerSelection();

    // Cập nhật server
    updateServerSpeakerSelection();

    // Cập nhật UI
    updateSpeakerSelectionUI();

    console.log(`🔄 Updated selection (${selectedSpeakers.size}): ${Array.from(selectedSpeakers)}`);
}

// Toggle chọn/bỏ chọn một speaker
function toggleSpeakerSelection(speakerName) {
    // Debouncing để tránh multiple rapid toggles
    if (toggleTimeout) {
        clearTimeout(toggleTimeout);
    }

    toggleTimeout = setTimeout(() => {
        const wasSelected = selectedSpeakers.has(speakerName);

        if (wasSelected) {
            selectedSpeakers.delete(speakerName);
            console.log(`➖ Removed speaker "${speakerName}"`);
        } else {
            selectedSpeakers.add(speakerName);
            console.log(`➕ Added speaker "${speakerName}"`);
        }

        // Lưu trạng thái
        saveSpeakerSelection();

        // Cập nhật server
        updateServerSpeakerSelection();

        // Cập nhật UI
        updateSpeakerSelectionUI();

        console.log(`🔄 Final selection (${selectedSpeakers.size}): ${Array.from(selectedSpeakers)}`);
        toggleTimeout = null;
    }, 100); // 100ms debounce
}



// Cập nhật UI hiển thị trạng thái chọn
function updateSpeakerSelectionUI() {
    // Cập nhật trạng thái các hàng trong bảng
    const speakersTable = document.getElementById('speakersTableBody');
    if (speakersTable) {
        const rows = speakersTable.querySelectorAll('tr');
        rows.forEach(row => {
            const nameCell = row.querySelector('.speaker-name-cell');
            const speakerName = nameCell ? nameCell.dataset.speakerName : null;

            if (speakerName) {
                const isSelected = selectedSpeakers.has(speakerName);

                if (isSelected) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
            }
        });
    }

    // Cập nhật speaker selection indicator
    updateSpeakerSelectionIndicator();
}

// Cập nhật speaker selection indicator
function updateSpeakerSelectionIndicator() {
    const indicator = document.getElementById('speakerSelectionIndicator');
    const countElement = document.getElementById('selectedCount');
    const listElement = document.getElementById('selectedSpeakersList');

    if (!indicator || !countElement || !listElement) {
        console.warn('Speaker selection indicator elements not found');
        return;
    }

    const selectedArray = Array.from(selectedSpeakers);
    const count = selectedArray.length;

    // Cập nhật số lượng
    countElement.textContent = count;

    // Cập nhật danh sách
    if (count === 0) {
        listElement.innerHTML = '<span class="no-selection">Chưa chọn người nói nào</span>';
        indicator.classList.add('hidden');
    } else {
        // Tạo tags cho từng speaker được chọn (layout ngang)
        const speakerTags = selectedArray.map(speaker => {
            const isNewSpeaker = speaker === 'Người mới';
            const tagClass = isNewSpeaker ? 'speaker-tag new-speaker' : 'speaker-tag';
            // Rút gọn tên thông minh hơn để fit nhiều speakers
            let displayName = speaker;
            if (speaker.length > 12) {
                // Ưu tiên giữ lại từ cuối (thường là tên)
                const words = speaker.split(' ');
                if (words.length > 1) {
                    displayName = words[0].charAt(0) + '. ' + words[words.length - 1];
                    if (displayName.length > 12) {
                        displayName = displayName.substring(0, 9) + '...';
                    }
                } else {
                    displayName = speaker.substring(0, 9) + '...';
                }
            }
            return `<span class="${tagClass}" title="${speaker}">${displayName}</span>`;
        }).join('');

        listElement.innerHTML = speakerTags;
        indicator.classList.remove('hidden');
    }

    console.log(`🎯 Updated speaker selection indicator: ${count} speakers selected`);
}



// ===== AUDIO DEVICE MANAGEMENT =====

// Enumerate available audio input devices
async function enumerateAudioDevices() {
    try {
        console.log('🎤 Enumerating audio devices...');

        // Request permission first to get device labels
        await navigator.mediaDevices.getUserMedia({ audio: true });

        // Get all media devices
        const devices = await navigator.mediaDevices.enumerateDevices();

        // Filter for audio input devices
        availableAudioDevices = devices.filter(device => device.kind === 'audioinput');

        console.log(`🎤 Found ${availableAudioDevices.length} audio input devices:`, availableAudioDevices);

        // Update UI with available devices
        updateAudioDeviceUI();

        return availableAudioDevices;
    } catch (error) {
        console.error('❌ Error enumerating audio devices:', error);
        availableAudioDevices = [];
        return [];
    }
}

// Update audio device selection UI
function updateAudioDeviceUI() {
    const deviceSelect = document.getElementById('audioDeviceSelect');
    if (!deviceSelect) return;

    // Clear existing options
    deviceSelect.innerHTML = '';

    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Thiết bị mặc định';
    deviceSelect.appendChild(defaultOption);

    // Add available devices
    availableAudioDevices.forEach(device => {
        const option = document.createElement('option');
        option.value = device.deviceId;
        option.textContent = device.label || `Microphone ${device.deviceId.substring(0, 8)}...`;
        deviceSelect.appendChild(option);
    });

    // Validate and set selected device
    if (selectedAudioDeviceId) {
        const deviceExists = availableAudioDevices.some(device => device.deviceId === selectedAudioDeviceId);
        if (deviceExists) {
            deviceSelect.value = selectedAudioDeviceId;
            console.log(`🎤 Restored saved device: ${selectedAudioDeviceId}`);
        } else {
            console.log(`⚠️ Saved device ${selectedAudioDeviceId} no longer available, using default`);
            selectedAudioDeviceId = null;
            localStorage.removeItem('selectedAudioDeviceId');
            deviceSelect.value = '';
        }
    }

    console.log(`🎤 Updated device UI with ${availableAudioDevices.length} devices`);
}

// Handle audio device selection change
function onAudioDeviceChange() {
    const deviceSelect = document.getElementById('audioDeviceSelect');
    if (!deviceSelect) return;

    const newDeviceId = deviceSelect.value;

    if (newDeviceId !== selectedAudioDeviceId) {
        console.log(`🎤 Switching audio device from ${selectedAudioDeviceId || 'default'} to ${newDeviceId || 'default'}`);

        selectedAudioDeviceId = newDeviceId;

        // Save to localStorage
        if (newDeviceId) {
            localStorage.setItem('selectedAudioDeviceId', newDeviceId);
        } else {
            localStorage.removeItem('selectedAudioDeviceId');
        }

        // Restart audio monitoring with new device
        if (isMonitoringAudio) {
            restartAudioMonitoring();
        }
    }
}

// Refresh audio devices list
async function refreshAudioDevices() {
    console.log('🔄 Refreshing audio devices...');

    // Show loading state
    const deviceSelect = document.getElementById('audioDeviceSelect');
    if (deviceSelect) {
        deviceSelect.innerHTML = '<option value="">Đang tải thiết bị...</option>';
    }

    try {
        await enumerateAudioDevices();
        showAlert('success', 'Đã làm mới danh sách thiết bị audio');
    } catch (error) {
        console.error('❌ Error refreshing audio devices:', error);
        showAlert('error', 'Không thể làm mới danh sách thiết bị');
    }
}

// ===== AUDIO MONITORING FUNCTIONS =====

// Khởi tạo audio monitoring
function initAudioMonitoring() {
    console.log('🎤 Initializing audio monitoring...');

    // Load saved silence threshold
    const savedThreshold = localStorage.getItem('silenceThreshold');
    if (savedThreshold) {
        silenceThreshold = parseInt(savedThreshold);
        updateSilenceThresholdUI(silenceThreshold);
    }

    // Load saved audio device
    const savedDeviceId = localStorage.getItem('selectedAudioDeviceId');
    if (savedDeviceId) {
        selectedAudioDeviceId = savedDeviceId;
    }

    // Enumerate audio devices first
    enumerateAudioDevices().then(() => {
        // Start audio monitoring
        startAudioMonitoring();
    });
}

// Bắt đầu monitoring audio
async function startAudioMonitoring() {
    try {
        console.log('🎤 Starting audio monitoring...');

        // Check if browser supports getUserMedia
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('Browser không hỗ trợ getUserMedia');
        }

        // Request microphone access with device constraints
        console.log('🎤 Requesting microphone access...');

        // Build audio constraints
        const audioConstraints = {
            echoCancellation: false,  // Tắt để có audio thô hơn
            noiseSuppression: false,  // Tắt để có audio thô hơn
            autoGainControl: false,   // Tắt để có audio thô hơn
            sampleRate: 16000        // Sample rate phù hợp
        };

        // Add device ID if specific device is selected
        if (selectedAudioDeviceId) {
            audioConstraints.deviceId = { exact: selectedAudioDeviceId };
            console.log(`🎤 Using specific device: ${selectedAudioDeviceId}`);
        } else {
            console.log('🎤 Using default audio device');
        }

        const stream = await navigator.mediaDevices.getUserMedia({
            audio: audioConstraints
        });
        // console.log('✅ Microphone access granted');

        // Create audio context
        console.log('🎤 Creating audio context...');
        audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // Resume audio context if suspended (required by some browsers)
        if (audioContext.state === 'suspended') {
            await audioContext.resume();
            console.log('🎤 Audio context resumed');
        }

        analyser = audioContext.createAnalyser();
        microphone = audioContext.createMediaStreamSource(stream);

        // Configure analyser for better sensitivity
        analyser.fftSize = 512;  // Tăng để có độ phân giải tốt hơn
        analyser.smoothingTimeConstant = 0.3;  // Giảm để responsive hơn
        analyser.minDecibels = -90;
        analyser.maxDecibels = -10;

        const bufferLength = analyser.frequencyBinCount;
        dataArray = new Uint8Array(bufferLength);

        console.log('🎤 Analyser configured:', {
            fftSize: analyser.fftSize,
            frequencyBinCount: bufferLength,
            sampleRate: audioContext.sampleRate
        });

        // Connect microphone to analyser
        microphone.connect(analyser);

        isMonitoringAudio = true;

        // Start monitoring loop
        startAudioLevelLoop();

        // console.log('✅ Audio monitoring started successfully');
        showAlert('success', 'Audio monitoring đã bắt đầu');

        // Update status to show it's working
        const statusText = document.getElementById('audioStatusText');
        if (statusText) {
            statusText.textContent = 'Đang lắng nghe...';
            statusText.style.color = '#28a745';
        }

    } catch (error) {
        console.error('❌ Error starting audio monitoring:', error);

        let errorMessage = 'Không thể truy cập microphone.';
        let shouldTryFallback = false;

        if (error.name === 'NotAllowedError') {
            errorMessage = 'Quyền truy cập microphone bị từ chối. Vui lòng cho phép truy cập microphone.';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'Không tìm thấy microphone. Vui lòng kiểm tra thiết bị.';
            shouldTryFallback = true;
        } else if (error.name === 'NotSupportedError') {
            errorMessage = 'Browser không hỗ trợ audio monitoring.';
        } else if (error.name === 'OverconstrainedError' || error.name === 'ConstraintNotSatisfiedError') {
            errorMessage = 'Thiết bị audio đã chọn không khả dụng.';
            shouldTryFallback = true;
        }

        // Try fallback to default device if specific device failed
        if (shouldTryFallback && selectedAudioDeviceId) {
            console.log('🔄 Trying fallback to default audio device...');
            selectedAudioDeviceId = null;
            localStorage.removeItem('selectedAudioDeviceId');

            // Update UI to show default device
            const deviceSelect = document.getElementById('audioDeviceSelect');
            if (deviceSelect) {
                deviceSelect.value = '';
            }

            // Try again with default device
            try {
                await startAudioMonitoring();
                showAlert('warning', 'Thiết bị đã chọn không khả dụng. Đã chuyển về thiết bị mặc định.');
                return;
            } catch (fallbackError) {
                console.error('❌ Fallback also failed:', fallbackError);
                errorMessage = 'Không thể truy cập bất kỳ thiết bị audio nào.';
            }
        }

        showAlert('error', errorMessage);

        // Reset UI to show error state
        updateAudioLevelUI(0);
        const valueDisplay = document.getElementById('audioLevelValue');
        if (valueDisplay) {
            valueDisplay.textContent = 'Lỗi';
        }

        // Set error status
        setAudioErrorStatus(errorMessage);
    }
}

// Dừng audio monitoring
function stopAudioMonitoring() {
    console.log('🛑 Stopping audio monitoring...');

    isMonitoringAudio = false;

    if (audioMonitoringInterval) {
        clearInterval(audioMonitoringInterval);
        audioMonitoringInterval = null;
    }

    if (microphone) {
        microphone.disconnect();
        microphone = null;
    }

    if (audioContext) {
        audioContext.close();
        audioContext = null;
    }

    analyser = null;
    dataArray = null;

    // Reset UI
    updateAudioLevelUI(0);

    // Reset status
    const statusText = document.getElementById('audioStatusText');
    const statusIndicator = document.getElementById('audioStatusIndicator');

    if (statusText) {
        statusText.textContent = 'Đã dừng';
        statusText.style.color = '#6c757d';
    }

    if (statusIndicator) {
        statusIndicator.classList.remove('speaking', 'silent', 'error');
    }

    // console.log('✅ Audio monitoring stopped');
}

// Vòng lặp monitoring audio level
function startAudioLevelLoop() {
    if (!isMonitoringAudio || !analyser || !dataArray) {
        console.error('❌ Cannot start audio loop - missing components');
        return;
    }

    console.log('🎤 Starting audio level monitoring loop...');

    function updateLoop() {
        if (!isMonitoringAudio || !analyser || !dataArray) {
            return;
        }

        try {
            // Get frequency data
            analyser.getByteFrequencyData(dataArray);

            // Method 1: Calculate RMS (Root Mean Square) for better accuracy
            let sum = 0;
            for (let i = 0; i < dataArray.length; i++) {
                sum += dataArray[i] * dataArray[i];
            }
            const rms = Math.sqrt(sum / dataArray.length);

            // Method 2: Also calculate simple average for comparison
            let simpleSum = 0;
            for (let i = 0; i < dataArray.length; i++) {
                simpleSum += dataArray[i];
            }
            const average = simpleSum / dataArray.length;

            // Method 3: Find peak value
            const peak = Math.max(...dataArray);

            // Use RMS as primary method, but boost it for better visibility
            let volumePercent = Math.round((rms / 255) * 100 * 2); // Boost by 2x

            // Cap at 100%
            volumePercent = Math.min(volumePercent, 100);

            // Debug logging (remove in production)
            if (volumePercent > 5) {  // Only log when there's actual audio
                console.log(`🎤 Audio levels - RMS: ${rms.toFixed(1)}, Avg: ${average.toFixed(1)}, Peak: ${peak}, Final: ${volumePercent}%`);
            }

            // Update UI
            updateAudioLevelUI(volumePercent);

        } catch (error) {
            console.error('❌ Error in audio monitoring loop:', error);
        }

        // Schedule next update
        if (isMonitoringAudio) {
            requestAnimationFrame(updateLoop);
        }
    }

    // Start the loop
    updateLoop();
}

// Cập nhật UI hiển thị audio level
function updateAudioLevelUI(level) {
    const indicator = document.getElementById('audioLevelIndicator');
    const valueDisplay = document.getElementById('audioLevelValue');

    if (indicator) {
        indicator.style.width = level + '%';
    }

    if (valueDisplay) {
        valueDisplay.textContent = level;
    }

    // Update status indicator and text
    updateAudioStatusIndicator(level);
    updateAudioStatusText(level);
}

// Cập nhật status indicator (speaking/silent)
function updateAudioStatusIndicator(level) {
    const statusIndicator = document.getElementById('audioStatusIndicator');
    if (statusIndicator) {
        // Remove all status classes
        statusIndicator.classList.remove('speaking', 'silent', 'error');

        if (level > silenceThreshold) {
            statusIndicator.classList.add('speaking');
        } else {
            statusIndicator.classList.add('silent');
        }
    }
}

// Cập nhật status text
function updateAudioStatusText(level) {
    const statusText = document.getElementById('audioStatusText');
    if (statusText) {
        if (level > silenceThreshold) {
            statusText.textContent = `Đang nói (${level}%)`;
            statusText.style.color = '#28a745';
        } else {
            statusText.textContent = `Im lặng (${level}%)`;
            statusText.style.color = '#6c757d';
        }
    }
}

// Set error status
function setAudioErrorStatus(message) {
    const statusIndicator = document.getElementById('audioStatusIndicator');
    const statusText = document.getElementById('audioStatusText');

    if (statusIndicator) {
        statusIndicator.classList.remove('speaking', 'silent');
        statusIndicator.classList.add('error');
    }

    if (statusText) {
        statusText.textContent = message || 'Lỗi audio';
        statusText.style.color = '#dc3545';
    }
}

// Test audio monitoring function
function testAudioMonitoring() {
    console.log('🧪 Testing audio monitoring...');

    if (!isMonitoringAudio) {
        showAlert('warning', 'Audio monitoring chưa được khởi tạo. Đang thử khởi động lại...');
        restartAudioMonitoring();
        return;
    }

    // Show current status
    const currentLevel = document.getElementById('audioLevelValue').textContent;
    const statusText = document.getElementById('audioStatusText').textContent;

    showAlert('info', `Audio monitoring đang hoạt động. Mức hiện tại: ${currentLevel}%. Trạng thái: ${statusText}`);

    // Log detailed info
    console.log('🧪 Audio monitoring test results:', {
        isMonitoringAudio,
        audioContext: audioContext ? audioContext.state : 'null',
        analyser: !!analyser,
        microphone: !!microphone,
        dataArray: !!dataArray,
        silenceThreshold,
        currentLevel
    });
}

// Cập nhật silence threshold
function updateSilenceThreshold(value) {
    silenceThreshold = parseInt(value);

    // Save to localStorage
    localStorage.setItem('silenceThreshold', silenceThreshold);

    // Update UI
    updateSilenceThresholdUI(silenceThreshold);

    // Send to server
    updateSilenceThresholdOnServer(silenceThreshold);

    console.log('🔧 Silence threshold updated to:', silenceThreshold);
}

// Cập nhật UI silence threshold
function updateSilenceThresholdUI(value) {
    const slider = document.getElementById('silenceThreshold');
    const valueDisplay = document.getElementById('silenceThresholdValue');
    const thresholdLine = document.getElementById('silenceThresholdLine');

    if (slider) {
        slider.value = value;
    }

    if (valueDisplay) {
        valueDisplay.textContent = value;
    }

    if (thresholdLine) {
        thresholdLine.style.left = value + '%';
    }
}

// Gửi silence threshold lên server
function updateSilenceThresholdOnServer(threshold) {
    fetch('/update_silence_threshold', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            threshold: threshold / 100 // Convert to 0-1 range
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // console.log('✅ Silence threshold updated on server');
        } else {
            console.error('❌ Failed to update silence threshold on server:', data.message);
        }
    })
    .catch(error => {
        console.error('❌ Error updating silence threshold on server:', error);
    });
}

// Restart audio monitoring (useful when settings change)
function restartAudioMonitoring() {
    console.log('🔄 Restarting audio monitoring...');
    stopAudioMonitoring();
    setTimeout(() => {
        startAudioMonitoring();
    }, 500);
}

// Handle click on audio bar to start monitoring
function handleAudioBarClick() {
    if (!isMonitoringAudio) {
        console.log('🎤 Audio bar clicked - starting monitoring...');
        startAudioMonitoring();
    } else {
        console.log('🎤 Audio monitoring already running');
        testAudioMonitoring();
    }
}

// ===== NEW SPEAKER MODE FUNCTIONS =====

let isNewSpeakerMode = false;
let originalSimilarityThreshold = 0.4; // Store original similarity threshold
let originalAutoProfileCreation = false; // Store original auto profile creation setting



function deactivateNewSpeakerMode() {
    const similarityInput = document.getElementById('similarity_threshold');
    const autoProfileCheckbox = document.getElementById('auto_profile_creation');
    const indicator = document.getElementById('newSpeakerIndicator');

    if (isNewSpeakerMode) {
        isNewSpeakerMode = false;

        // Restore original threshold
        similarityInput.value = originalSimilarityThreshold.toString();

        // Restore original auto profile creation setting (even if hidden from UI)
        if (autoProfileCheckbox) {
            autoProfileCheckbox.checked = originalAutoProfileCreation;
        }

        // Hide indicator
        if (indicator) {
            indicator.classList.remove('show');
        }

        // Update similarity threshold on server
        fetch('/update_setting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: 'similarity_threshold',
                value: originalSimilarityThreshold
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // console.log(`✅ Similarity threshold restored to ${originalSimilarityThreshold}`);
            } else {
                console.error('❌ Failed to restore similarity threshold:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ Error restoring similarity threshold:', error);
        });

        // Update auto profile creation on server
        fetch('/update_setting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: 'auto_profile_creation',
                value: originalAutoProfileCreation
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // console.log(`✅ Auto profile creation restored to ${originalAutoProfileCreation}`);
            } else {
                console.error('❌ Failed to restore auto profile creation:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ Error restoring auto profile creation:', error);
        });

        // Show notification
        const autoStatus = originalAutoProfileCreation ? 'Bật' : 'Tắt';
        showAlert('info', `Chế độ "Người mới" đã tắt - Ngưỡng: ${originalSimilarityThreshold}, Tự động tạo hồ sơ: ${autoStatus}`);

        console.log(`🔄 New speaker mode deactivated - Settings restored`);
    }
}

// Auto-deactivate new speaker mode when similarity threshold is manually changed
function onSimilarityThresholdChange(value) {
    if (isNewSpeakerMode && parseFloat(value) !== 0.7) {
        // User manually changed threshold, deactivate new speaker mode
        originalSimilarityThreshold = parseFloat(value);

        // Also deselect "Người mới" from speakers list
        selectedSpeakers.delete('Người mới');
        updateSpeakerSelectionUI();
        updateServerSpeakerSelection();

        deactivateNewSpeakerMode();

        console.log('🔄 New speaker mode auto-deactivated due to manual threshold change');
    } else if (!isNewSpeakerMode) {
        // Update original threshold when not in new speaker mode
        originalSimilarityThreshold = parseFloat(value);
    }
}

// Auto-deactivate new speaker mode when auto profile creation is manually changed
function onAutoProfileCreationChange(checked) {
    if (isNewSpeakerMode && !checked) {
        // User manually disabled auto profile creation, deactivate new speaker mode
        originalAutoProfileCreation = checked;

        // Also deselect "Người mới" from speakers list
        selectedSpeakers.delete('Người mới');
        updateSpeakerSelectionUI();
        updateServerSpeakerSelection();

        deactivateNewSpeakerMode();

        console.log('🔄 New speaker mode auto-deactivated due to manual auto profile creation change');
    } else if (!isNewSpeakerMode) {
        // Update original setting when not in new speaker mode
        originalAutoProfileCreation = checked;
    }
}

// Handle click on "Người mới" in speakers list
function toggleNewSpeakerProfile() {
    const speakerName = 'Người mới';
    const wasSelected = selectedSpeakers.has(speakerName);

    if (wasSelected) {
        // Deselect "Người mới" - restore original threshold
        selectedSpeakers.delete(speakerName);
        deactivateNewSpeakerMode();

        console.log('🔄 "Người mới" deselected - threshold restored');

        // Add visual feedback for deselection
        const speakerRow = document.querySelector('[data-speaker-name="Người mới"]');
        if (speakerRow) {
            speakerRow.style.transform = 'scale(0.98)';
            setTimeout(() => {
                speakerRow.style.transform = '';
            }, 150);
        }

    } else {
        // Select "Người mới" - activate new speaker mode
        selectedSpeakers.add(speakerName);
        activateNewSpeakerModeFromList();

        console.log('🆕 "Người mới" selected - new speaker mode activated');

        // Add visual feedback for selection
        const speakerRow = document.querySelector('[data-speaker-name="Người mới"]');
        if (speakerRow) {
            speakerRow.style.transform = 'scale(1.02)';
            setTimeout(() => {
                speakerRow.style.transform = '';
            }, 150);
        }
    }

    // Update UI
    updateSpeakerSelectionUI();
    updateServerSpeakerSelection();
}

// Activate new speaker mode when selected from speakers list
function activateNewSpeakerModeFromList() {
    const similarityInput = document.getElementById('similarity_threshold');
    const autoProfileCheckbox = document.getElementById('auto_profile_creation');
    const indicator = document.getElementById('newSpeakerIndicator');

    if (!isNewSpeakerMode) {
        isNewSpeakerMode = true;

        // Store original threshold and auto profile setting
        originalSimilarityThreshold = parseFloat(similarityInput.value);
        originalAutoProfileCreation = autoProfileCheckbox ? autoProfileCheckbox.checked : false;

        // Set threshold to 0.7
        similarityInput.value = '0.7';

        // Enable auto profile creation (even if hidden from UI)
        if (autoProfileCheckbox) {
            autoProfileCheckbox.checked = true;
        }

        // Show indicator
        if (indicator) {
            indicator.classList.add('show');
        }

        // Update similarity threshold on server
        fetch('/update_setting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: 'similarity_threshold',
                value: 0.7
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // console.log('✅ Similarity threshold updated to 0.7');
            } else {
                console.error('❌ Failed to update similarity threshold:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ Error updating similarity threshold:', error);
        });

        // Update auto profile creation on server
        fetch('/update_setting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: 'auto_profile_creation',
                value: true
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // console.log('✅ Auto profile creation enabled');
            } else {
                console.error('❌ Failed to enable auto profile creation:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ Error enabling auto profile creation:', error);
        });

        // Show notification
        showAlert('success', 'Đã bật "Chế độ người mới" - Ngưỡng: 0.7, Tự động tạo hồ sơ: Bật');

        console.log('🆕 New speaker mode activated - Auto profile creation enabled');
    }
}

// ===== DROPDOWN SELECTION PERSISTENCE =====

// Mark user interaction to pause restore
function markUserInteraction() {
    userInteracting = true;

    // Clear existing timeout
    if (interactionTimeout) {
        clearTimeout(interactionTimeout);
    }

    // Reset flag after 2 seconds of no interaction
    interactionTimeout = setTimeout(() => {
        userInteracting = false;
        console.log('🔓 User interaction timeout - restore enabled');
    }, 2000);

    console.log('🔒 User interaction detected - restore paused');
}

// Save all current dropdown selections
function saveAllDropdownSelections() {
    const dropdowns = document.querySelectorAll('.speaker-dropdown');
    savedDropdownSelections.clear();

    dropdowns.forEach(dropdown => {
        const index = dropdown.getAttribute('data-index');
        const value = dropdown.value;
        if (index !== null && value && value !== 'Người lạ') {
            savedDropdownSelections.set(parseInt(index), value);
        }
    });

    console.log('💾 Saved dropdown selections:', Object.fromEntries(savedDropdownSelections));
}

// Wait for dropdowns to be ready (have speaker options loaded)
function waitForDropdownsReady() {
    return new Promise((resolve) => {
        const checkReady = () => {
            const dropdowns = document.querySelectorAll('.speaker-dropdown');
            if (dropdowns.length === 0) {
                resolve(); // No dropdowns to check
                return;
            }

            // Check if at least one dropdown has speakers loaded
            let hasLoadedDropdown = false;
            dropdowns.forEach(dropdown => {
                const options = dropdown.querySelectorAll('option');
                if (options.length > 1) { // More than just "Người lạ"
                    hasLoadedDropdown = true;
                }
            });

            if (hasLoadedDropdown) {
                resolve();
            } else {
                setTimeout(checkReady, 100); // Check again in 100ms
            }
        };

        checkReady();
    });
}

// Restore dropdown selections after rebuild
function restoreAllDropdownSelections() {
    if (savedDropdownSelections.size === 0) {
        console.log('📂 No dropdown selections to restore');
        return;
    }

    // Skip restore if user is currently interacting
    if (userInteracting) {
        console.log('🔒 Skipping restore - user is interacting with dropdowns');
        return;
    }

    // Log speaker name mapping if exists
    if (speakerNameMapping.size > 0) {
        console.log('🔄 Speaker name mapping active during restore:');
        speakerNameMapping.forEach((newName, oldName) => {
            console.log(`   "${oldName}" -> "${newName}"`);
        });
    }

    // Debounce để tránh multiple calls
    if (restoreTimeout) {
        clearTimeout(restoreTimeout);
    }

    restoreTimeout = setTimeout(() => {
        // Double-check user interaction before proceeding
        if (userInteracting) {
            console.log('🔒 Aborting restore - user interaction detected during timeout');
            restoreTimeout = null;
            return;
        }

        console.log('🔄 Restoring dropdown selections:', Object.fromEntries(savedDropdownSelections));

        // Wait for speakers to be loaded in dropdowns
        waitForDropdownsReady().then(() => {
            let restoredCount = 0;
            let failedCount = 0;

            savedDropdownSelections.forEach((value, index) => {
                const dropdown = document.querySelector(`.speaker-dropdown[data-index="${index}"]`);
                if (dropdown) {
                    // Skip if this dropdown is currently focused (user is interacting)
                    if (document.activeElement === dropdown) {
                        console.log(`🔒 Skipping dropdown ${index} - currently focused`);
                        return;
                    }

                    // Check if dropdown has been populated with speakers
                    const options = dropdown.querySelectorAll('option');
                    if (options.length <= 1) {
                        // Only has "Người lạ" option, speakers not loaded yet
                        console.log(`⏳ Dropdown ${index} not ready yet, will retry...`);
                        failedCount++;
                        return;
                    }

                    // Apply speaker name mapping if exists
                    let valueToRestore = value;
                    if (speakerNameMapping.has(value)) {
                        valueToRestore = speakerNameMapping.get(value);
                        console.log(`🔄 Mapping "${value}" to "${valueToRestore}" for dropdown ${index}`);
                    }

                    // Check if the value exists in dropdown options
                    const option = dropdown.querySelector(`option[value="${valueToRestore}"]`);
                    if (option) {
                        dropdown.value = valueToRestore;
                        console.log(`✅ Restored dropdown ${index} to "${valueToRestore}"`);
                        restoredCount++;
                    } else {
                        console.log(`⚠️ Speaker "${valueToRestore}" not found in dropdown ${index}, keeping "Người lạ"`);
                        failedCount++;
                    }
                }
            });

            console.log(`📊 Restore summary: ${restoredCount} restored, ${failedCount} failed`);

            // If some failed, retry after speakers are loaded
            if (failedCount > 0 && restoredCount === 0) {
                console.log('🔄 All restores failed, will retry after speakers load...');
                setTimeout(() => restoreAllDropdownSelections(), 1000);
            }
        });

        restoreTimeout = null;
    }, 300); // Increased debounce delay
}

// Debug function to test merge logic
function debugMergeLogic() {
    console.log('🔍 === MERGE LOGIC DEBUG ===');
    console.log('mergeSameSpeaker:', mergeSameSpeaker);

    if (mergeSameSpeaker) {
        console.log('✅ Merge mode is ON');
        console.log('📋 "Người lạ" entries will NOT be merged (each treated as separate person)');
        console.log('📋 Other speakers with same name will be merged');
    } else {
        console.log('❌ Merge mode is OFF (chỉ áp dụng cho "Người lạ")');
        console.log('📋 "Người lạ" entries displayed separately');
        console.log('📋 Other speakers still merged normally');
    }

    // Count "Người lạ" entries
    const nguoiLaCount = speechResults.filter(result =>
        result.includes('[Người lạ]')
    ).length;
    console.log(`📊 Found ${nguoiLaCount} "Người lạ" entries`);

    console.log('🔍 === DEBUG COMPLETE ===');
}

// Expose dropdown functions globally
window.saveAllDropdownSelections = saveAllDropdownSelections;
window.restoreAllDropdownSelections = restoreAllDropdownSelections;
window.markUserInteraction = markUserInteraction;
window.debugMergeLogic = debugMergeLogic;
window.debugSpeakerNameMapping = debugSpeakerNameMapping;
window.debugSpeakerSelection = debugSpeakerSelection;
window.updateSpeakerSelectionIndicator = updateSpeakerSelectionIndicator;
window.autoSelectSpeaker = autoSelectSpeaker;

// ===== EXPORT FUNCTIONS =====

/**
 * Export speech recognition results to TXT format
 */
function exportToTxt() {
    console.log('🔄 Starting TXT export...');

    // Show loading indicator
    showAlert('info', 'Đang chuẩn bị file TXT...');

    // Use fetch to get the file and trigger download
    console.log('📡 Making fetch request to /export_txt');
    fetch('/export_txt')
        .then(response => {
            console.log('📥 Received response:', response);
            console.log('📊 Response status:', response.status);
            console.log('📋 Response headers:', [...response.headers.entries()]);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is JSON (error) or file
            const contentType = response.headers.get('Content-Type');
            console.log('📋 Content-Type:', contentType);

            if (contentType && contentType.includes('application/json')) {
                // This is an error response, not a file
                return response.json().then(errorData => {
                    throw new Error(errorData.message || 'Server returned error');
                });
            }

            // Get filename from Content-Disposition header
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'ket-qua-nhan-dang.txt';
            console.log('📄 Content-Disposition:', contentDisposition);

            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename=(.+)/);
                if (filenameMatch) {
                    filename = filenameMatch[1].replace(/"/g, '');
                }
            }
            console.log('📁 Using filename:', filename);

            console.log('🔄 Converting response to blob...');
            return response.blob().then(blob => {
                console.log('📦 Blob created:', blob);
                console.log('📏 Blob size:', blob.size, 'bytes');
                console.log('📋 Blob type:', blob.type);
                return { blob, filename };
            });
        })
        .then(({ blob, filename }) => {
            console.log('💾 Creating download link...');

            // Create download link
            const url = window.URL.createObjectURL(blob);
            console.log('🔗 Blob URL created:', url);

            const link = document.createElement('a');
            link.href = url;
            link.download = filename;

            console.log('🖱️ Triggering download...');
            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('🧹 Cleaning up blob URL...');
            // Clean up
            window.URL.revokeObjectURL(url);

            console.log('✅ TXT export completed successfully');
            showAlert('success', 'File TXT đã được tải xuống!');
        })
        .catch(error => {
            console.error('❌ Error exporting TXT:', error);
            console.error('❌ Error stack:', error.stack);
            showAlert('error', 'Lỗi khi xuất file TXT: ' + error.message);
        });
}

/**
 * Export speech recognition results to Excel format
 */
function exportToExcel() {
    console.log('🔄 Starting Excel export...');

    // Show loading indicator
    showAlert('info', 'Đang chuẩn bị file Excel...');

    // Use fetch to get the file and trigger download
    console.log('📡 Making fetch request to /export_excel');
    fetch('/export_excel')
        .then(response => {
            console.log('📥 Received response:', response);
            console.log('📊 Response status:', response.status);
            console.log('📋 Response headers:', [...response.headers.entries()]);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is JSON (error) or file
            const contentType = response.headers.get('Content-Type');
            console.log('📋 Content-Type:', contentType);

            if (contentType && contentType.includes('application/json')) {
                // This is an error response, not a file
                return response.json().then(errorData => {
                    throw new Error(errorData.message || 'Server returned error');
                });
            }

            // Get filename from Content-Disposition header
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'ket-qua-nhan-dang.xlsx';
            console.log('📄 Content-Disposition:', contentDisposition);

            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename=(.+)/);
                if (filenameMatch) {
                    filename = filenameMatch[1].replace(/"/g, '');
                }
            }
            console.log('📁 Using filename:', filename);

            console.log('🔄 Converting response to blob...');
            return response.blob().then(blob => {
                console.log('📦 Blob created:', blob);
                console.log('📏 Blob size:', blob.size, 'bytes');
                console.log('📋 Blob type:', blob.type);
                return { blob, filename };
            });
        })
        .then(({ blob, filename }) => {
            console.log('💾 Creating download link...');

            // Create download link
            const url = window.URL.createObjectURL(blob);
            console.log('🔗 Blob URL created:', url);

            const link = document.createElement('a');
            link.href = url;
            link.download = filename;

            console.log('🖱️ Triggering download...');
            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('🧹 Cleaning up blob URL...');
            // Clean up
            window.URL.revokeObjectURL(url);

            console.log('✅ Excel export completed successfully');
            showAlert('success', 'File Excel đã được tải xuống!');
        })
        .catch(error => {
            console.error('❌ Error exporting Excel:', error);
            console.error('❌ Error stack:', error.stack);
            showAlert('error', 'Lỗi khi xuất file Excel: ' + error.message);
        });
}

// Expose export functions globally
window.exportToTxt = exportToTxt;
window.exportToExcel = exportToExcel;

