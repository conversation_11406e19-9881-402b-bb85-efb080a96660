/* 
 * court-styles.css - CSS riêng cho trang biên bản tòa án
 * Phiên bản: 1.0.3
 * <PERSON><PERSON>u sắc: Đỏ-Trắng
 */

@page {
    size: A4;
    margin: 20mm 25mm;
}

:root {
    --primary-color: #e63946; /* Đỏ */
    --primary-light: #f8d7da; /* Đỏ nhạt */
    --primary-dark: #c1121f; /* Đỏ đậm */
    --light-color: #fff;
    --dark-color: #212529;
    --gray-color: #6c757d;
    --gray-light: #e9ecef;
    --gray-dark: #343a40;
    --border-radius: 4px;
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Times New Roman', serif;
    font-size: 14pt;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
    color: #000;
}

.document-container {
    width: 210mm;
    min-height: 297mm;
    padding: 20mm 25mm;
    margin: 20px auto;
    background-color: white;
    box-shadow: var(--box-shadow);
    position: relative;
}

.document-header {
    text-align: center;
    margin-bottom: 20px;
}

.document-title {
    font-weight: bold;
    font-size: 16pt;
    text-align: center;
    margin: 20px 0;
}

.header-left, .header-right {
    display: inline-block;
    width: 48%;
    vertical-align: top;
}

.header-left {
    text-align: left;
}

.header-right {
    text-align: center;
}

.document-content {
    margin-top: 20px;
}

.section {
    margin-bottom: 15px;
}

.section-title {
    font-weight: bold;
    margin-bottom: 10px;
}

.editable {
    border: 1px dashed #ccc;
    padding: 5px;
    min-height: 20px;
    position: relative;
    cursor: text;
}

.editable:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.editable:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: #fff;
}

.editable-placeholder {
    color: #999;
    font-style: italic;
}

.editable.active {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

/* CSS cho phần editable inline */
.section span.editable {
    display: inline !important;
    white-space: nowrap !important;
}

/* CSS riêng cho phần ngày tháng năm */
.date-time-section span.editable {
    display: inline !important;
    white-space: nowrap !important;
}

/* Định dạng cho nội dung lời nói khi chèn vào biên bản */
.speaker-content {
    margin-bottom: 12px;
    display: block;
}

.speaker-name {
    font-weight: bold;
}

.speaker-time {
    font-style: italic;
    color: #555;
    font-size: 0.9em;
}

.speaker-text {
    display: block;
    margin-left: 20px;
    margin-top: 4px;
}

.signatures {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
}

.signature {
    text-align: center;
    width: 45%;
}

.signature-title {
    font-weight: bold;
    margin-bottom: 60px;
}

.toolbar {
    position: fixed;
    top: 10px;
    right: 10px;
    background-color: white;
    padding: 10px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 1000;
}

.toolbar button {
    padding: 8px 12px;
    margin: 0 5px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.toolbar button:hover {
    background-color: var(--primary-dark);
}

.toolbar .dropdown {
    display: inline-block;
    position: relative;
}

.toolbar .dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
    border-radius: var(--border-radius);
    right: 0;
}

.toolbar .dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    border-radius: var(--border-radius);
}

.toolbar .dropdown-content a:hover {
    background-color: var(--gray-light);
    color: var(--primary-color);
}

.toolbar .dropdown:hover .dropdown-content {
    display: block;
}

@media print {
    body {
        background-color: white;
        margin: 0;
        padding: 0;
    }
    
    /* Điều chỉnh container chính để phù hợp với kích thước trang A4 */
    .document-container {
        box-shadow: none;
        margin: 0 !important;
        padding: 10mm !important; /* Giảm padding để tiết kiệm không gian */
        width: auto !important;
        min-height: auto !important;
        height: auto !important;
    }
    
    /* Giảm margin cho các phần tử */
    .document-header, .document-title, .document-content, .section {
        margin: 0 0 10px 0 !important;
    }
    
    /* Tối ưu khoảng cách giữa các section */
    .section {
        margin-bottom: 8px !important;
        padding: 0 !important;
    }
    
    /* Điều chỉnh font size và line height để tối ưu không gian */
    body, .document-content {
        font-size: 11pt !important;
        line-height: 1.3 !important;
    }
    
    /* Tiêu đề có thể nhỏ hơn một chút */
    .document-title {
        font-size: 14pt !important;
        margin: 10px 0 !important;
    }
    
    /* Ẩn tất cả các phần không cần thiết khi in */
    .toolbar, 
    .speech-editor, 
    #speechEditor,
    #settingsToggle,
    #settingsModal,
    .modal,
    .minimize-button,
    .result-card button,
    .empty-state {
        display: none !important;
    }
    
    /* Đảm bảo các phần có thể chỉnh sửa không hiển thị như placeholders */
    .editable {
        border: none !important;
        padding: 0 !important;
        background-color: transparent !important;
        min-height: 0 !important;
    }
    
    .editable.editable-placeholder {
        display: none !important;
    }
    
    .editable.filled {
        display: block !important;
    }
    
    /* Đảm bảo các phần tử inline hiển thị đúng */
    span.editable.filled, span.editable.inline-edit {
        display: inline !important;
        white-space: nowrap !important;
    }
    
    /* Đảm bảo các phần editable trong date-time-section hiển thị đúng */
    .date-time-section span.editable.filled {
        display: inline !important;
        white-space: nowrap !important;
    }
    
    /* Ngăn ngừa page-break trong dòng thời gian */
    .date-time-section {
        page-break-inside: avoid !important;
        white-space: nowrap !important;
        width: 100% !important;
    }
    
    /* Định dạng nội dung người nói khi in */
    .speaker-content {
        margin-bottom: 6px !important;
    }
    
    .speaker-text {
        margin-left: 10px !important;
        margin-top: 2px !important;
    }
    
    /* Tối ưu khoảng cách cho các tiêu đề section */
    .section-title {
        margin: 8px 0 4px 0 !important;
        font-weight: bold !important;
    }
    
    /* Điều chỉnh phần chữ ký */
    .signatures {
        page-break-inside: avoid;
        margin-top: 15px !important;
    }
    
    .signature-title {
        margin-bottom: 40px !important; /* Giảm khoảng cách chữ ký */
    }
    
    /* Kiểm soát page break */
    .document-header, .document-title {
        page-break-after: avoid;
    }
    
    /* Phần này thường rất dài nên cho phép ngắt trang nếu cần */
    #questioning, #debate, #prosecutor_statement {
        page-break-inside: auto;
    }
    
    /* Điều chỉnh các phần tử inline để tiết kiệm không gian */
    span.editable {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* Loại bỏ khoảng trắng dư thừa */
    * {
        text-indent: 0 !important;
    }
}

/* Phần soạn thảo */
.speech-editor {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #f8f9fa;
    padding: 10px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    z-index: 1000;
}

.speech-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.speech-editor-title {
    font-weight: bold;
    color: var(--primary-color);
}

.speech-editor-content {
    display: flex;
    height: 200px;
}

.speech-results {
    flex: 2;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background-color: white;
    margin-right: 10px;
}

.speech-actions {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.speech-actions button {
    margin-bottom: 10px;
    padding: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.speech-actions button:hover {
    background-color: var(--primary-dark);
}

.minimize-button {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.result-card {
    background-color: var(--primary-light);
    border-radius: var(--border-radius);
    padding: 10px;
    margin-bottom: 10px;
    border-left: 3px solid var(--primary-color);
}

.speaker-tag {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: var(--border-radius);
    margin-right: 5px;
    font-size: 12px;
}

.result-text {
    margin-top: 5px;
}

.timestamp {
    text-align: right;
    font-size: 0.8em;
    color: var(--gray-color);
    margin-top: 5px;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
}

.modal-content {
    position: relative;
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    width: 50%;
    border-radius: var(--border-radius);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.modal-title {
    font-weight: bold;
    font-size: 18px;
    color: var(--primary-color);
}

.modal-close {
    cursor: pointer;
    font-size: 20px;
}

.modal-body {
    margin-bottom: 15px;
}

.modal-footer {
    text-align: right;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Các nút trong modal */
.modal-footer button {
    padding: 8px 15px;
    margin-left: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.modal-footer button:hover {
    background-color: var(--primary-dark);
}

@media (max-width: 768px) {
    .modal-content {
        width: 90%;
    }
    
    .speech-editor-content {
        flex-direction: column;
        height: auto;
    }
    
    .speech-results {
        margin-right: 0;
        margin-bottom: 10px;
        height: 200px;
    }
}
.progress-bar {
    height: 4px;
    background-color: #f1f1f1;
    border-radius: 2px;
    margin: 10px 0;
    overflow: hidden;
}

.progress-fill {
    width: 0%;
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 2px;
    transition: width 0.3s;
}
/* 
 * Bổ sung CSS cho các nút undo, redo và trạng thái lưu tạm
 * Thêm vào court-styles.css
 */

/* Thanh công cụ mở rộng với các tính năng mới */
.toolbar-group {
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
    border-right: 1px solid #ddd;
    padding-right: 10px;
}

.toolbar-group:last-child {
    border-right: none;
}

/* Nút được vô hiệu hóa */
.toolbar button.disabled,
.toolbar .dropdown-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Thanh trạng thái */
.status-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #666;
    z-index: 900;
}

.status-bar-item {
    display: flex;
    align-items: center;
}

.status-bar-item i {
    margin-right: 5px;
}

.save-status {
    display: flex;
    align-items: center;
}

.save-status .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.save-status .dot.saved {
    background-color: #28a745;
}

.save-status .dot.pending {
    background-color: #ffc107;
}

/* Hiệu ứng khi lưu tạm */
.autosave-flash {
    animation: autosaveFlash 1s ease-in-out;
}

@keyframes autosaveFlash {
    0% { background-color: rgba(40, 167, 69, 0); }
    50% { background-color: rgba(40, 167, 69, 0.2); }
    100% { background-color: rgba(40, 167, 69, 0); }
}

/* Hiệu ứng khi Undo/Redo */
.undo-redo-flash {
    animation: undoRedoFlash 0.7s ease-in-out;
}

@keyframes undoRedoFlash {
    0% { background-color: rgba(0, 123, 255, 0); }
    50% { background-color: rgba(0, 123, 255, 0.1); }
    100% { background-color: rgba(0, 123, 255, 0); }
}

/* Thanh lịch sử */
.history-panel {
    position: fixed;
    right: -350px;
    top: 80px;
    width: 350px;
    height: calc(100vh - 150px);
    background-color: white;
    border-left: 1px solid #ddd;
    border-top: 1px solid #ddd;
    box-shadow: -2px 2px 10px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 950;
    display: flex;
    flex-direction: column;
}

.history-panel.open {
    right: 0;
}

.history-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    background-color: #f8f9fa;
}

.history-panel-title {
    font-weight: bold;
    font-size: 1rem;
}

.history-panel-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
}

.history-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.history-item {
    padding: 8px 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    display: flex;
    flex-direction: column;
}

.history-item:hover {
    background-color: #f5f5f5;
}

.history-item.active {
    background-color: var(--primary-light);
    border-left: 3px solid var(--primary-color);
}

.history-item-description {
    font-weight: bold;
    margin-bottom: 3px;
}

.history-item-time {
    font-size: 0.8rem;
    color: #777;
}

/* Điều chỉnh cho bố cục khi thanh trạng thái hiển thị */
.speech-editor {
    bottom: 30px; /* Tăng lên để không đè lên thanh trạng thái */
}

/* Responsive */
@media (max-width: 768px) {
    .toolbar-group {
        margin-right: 5px;
        padding-right: 5px;
    }
    
    .toolbar button, 
    .toolbar .dropdown-btn {
        padding: 6px 8px;
        font-size: 0.85rem;
    }
    
    .toolbar button i,
    .toolbar .dropdown-btn i {
        margin-right: 0;
    }
    
    .toolbar button span,
    .toolbar .dropdown-btn span {
        display: none;
    }
    
    .history-panel {
        width: 280px;
    }
}
/* CSS bổ sung cho việc in và xuất file 
   Đặt đoạn mã này trong file court-styles.css */

/* Đảm bảo ẩn thanh trạng thái, toolbar và các UI khác khi in */
@media print {
    .toolbar,
    .status-bar,
    .speech-editor,
    .history-panel,
    #alertsContainer,
    .alert,
    .modal,
    .dropdown-content,
    .processing-modal {
        display: none !important;
    }
    
    /* Đảm bảo nội dung document hiển thị tối ưu cho in */
    .document-container {
        margin: 0;
        padding: 0;
        box-shadow: none;
        width: 100%;
        border: none;
    }
    
    /* Điều chỉnh các phần tử để in tốt hơn */
    .document-header {
        padding-top: 0;
    }
    
    /* Đảm bảo tất cả content hiển thị đầy đủ */
    @page {
        size: A4;
        margin: 2cm;
    }
    
    body {
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.5;
        background: white;
        color: black;
    }
    
    /* Xử lý page-break */
    .document-content .section {
        page-break-inside: avoid;
    }
    
    .signatures {
        page-break-inside: avoid;
    }
    
    /* Ẩn URL trong footer trang in */
    @page {
        margin-bottom: 2cm;
        
        /* Ẩn URL và tiêu đề trang khi in */
        @top-left {
            content: "";
        }
        
        @top-center {
            content: "";
        }
        
        @top-right {
            content: "";
        }
        
        @bottom-center {
            content: "";
        }
        
        @bottom-left {
            content: "";
        }
        
        @bottom-right {
            content: "";
        }
    }
}

/* Ẩn URL khi in */
@media print {
    html::after, html::before {
        content: none !important;
    }
    
    body::after, body::before {
        content: none !important;
    }
    
    /* Ẩn header trang */
    @page:first {
        @top-left {
            content: "";
        }
        @top-center {
            content: "";
        }
        @top-right {
            content: "";
        }
    }
}
/* CSS cho các tiêu đề có thể chỉnh sửa */
.section-title.editable:focus,
.section-title.editable:active {
    outline: 1px dashed #ccc;
    background-color: #f9f9f9;
}

/* Định dạng cho các tiêu đề phần có thể chỉnh sửa */
prosecutor_heading, 
questioning_heading, 
debate_heading, 
defendant_heading {
    font-weight: bold;
    margin-top: 10px;
    margin-bottom: 5px;
}

/* Định dạng cho các phần text có thể chỉnh sửa */
deliberation_text, 
verdict_text {
    font-style: italic;
    margin: 15px 0;
}
.merge-speaker-option {
    margin-top: 15px;
    border-top: 1px solid #ddd;
    padding-top: 15px;
}

.merge-speaker-checkbox {
    display: flex;
    align-items: center;
}

.merge-speaker-checkbox input[type="checkbox"] {
    margin-right: 10px;
}

.merge-speaker-checkbox label {
    cursor: pointer;
    user-select: none;
}

/* Cải tiến hiển thị phần lời nói đã gộp */
.speaker-content {
    background-color: rgba(230, 57, 70, 0.05);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 15px;
    border-left: 3px solid var(--primary-color);
}

.speaker-content:hover {
    background-color: rgba(230, 57, 70, 0.1);
}

.speaker-name {
    font-weight: bold;
    color: var(--primary-color);
}

.speaker-time {
    font-style: italic;
    color: #777;
    font-size: 0.9em;
}

.speaker-text {
    margin-top: 5px;
    line-height: 1.5;
}

/* Hiệu ứng khi gộp lời nói */
.merge-animation {
    animation: mergeFade 1s ease-in-out;
}

@keyframes mergeFade {
    0% { background-color: rgba(230, 57, 70, 0.2); }
    100% { background-color: rgba(230, 57, 70, 0.05); }
}

/* Tính năng gộp người nói */
.form-group label input[type="checkbox"]#mergeSpeakers + * {
    position: relative;
}

.form-group label input[type="checkbox"]#mergeSpeakers:checked + *::after {
    content: " (Đã bật)";
    font-size: 0.8em;
    color: #4caf50;
    font-style: italic;
}

.form-group label input[type="checkbox"]#mergeSpeakers + *::after {
    content: " (Đã tắt)";
    font-size: 0.8em;
    color: #999;
    font-style: italic;
}